# 需求 [fix_tmp_index]

## 反馈

1. Fred提出以下需求
   1. 使用GPT自动翻译菜单和显示的文字。注意context的使用
   2. 使用AI增加自动翻译功能
   3. Comments的过滤现在使用Aliyun，也可以考虑一起改为用LLM
   4. Forum与Comments使用LLM翻译

## 需求提出人:    Fred

## 修改人：       Luo xiaowei

## 提出日期:      2025-06-20

## 1. 项目背景与目标

### 1.1 背景

需在现有 `i18n` 翻译系统和 `TranslatorManager` 的基础上，引入大型语言模型（LLM）进行自动化翻译，覆盖界面固定文本和Forum动态内容, 使用GPT自动翻译菜单和显示的文字。

### 1.2 核心目标

* **自动化**: 使用LLM自动翻译`i18n`数据库中缺失的翻译内容。
* **高质量**: 建立翻译质量评估与人工校对的闭环流程，确保翻译内容准确、符合上下文。
* **可扩展**: 构建统一的翻译后端，支持对Forum等动态内容的按需翻译。
* **智能化**: 能够根据不同的翻译场景（如UI文本 vs Forum长文）采用不同的翻译模型和策略。

## 2. 现有系统分析

### 2.1 i18n翻译系统

一个依赖内存缓存和MongoDB数据库的经典翻译流程，核心是等待人工通过后台工具填写缺失的翻译。

**流程图:**

```mermaid
graph TD
    A["用户请求"] --> B["获取用户语言设置"]
    B --> C["调用i18n.getFun获取翻译函数"]
    C --> D["检查内存缓存"]
    D -->|"缓存命中"| E["返回缓存中的翻译"]
    D -->|"缓存未命中"| F["执行过滤逻辑<br/>i18nRegexArray"]
    F -->|"不通过"| G["直接返回原文"]
    F -->|"通过"| H["在数据库i18n中<br/>创建待翻译记录"]
    H --> G
    E --> I["最终返回翻译结果"]
    G --> I

    %% reload函数调用逻辑
    J["管理员更新翻译"] --> K["调用i18n.reload(key, callback)"]
    L["系统初始化"] --> M["调用i18n.reload(callback)"]
    N["AJAX刷新请求"] --> O["调用i18n.reload(callback)"]

    K --> P{是否指定特定key?}
    M --> Q["重新加载所有翻译"]
    O --> Q

    P -->|"是"| R["从数据库查询单个记录<br/>Coll.findOne({_id:key})"]
    P -->|"否"| Q

    R --> S{记录是否存在?}
    S -->|"是"| T["更新缓存中的该记录<br/>cache[key] = record"]
    S -->|"否"| U["从缓存中删除该记录<br/>delete cache[key]"]

    Q --> V["从数据库查询所有记录<br/>Coll.findToArray({})"]
    V --> W["清空并重建整个缓存<br/>cache = {}"]
    W --> X["遍历所有记录更新缓存<br/>cache[r._id] = r"]

    T --> Y["执行回调函数cb()"]
    U --> Y
    X --> Y

    Y --> Z["缓存更新完成"]
```

* **过滤机制**: 系统拥有严格的过滤规则，会跳过对纯数字、URL、Email、错误信息等内容的翻译请求。

### 2.2 LLM翻译系统

一个管理多个LLM服务（Gemini, OpenAI等）的管理器，具备优先级调度、资源排队、失败降级和批量处理能力，主要用于动态内容。

**流程图:**

```mermaid
graph TD
    A[外部翻译请求] --> B[TranslatorManager.translate]
    B --> C[按优先级获取可用翻译器]
    C --> D{是否有可用翻译器?}
    D -->|否| E[加入等待队列]
    E --> F[等待其他翻译完成释放资源]
    F --> C
    D -->|是| G[使用当前翻译器进行翻译]
    G --> H{翻译是否成功且结果初步有效?}
    H -->|否| I{是否还有下一个备用翻译器?}
    I -->|是| J[切换到下一个翻译器]
    J --> G
    I -->|否| K[返回翻译失败错误]
    H -->|是| L[返回翻译结果和使用的服务]
```

## 3. 整体解决方案设计

### 3.1 核心架构

新方案将作为一个增强模块，与现有系统协同工作。它由一个定时任务调度器触发，调用新增的翻译、评估、更新等一系列服务，并引入prompt模板管理系统来支持不同场景的翻译需求。同时扩展现有的TranslatorManager，新增评论过滤功能来实时处理Forum评论内容。

```mermaid
graph TB
    subgraph "新增i18n LLM翻译系统"
        A[定时任务调度器] --> B[i18n缺失翻译检测器]
        B --> C[翻译内容过滤器]
        C --> D[Prompt模板选择器]
        D --> E[LLM翻译调用器]
        E --> F[翻译质量评估器]
        F --> G[数据库更新器]
    end

    subgraph "新增Forum评论过滤系统"
        H[Forum评论提交] --> I[评论内容预处理]
        I --> J[Prompt模板选择器]
        J --> K[LLM评论过滤器]
        K --> L{过滤结果}
        L -->|通过| M[保存评论]
        L -->|不通过| N[返回拒绝原因]
    end

    subgraph "现有系统"
        O[lib/i18n.coffee]
        P[lib/translator/]
        Q[MongoDB i18n Collection]
        R[Forum评论API]
    end

    subgraph "新增Prompt模板系统"
        S[MongoDB prompts Collection]
        T[模板管理API]
        U[模板版本控制]
    end

    A -.->|每10-30分钟| B
    B -.->|查询| Q
    C -.->|复用过滤规则| O
    D -.->|查询模板| S
    E -.->|调用翻译服务| P
    G -.->|更新翻译结果| Q
    
    H -.->|集成到| R
    J -.->|查询过滤模板| S
    K -.->|扩展TranslatorManager| P
    
    T -.->|管理模板| S
```

### 3.2 数据库结构变更

#### 3.2.1 新增prompts模板集合

| 字段名          | 字段类型       | 必填 | 字段含义       | 示例值                                      | 备注                                          |
|----------------|---------------|-----|---------------|--------------------------------------------|----------------------------------------------|
| `_id`          | String        | 必填 | 业务唯一标识符  | `"ui_menu_gpt4_v1"` | 用于版本管理和业务关联，格式：场景_用途_模型_版本    |
| `nm`           | String        | 必填 | 模板显示名称    | `"UI菜单翻译模板"` | 用于管理界面显示和用户识别                       |
| `desc`         | String        | 可选 | 模板详细描述    | `"专用于网页界面菜单项的翻译"` | 说明模板的用途和特点                             |
| `ver`          | Integer       | 必填 | 版本号         | `1` , `2` , `3` | 数字版本便于排序和比较，支持版本演进               |
| `status`       | String        | 必填 | 模板状态       | `active` , `draft` , `deprecated` | 枚举值                                        |
| `scenario`     | String        | 必填 | 主要应用场景    | `"ui_translation"` | 枚举值                                        |
| `tags`         | Array[String] | 可选 | 灵活标签数组    | `["ui", "menu", "short_text"]` | 支持跨场景的灵活分类和搜索                       |
| `m_cfg`        | Object        | 必填 | AI模型配置信息  |                                            | 聚合模型相关的所有配置                           |
| `m_cfg.m_nm`   | String        | 必填 | AI模型名称     | `gpt` 、 `claude` 、 `deepseek` 、 `gemini` | 支持的模型                                     |
| `m_cfg.params` | Object        | 可选 | 模型参数配置    | `{"temperature": 0.3, "max_tokens": 100}` | 包含temperature、max_tokens、top_p等模型特定参数 |
| `tpl`          | Object        | 必填 | 模板内容聚合    |                                             | 包含所有模板相关内容                            |
| `tpl.main`     | String        | 必填 | 主要提示模板内容 | `"请将以下英文UI术语翻译为{target_language}..."` | 核心的提示词模板，支持变量替换                   |
| `tpl.sys`      | String        | 可选 | 系统提示词      | `"你是一个专业的UI界面翻译专家..."` | 用于设定AI的角色和行为准则                       |
| `vars`         | Array[Object] | 可选 | 模板变量定义数组 |                                             | 定义模板中可替换的变量                          |
| `vars[].nm`    | String        | 必填 | 变量名称        | `"text"` , `"target_language"` | 在模板中使用{变量名}格式引用                     |
| `vars[].tp`    | String        | 必填 | 变量数据类型     | `"string"` , `"number"` , `"array"` | 用于参数验证和类型检查                          |
| `vars[].req`   | Boolean       | 必填 | 是否为必填变量   | `true` , `false` | 控制变量的必填性验证                            |
| `vars[].desc`  | String        | 可选 | 变量说明        | `"需要翻译的UI文本"` | 帮助开发者理解变量用途                          |
| `ts`           | ISODate       | 必填 | 创建时间        | `ISODate("2025-06-23T09:30:00.000Z")` | 模板首次创建的时间戳                            |
| `_mt`          | ISODate       | 必填 | 最后更新时间     | `ISODate("2025-06-23T10:00:00.000Z")` | 模板最后一次修改的时间戳                        |

## 4. 分阶段实施方案

### **阶段一：基础集成与批量翻译、评论过滤**

**目标**: 快速打通核心流程，实现i18n缺失翻译的自动化填充，同时建立Forum评论的实时过滤机制。

1. **创建定时任务**: 实现一个后台定时任务，每10-30分钟触发一次批量翻译流程。
2. **建立Prompt模板系统**:
    * 创建`prompts`集合，存储不同场景的翻译模板。
    * 实现模板管理API，支持模板的增删改查和版本控制。
    * 预设基础模板：
        * **UI翻译模板**: 针对界面文本的简洁翻译
        * **Forum内容模板**: 针对论坛动态内容的自然翻译
        * **通用模板**: 作为默认备选方案
3. **实现翻译逻辑**:
    * 任务从`i18n`库中查询各语言（如 `kr`, `zh-cn`）字段缺失的记录。
    * 复用现有`i18n.coffee`的过滤逻辑，排除无需翻译的内容。
    * 根据文本特征智能选择合适的prompt模板：
        * 短文本（<50字符）→ UI翻译模板
        * 长文本（>50字符）→ 通用翻译模板
    * 使用选定的模板构建完整的prompt，调用`TranslatorManager`进行翻译。
4. **扩展TranslatorManager实现评论过滤**:
    * 在现有`TranslatorManager`中新增评论过滤方法。
    * 集成到Forum评论提交API，实现用户提交评论时的实时过滤。
    * 过滤逻辑：
        * 根据评论过滤模板构建prompt
        * 调用AI模型进行内容审核
        * 返回过滤结果和具体原因
        * 失败时默认不通过审核
    * 过滤不通过时，向用户返回具体的拒绝原因。
5. **保存结果**:
    * 将LLM返回的翻译结果直接更新回数据库。
    * 在`translation_meta`中记录：
        * 状态为`auto`
        * 使用的模型名称
        * 使用的prompt模板ID
        * 初始置信度分数（可设为1.0）

### **阶段二：质量控制与人工校对**

**目标**: 建立翻译质量的自动评估与人工审核闭环，提升翻译可靠性。

1. **实现质量评估系统**:
    * 在LLM返回结果后、存入数据库前，执行评估。
    * 评估算法采用加权综合评分：
        * **关键词保留 (权重: 0.4)**: 检查数字、专有名词、品牌名等是否在译文中保留。
        * **语义相似度 (权重: 0.4)**: 可通过计算编辑距离或使用embedding向量的余弦相似度来实现。
        * **格式与长度 (权重: 0.2)**: 检查译文与原文的长度比例、标点符号等是否合理。
2. **应用质量阈值**:
    * 设定一个置信度阈值（如 `0.75`）。
    * 评估分数若高于阈值，状态记为 `auto`。
    * 若低于阈值，状态记为 `pending_review`。
3. **开发校对工具API**:
    * 创建管理后台所需的API：
        * `GET /api/translations?status=pending_review`: 获取所有待校对的翻译列表。
        * `POST /api/translations/verify`: 管理员提交修改后的翻译，API将其保存，并将状态更新为`verified`。
    * 新增prompt模板管理API：
        * `GET /api/prompt-templates`: 获取所有可用的prompt模板。
        * `POST /api/prompt-templates`: 创建新的prompt模板。
        * `PUT /api/prompt-templates/:id`: 更新现有模板。
        * `POST /api/prompt-templates/:id/test`: 测试模板效果。
4. **校对规则与模板优化**:
    * 根据确认，所有被标记为`pending_review`的条目都需要进行人工校对。
    * 收集校对反馈，分析不同模板的翻译质量表现。
    * 根据质量数据优化prompt模板，提升翻译准确性。

### **阶段三：高级功能与优化**

**目标**: 将翻译能力扩展到动态内容，并实现智能化策略和系统监控。

1. **Forum内容翻译中间件**:
    * 为Forum的API实现一个响应中间件（Response Middleware）。
    * 该中间件会根据用户的语言偏好，拦截API响应，并对其中的文本字段（如帖子标题、内容）调用统一翻译服务。
    * 针对Forum内容创建专门的prompt模板，支持上下文感知翻译
2. **智能模板选择系统**:
    * 实现基于内容特征的自动模板选择算法：
        * **文本长度**: 短文本使用UI模板，长文本使用内容模板
        * **内容类型**: 根据关键词识别房产、论坛、新闻等不同场景
        * **语言对**: 不同语言对使用优化的专门模板
    * 支持A/B测试，比较不同模板的翻译效果。
3. **自适应翻译策略**:
    * 根据场景和模板配置选择最适合的AI模型：
        * **i18n批量翻译**: 使用成本效益高的模型，配合通用模板
        * **Forum实时翻译**: 使用高性能模型，配合上下文感知模板
        * **房产内容翻译**: 使用专业模板，保留专业术语准确性
4. **模板版本管理与优化**:
    * 实现模板版本控制，支持模板的迭代优化。
    * 基于翻译质量反馈自动调整模板参数。
    * 支持模板的灰度发布和回滚机制。
5. **集成监控与报告系统**:
    * 监控翻译服务的性能、成本、成功率和质量评分分布。
    * 按模板统计翻译质量和使用频率。
    * 生成定期报告，为优化Prompt模板、调整模型选择和评估翻译系统ROI提供数据支持。
    * 实现异常检测，当某个模板的翻译质量下降时及时告警。

## 5. 完整翻译流程图

### 5.1 基于Prompt模板的LLM翻译与评论过滤流程

```mermaid
graph TD
    subgraph "i18n批量翻译流程"
        A[定时任务启动] --> B[查询i18n缺失翻译]
        B --> C{是否有待翻译内容?}
        C -->|否| D[任务结束]
        C -->|是| E[应用i18n过滤规则]
        E --> F{内容是否通过过滤?}
        F -->|否| G[跳过翻译]
        F -->|是| H[分析文本特征]

        H --> I[智能选择翻译Prompt模板]
        I --> J[从prompt_templates集合<br/>获取翻译模板配置]
        J --> K[构建完整翻译Prompt]
        K --> L[根据模板配置选择AI模型]
        L --> M[调用TranslatorManager翻译]

        M --> N{翻译是否成功?}
        N -->|否| O[记录错误日志]
        N -->|是| P[执行质量评估]
        P --> Q{质量分数是否达标?}
        Q -->|否| R[标记为pending_review]
        Q -->|是| S[标记为auto]

        R --> T[更新i18n集合<br/>含translation_meta]
        S --> T
        T --> U[更新统计数据]
        U --> V{是否还有更多内容?}
        V -->|是| E
        V -->|否| W[生成翻译报告]

        G --> V
        O --> V
        W --> D
    end

    subgraph "Forum评论实时过滤流程"
        AA[用户提交评论] --> BB[评论内容预处理]
        BB --> CC[选择评论过滤Prompt模板]
        CC --> DD[从prompt_templates集合<br/>获取过滤模板配置]
        DD --> EE[构建完整过滤Prompt]
        EE --> FF[根据模板配置选择AI模型]
        FF --> GG[调用TranslatorManager过滤]

        GG --> HH{过滤调用是否成功?}
        HH -->|否| II[默认拒绝评论]
        HH -->|是| JJ{评论是否通过过滤?}
        JJ -->|是| KK[保存评论到数据库]
        JJ -->|否| LL[返回拒绝原因给用户]

        II --> MM[记录过滤日志]
        LL --> MM
        KK --> NN[评论发布成功]
    end

    %% 人工校对流程
    XX[管理员校对] --> YY[获取pending_review列表]
    YY --> ZZ[修改翻译内容]
    ZZ --> AAA[更新为verified状态]
    AAA --> BBB[收集质量反馈]
    BBB --> CCC[优化Prompt模板]
```

## 影响范围

## 是否需要补充UT

1. 需要补充与修复UT

## 确认日期:    2025-06

## online-step

1. 执行batch -> src/batch/prompts/import_llm_templates.coffee,添加LLM的prompts模板
```shell
./start.sh -n import_llm_templates -cmd "lib/batchBase.coffee batch/prompts/import_llm_templates.coffee --category all
```
2. 执行batch -> src/batch/forum/translate_forum.coffee,翻译forum中的记录
```shell
./start.sh -n translate_forum -cmd "lib/batchBase.coffee batch/forum/translate_forum.coffee preload-model dryrun -d [YYYY-MM-DD] -l [Number]"
```
3. 先执行batch -> src/batch/i18n/auto_translate_i18n.coffee,对韩语进行重新翻译, 然后cornjob定期翻译i18n中未翻译的内容
```shell
未翻译内容翻译以及韩语重新翻译:
./start.sh -n auto_translate_i18n -cmd "lib/batchBase.coffee batch/i18n/auto_translate_i18n.coffee --languages zh,zh-cn,kr forceKr dryrun

corn定期翻译i18n中未翻译的数据:
./start.sh -n auto_translate_i18n -cmd "lib/batchBase.coffee batch/i18n/auto_translate_i18n.coffee --languages zh,zh-cn,kr dryrun
```
4. 重启server