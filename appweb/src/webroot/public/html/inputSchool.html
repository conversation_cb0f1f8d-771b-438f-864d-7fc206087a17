<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<head>
  <link rel="stylesheet" href="/css/bootstrap.3.3.6.min.css">
  <link rel="stylesheet" href="/css/materialize.min.css">
  <style>
      .mapContainer {
        height: 450px;
        margin-top: 5px;
      }
      #map {
        height: 100%;
      }
      .row {
        margin-top: 2px;
        margin-left: 2px;
      }
      .header {
        display: flex;
        align-items: left;
        gap: 2px;
      }
      label {
        margin-right: 10px;
      }
      #appEdit {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
      }
      .dataRow {
        display: flex;
        justify-content: space-between;
        margin-top: 5px;
      }
      .dataRow .value {
        color: #333;
        font-weight: 500;
      }
      .tabs {
        overflow-x: auto;
        white-space: nowrap;
        margin-top: 60px;
        margin-bottom: 18px;
      }
      .tabs .tab {
        text-transform: capitalize;
        display: inline-block;
        white-space: nowrap;
        font-size: 12px;
        vertical-align: middle;
        background-color: #f5f5f5;
        color: #999999;
        border-radius: 2px;
        padding: 6px 5px;
        margin-right: 10px;
        max-width: 140px;
        overflow: hidden;
        text-overflow: ellipsis;
        position: relative;
        transition: 0.3s;
      }
      .tabs .tab.active {
        background-color: #e9f9f4;
        border: 0.5px solid #40bc93;
        color: #40bc93;
      }
  </style>
</head>

<body>
  <div id="app">
    <div id="appSelect" class="container" v-show="Object.keys(school).length == 0">
      <div class="row">
        <div class="input-field col s2">
          <select v-model='prov' id="prov" class="browser-default">
            <option value='AB'>AB</option>
            <option value='BC'>BC</option>
            <option value='QC'>QC</option>
            <option value='ON'>ON</option>
            <label>Prov</label>
          </select>
        </div>
        <div class="input-field col s2">
          <select v-model='coll' id="coll" class="browser-default">
            <option value='merged'>merged</option>
            <option value='manual'>manual</option>
            <option value='findschool'>findschool</option>
          </select>
        </div>
        <div class="col s2">
          <a class="waves-effect waves-light btn" style="margin-top:20px" @click="getSchools">search</a>
        </div>
        <div class="input-field col s4" v-if="schoolList">
          <input type="text" placeholder="School Name" v-model="wordFilter" style="padding-left:10px;">
        </div>
        <div class="col s2" v-if="schoolList">
          <a class="waves-effect waves-light btn" style="margin-top:20px" @click="filterError">Error Only</a>
        </div>
        <!--div class="col s2" v-if="schoolList">
          <a class="waves-effect waves-light btn" style="margin-top:20px">create</a>
        </div-->
      </div>

      <div class="row" v-if="schoolList">
        <div class="collection">
          <!-- Title Row -->
          <div class="collection-item" style="background-color: #f5f5f5; font-weight: bold;">
            <div class="row">
              <span class="col s3">School ID</span>
              <span class="col s3">School Name</span>
              <span class="col s2">City</span>
              <span class="col s1">Province</span>
              <span class="col s1">Active</span>
              <span class="col s1">Boundary</span>
            </div>
          </div>
          <!-- School List Items -->
          <a href="#!" class="collection-item" v-for="(item, index) in filteredSchoolList" :key="index"
            @click="pickSchool(index, item)" :class="{ active: tagClass == index }">
            <div class="row">
              <span class="col s3">
                {{item._id}}
              </span>
              <span class="col s3">
                {{item.nm}}
              </span>
              <span class="col s2">
                {{item.city}}
              </span>
              <span class="col s1">
                {{item.prov}}
              </span>
              <span class="col s1">
                {{item.IsActive}}
              </span>
              <span class="col s1">
                {{item.bnsSize}}
              </span>
            </div>
          </a>
        </div>
      </div>
    </div>

    <div id="appEdit" class="container-fluid" v-show="Object.keys(school).length != 0">
      <div class="col-md-12">
        <div class="header">
          <button type="button" class="btn btn-primary" @click="clickBtnBack">back</button>
          <button type="button" class="btn btn-secondary" @click="toggleEditMode">
            {{ isEditing ? 'Save' : 'Modify' }}
          </button>
          <div class="col-md-6">
            <h5>{{school.nm}}</h5>
            <div>
              <input type="checkbox" name="active"  :disabled="!isEditing" :checked="isEditing?editSchool.IsActive === 1:school.IsActive === 1"
                @change="editSchool.IsActive = editSchool.IsActive === 1 ? 0 : 1" class="filled-in" id="f-active">
              <label for="f-active">Active</label>
            </div>
          </div>
          <div class="col-md-6">
            <div>
              <!-- Display/Edit Mode Toggle -->
              <p>
                <span v-if="!isEditing">{{ school.prov }}</span>
                <input v-else type="text" v-model="editSchool.prov" class="form-control" placeholder="Province">
              </p>
              <p>
                <span v-if="!isEditing">{{ school.city }} {{ school.addr }} {{ school.zip }}</span>
                <input v-if="isEditing" type="text" v-model="editSchool.city" class="form-control" placeholder="City">
                <input v-if="isEditing" type="text" v-model="editSchool.addr" class="form-control" placeholder="Address">
                <input v-if="isEditing" type="text" v-model="editSchool.zip" class="form-control" placeholder="Postal Code">
              </p>
              <p>
                Board: {{ school.board }}
                <span v-if="!isEditing">{{ school.tel }}</span>
                <input v-else type="text" v-model="editSchool.tel" class="form-control" placeholder="Telephone">
              </p>
              <p>
                URL:
                <a v-if="!isEditing" :href="school.url">{{ school.url }}</a>
                <input v-else type="text" v-model="editSchool.url" class="form-control" placeholder="Website URL">
              </p>
              <p>
                Eqao Id:
                <span v-if="!isEditing">{{ school.eqaoId? school.eqaoId.toString():'' }}</span>
                <input v-else type="text" v-model="editSchool.eqaoId" class="form-control" placeholder="Eqao ID">
              </p>
              <p>
                Fraser Id:
                <span v-if="!isEditing">{{ school.fraserId }}</span>
                <input v-else type="text" v-model="editSchool.fraserId" class="form-control" placeholder="Fraser ID">
              </p>
            </div>
          </div>
        </div>
      </div>

      <hr>
      <div class="col-md-5">
        <h5>Boundary</h5>
        <div>
          <div class="row">
            <select multiple="true" id="chooseBn" class="browser-default">
              <option v-for="(title, index) in bnTitles" :key="index" @click="chooseBn(index)">
                {{title}}
              </option>
            </select>
          </div>
          <div>
            <button id='newBn' @click="createNewBoundary">Create New Boundary</button>
            <button @click="toggleCensusBoundary">
              {{ isCensusVisible ? 'Hide Census Boundary' : 'Show Census Boundary' }}
            </button>
          </div>
        </div>
        <div>
          <div class="row">
            <input type="radio" name="language" value='eng' v-model="bnInfo.language" id="r-eng"><label
              for="r-eng">English</label>
            <input type="radio" name="language" value='fi' v-model="bnInfo.language" id="r-fi"><label for="r-fi">
              French Immersion</label>
            <input type="radio" name="language" value='ef' v-model="bnInfo.language" id="r-ef"><label
              for="r-ef">Extended French</label>
          </div>

          <div class="row">
            <label class="col s2">Grade</label>
            <select id="gradeFrom" v-model="bnInfo.gf" class="browser-default col s2">
            </select>
            <span class="col s1">-</span>
            <select id="gradeTo" v-model="bnInfo.gt" class="browser-default col s2">
            </select>
          </div>

          <div class="row">
            <input type="checkbox" :checked="bnInfo.ele === 1" @change="bnInfo.ele = bnInfo.ele === 1 ? 0 : 1"
              class="filled-in" id="c-ele">
            <label for="c-ele">Elementary</label>
            <input type="checkbox" :checked="bnInfo.mid === 1" @change="bnInfo.mid = bnInfo.mid === 1 ? 0 : 1"
              class="filled-in" id="c-mid">
            <label for="c-mid">Middle</label>
            <input type="checkbox" :checked="bnInfo.hgh === 1" @change="bnInfo.hgh = bnInfo.hgh === 1 ? 0 : 1"
              class="filled-in" id="c-hgh">
            <label for="c-hgh">Secondary</label>
          </div>

          <div class="row">
            <input type="checkbox" :checked="bnInfo.ib === 1" @change="bnInfo.ib = bnInfo.ib === 1 ? 0 : 1"
              class="filled-in" id="c-ib">
            <label for="c-ib">IB</label>
            <input type="checkbox" :checked="bnInfo.ap === 1" @change="bnInfo.ap = bnInfo.ap === 1 ? 0 : 1"
              class="filled-in" id="c-ap">
            <label for="c-ap">AP</label>
            <input type="checkbox" :checked="bnInfo.gif === 1" @change="bnInfo.gif = bnInfo.gif === 1 ? 0 : 1"
              class="filled-in" id="c-gif">
            <label for="c-gif">GIFTED</label>
            <input type="checkbox" :checked="bnInfo.art === 1" @change="bnInfo.art = bnInfo.art === 1 ? 0 : 1"
              class="filled-in" id="c-art">
            <label for="c-art">Art</label>
            <input type="checkbox" :checked="bnInfo.sport === 1" @change="bnInfo.sport = bnInfo.sport === 1 ? 0 : 1"
              class="filled-in" id="c-sport">
            <label for="c-sport">Sport</label>
          </div>

          <div class="row">
            <input type="radio" name="inclusiveOrNot" value="0" v-model="bnInfo.exclude" checked id="i-inc"><label
              for="i-inc">Inclusive</label>
            <input type="radio" name="inclusiveOrNot" value="1" v-model="bnInfo.exclude" id="i-exc"><label
              for="i-exc">Exclude</label>
            <input type="text" placeholder="Comments" v-model="bnInfo.Comments" />
          </div>
        </div>
      </div>

      <div class="col-md-7">
        <div class="mapContainer">
          <div class="row" style="position: relative;">
            <!-- button id="btnDraw" type="button" class="btn btn-primary" onclick="clickBtnDraw()">draw</button-->
            <button type="button" class="btn btn-primary" onclick="undo()">undo</button>
            <button type="button" class="btn btn-success" @click="saveBn">save boundary</button>
            <button type="button" class="btn btn-danger waves-red" style="background-color:red;"
              onclick="resetBoundary()">reset</button>
            <button type="button" class="btn btn-danger waves-red" style="background-color:red;"
              @click="removeBoundary">Remove</button>
            <span id="status" class="card-content teal" style="position: absolute">{{message}}</span>
          </div>
          <div id="map" style="margin:2px">
          </div>

        </div>
        <div id="appCensus">
          <section class="tabs">
            <span class="tab" v-for="tab in tabs" :key="tab.key" @click="handleTabClick(tab)" data-sub="demographics" :data-query="'tab:'+tab.key" :class="curTab==tab.key?'active':''">{{tab.txt}}</span>
          </section>
          <div v-if="tabs.length && curTab=='summary'">
            <div v-for="summary in summaryKeys" :key="summary.k" class="dataRow">
              <span>{{summary.txt}}</span>
              <span class="value">{{summary.v}}</span>
            </div>
          </div>
          <div class="chart-container" v-show="curTab!='summary'">
            <canvas id="myChart" style="height: 450px;"></canvas>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="/js/rmapp.min.js"></script>
  <script src="/js/vue3.min.js"></script>
  <script src="/js/axios.min.js"></script>
  <script src="/js/Chart-3.6.2.min.js" id="chartJs"></script>
  <script src="/js/chartjs-plugin-datalabels.js" id="datalabels"></script>
  <script>
    const { createApp, ref, toRaw } = Vue
    function parseResponse(response) {
        if (response.data) {
            json = response.data
            if (json.ec == 1) {
                window.location.href = '/login';
            } else if (json.ec == 2) {
                if (json.eurl) {
                    window.location.href = json.eurl;
                } else {
                    alert(json.e)
                }
            }
            return json;
        } else {
            return null;
        }
    }
    var map;
    var path;
    var boundaryPolygon;
    var schoolId = getParameterByName('schoolId');
    var markers = [];
    var schMarker = null;
    var schPosition = null;
    var census_data = null

    const app = createApp({
        setup() {
          if (schoolId) {
            this.getSchoolInfo();
          }
        },
        mounted() {
          this.getPageData(this.datas, {}, true);
          bus.$on('pagedata-retrieved', function (d) {
            this.jsGmapUrl = d
          });
        },
        data() {
          return {
            schoolList: null,
            wordFilter: '',
            tagClass: '',
            prov:'BC',
            coll:'merged',
            message: null,

            school: {},
            schoolCensusIds: null,
            bns: [],
            bn: {},
            rawCoordinates: [],
            bnsIndex: 0,
            parsedBnInfo: {},
            sw: [],
            ne: [],
            coordinates: [],
            err:null,
            isCensusVisible: false,
            censusBoundaryData: {},

            chart:null,
            summaryKeys:[],
            curTab:'summary',
            tabs:[],

            jsGmapUrl: '',
            datas: [
              'jsGmapUrl'
            ],

            isEditing: false,
            editSchool: { nm: '', prov: '', city: '', addr: '', zip: '', tel: '', url: '', IsActive: 0, eqaoId: [], fraserId: ''},
          }
        },
        computed: {
            //parse raw data to bind with Vue
            bnInfo: function() {
                let info = {
                    language: '',
                    gf: 0,
                    gt: 0,
                    ele: 0,
                    mid: 0,
                    hgh: 0,
                    exclude: 0,
                    ap:0,
                    ib:0,
                    gif:0,
                    art:0,
                    sport:0,
                    Comments: ''
                }
                if (isEmpty(this.bn)) {
                    return {};
                }
                if (this.bn.eng == 1) {
                    info.language = 'eng';
                }
                if (this.bn.fi == 1) {
                    info.language = 'fi';
                }
                if (this.bn.ef == 1) {
                    info.language = 'ef';
                }
                info.gf = this.bn.gf;
                info.gt = this.bn.gt;
                info.ele = this.bn.ele;
                info.hgh = this.bn.hgh;
                info.mid = this.bn.mid;
                info.ib = this.bn.ib;
                info.ap = this.bn.ap;
                info.art = this.bn.art;
                info.sport = this.bn.sport;
                info.gif = this.bn.gif;

                info.exclude = this.bn.exclude;
                console.log(info.Comments);
                console.log(this.bn.Comments);
                info.Comments = this.bn.Comments;
                return info;
            },
            coordinatesOriginal: function() {
                let latLng = [];
                if (!isEmpty(this.bn) && !isEmpty(this.bn.bn)) {
                    let bn = this.bn.bn;
                    for (let i = 0; i < bn.length; i += 2) {
                        let obj = {
                            lat: this.bn.bn[i],
                            lng: this.bn.bn[i + 1]
                        };
                        latLng.push(obj);
                    }
                }
                return latLng;
            },
            centerCoordinate: function() {
                if (isEmpty(this.bn.sw) || isEmpty(this.bn.ne)) {
                    return {};
                }
                let sw = {
                    lat: this.bn.sw[0],
                    lng: this.bn.sw[1]
                };
                let ne = {
                    lat: this.bn.ne[0],
                    lng: this.bn.ne[1]
                };
                let cent = {
                    lat: (sw.lat + ne.lat) * 0.5,
                    lng: (sw.lng + ne.lng) * 0.5,
                    sw: sw,
                    ne: ne
                };
                return cent;
            },
            bnTitles: function() {
                let titles = [];
                if (this.bns.length == 0){
                  return titles;
                }
                for (let bn of this.bns) {
                    let language = '';
                    let inOrexclude = '';
                    let bnLength = 0;

                    if (isEmpty(bn)) {
                        titles.push('unsaved');
                        return titles;
                    }
                    if (bn.eng == 1) {
                        language = 'EN';
                    }
                    if (bn.fi == 1) {
                        language = 'FI';
                    }
                    if (bn.ef == 1) {
                        language = 'EF';
                    }
                    if (bn.exclude == 0) {
                        inOrexclude = 'inclusive';
                    } else {
                        inOrexclude = 'exclude';
                    }
                    if (bn.bn) {
                        bnLength = (bn.bn.length / 2);
                    }
                    let title = language + ' G' + bn.gf + '- G' + bn.gt + ' ' + inOrexclude + ' ' + bnLength;
                    titles.push(title);
                }
                return titles;
            },
            filteredSchoolList() {
                if (!this.schoolList) return [];
                return this.schoolList.filter(item => {
                  const matchesWordFilter = this.wordFilter
                    ? item.nm.toLowerCase().includes(this.wordFilter.toLowerCase())
                    : true;
                  const matchesErrorFilter = this.err ? item.fromErr : true;
                  return matchesWordFilter && matchesErrorFilter;
                });
            }
        },
        watch: {
            school: {
                handler(newValue, oldValue) {
                    this.resetBnOptions()
                    this.resetCensus()
                    this.curTab = 'summary'
                    this.resetEditSchool()
                },
                deep: false,
            }
        },
        methods: {
            filterError:function(){
              if(this.err) {
                this.err=null;
              } else {
                this.err=1;
              }
            },
            toggleEditMode:function() {
              if (this.isEditing) {
                this.saveSchoolInfo();
              } else {
                this.editSchool = { ...this.school };
              }
              if (this.school.eqaoId) {
                this.editSchool.eqaoId = this.school.eqaoId.toString();
              }
              this.isEditing = !this.isEditing;
            },
            getSchools: function(){
              this.schoolList = null;
              axios.get('/bkend/school/schoolList.' + this.prov + '?coll=' + this.coll).then((response) => {
                if (response.data) {
                  this.schoolList = response.data.list;
                }
              });
            },
            pickSchool: function(index, school){
              this.tagClass = index;
              schoolId = school._id;
              this.getSchoolInfo()
            },
            getSchoolInfo: function() {
                if (!schoolId){
                  return alert("No School ID");
                }
                axios.post('/bkend/school/getSchool', {
                    schoolId: schoolId,
                    coll: this.coll
                }).then((response) => {
                    this.school = parseResponse(response);
                    this.bns = this.school.bns;
                    this.showSchoolPosition();
                    resetBoundary();
                    // 需要sourceId作为参数，所以要等查询的school回来之后再请求
                    axios.post('/bkend/schoolCensus/getSchoolCensusIds', {
                        id: schoolId,
                        sourceId: this.school.sourceId
                    }).then((response) => {
                        let r = parseResponse(response);
                        this.schoolCensusIds = r.censusIds || []
                    })
                    axios.post('/bkend/schoolCensus/getSchoolCensusData', {
                        id: schoolId,
                        sourceId: this.school.sourceId
                    }).then((response) => {
                        let ret = parseResponse(response);
                        this.summaryKeys = ret.summary || [];
                        this.tabs = ret.censusChartData || [];
                    })
                })
            },
            saveSchoolInfo: function() {
                const { _id: schoolId } = this.school;
                const { nm, prov, city, zip, tel, url, addr, IsActive, eqaoId, fraserId } = this.editSchool;

                axios.post('/bkend/school/saveSchool', { schoolId, nm, prov, city, zip, tel, url, addr, IsActive, eqaoId, fraserId, coll: this.coll })
                  .then((response) => {
                    if (response.data.ok === 1) {
                      flashMsg('School saved successfully.');
                      // 保存成功后更新school数据
                      Object.assign(this.school, this.editSchool, { IsActive }, { eqaoId: [eqaoId] });
                    } else {
                      flashMsg('Failed to save school.');
                    }
                  })
                  .catch((error) => {
                    console.error('Error saving school info:', error);
                    alert('An error occurred while saving the information.');
                  });
            },
            resetBnOptions: function() {
                  this.bn = {
                      language: '',
                      gf: 0,
                      gt: 0,
                      ele: 0,
                      mid: 0,
                      hgh: 0,
                      exclude: 0,
                      ap: 0,
                      ib: 0,
                      gif: 0,
                      art: 0,
                      sport: 0,
                      Comments: ''
                  }
            },
            resetEditSchool: function() {
                  this.editSchool = {}
                  this.isEditing = false
            },
            resetCensus: function(){
              this.isCensusVisible = false;
              if(census_data){
                  census_data.setMap(null);
                  census_data = null
              }
            },
            showSchoolPosition: function(){
              if (!schMarker){
                schMarker =  new google.maps.Marker({
                  position: map.getCenter(),
                  title: 'School',
                  label: 'SCHOOL',
                  map: map,
                  draggable: true
                });
                google.maps.event.addListener(schMarker, 'dragend', function (e) {
                  let newPosition = e.latLng;
                  let originalPosition = schPosition;
                  
                  if (confirm("Save new school position?")) {
                    // Save the new position
                    schPosition = newPosition;
                    axios.post('/bkend/school/saveSchool', { schoolId, loc: [newPosition.lat(), newPosition.lng()] })
                    .then((response) => {
                      if (response.data.ok === 1) {
                        flashMsg('School saved successfully.');
                      } else {
                        flashMsg('Failed to save school.');
                      }
                    })
                    .catch((error) => {
                      console.error('Error saving school info:', error);
                      alert('An error occurred while saving the information.');
                    });
                  } else {
                    // Return to original position if canceled
                    schMarker.setPosition(originalPosition);
                  }
                });
              }
              if (this.school.loc && this.school.loc.length > 0){
                schPosition = new google.maps.LatLng(this.school.loc[0],this.school.loc[1]);
                schMarker.setPosition(schPosition);
                map.setCenter(schPosition);
              }else{
                locatePostion(
                  this.school.addr + "," + this.school.city + " " + this.school.prov + " " + this.school.zip,
                  function(latLng){
                    schPosition = latLng;
                    schMarker.setPosition(schPosition);
                    map.setCenter(schPosition);
                });
              }
            },
            chooseBn: function(index) {
                this.bn = this.bns[index];
                this.bnsIndex = index;
            },
            createNewBoundary: function(){
              createNewBoundary(this.school);
            },
            toggleCensusBoundary() {
              this.isCensusVisible = !this.isCensusVisible;
              if (this.isCensusVisible) {
                // Check if census_data exists
                if (!census_data) {
                  census_data = new google.maps.Data();
                  census_data.setStyle({
                    fillColor: '#FFFF00',
                    fillOpacity: 0.3,
                    strokeColor: '#000000',
                    strokeWeight: 2
                  });
                }

                // Iterate over schoolCensusIds
                for (let i = 0; i < this.schoolCensusIds.length; i++) {
                  let censusId = this.schoolCensusIds[i];

                  // Check if data for this censusId has already been fetched
                  if (this.censusBoundaryData[censusId]) {
                    // If data exists, add it to the map
                    census_data.addGeoJson(this.censusBoundaryData[censusId]);
                  } else {
                    // If not, fetch and store it
                    axios.post('/boundary/one', { id: censusId, col: "census2021" })
                      .then((response) => {
                        let r = parseResponse(response);
                        let geoJsonData = r.ret.bnds.features[0];
                        this.censusBoundaryData[censusId] = geoJsonData;
                        census_data.addGeoJson(geoJsonData);
                      })
                      .catch(error => {
                        console.error(`Error fetching boundary for ID ${censusId}:`, error);
                      });
                  }
                }
                census_data.setMap(map);
              } else {
                this.resetCensus();
              }
            },
            parseCoordinates: function() {
                this.rawCoordinates = [];
                var path = boundaryPolygon.getPath();
                for (let p of path.getArray()) {
                    this.rawCoordinates.push(p.lat());
                    this.rawCoordinates.push(p.lng());
                }
            },
            removeBoundary: function(){
              if (null == this.bnsIndex){
                return alert("No Boundary selected yet!");
              }
              if (confirm("Remove Boundary " + this.bnsIndex + "?")){
                bnData = {};
                bnData['_id'] = this.school._id;
                bnData['bnsIndex'] = this.bnsIndex;
                axios.put('/bkend/school/removeBn', bnData).then((response) => {
                    var r = parseResponse(response);
                    if (isEmpty(r.e)) {
                        flashMsg("Boundary Removed");
                        this.bnsIndex = null;
                        resetBoundary();
                        this.getSchoolInfo();
                        this.resetBnOptions();
                    } else {
                        window.alert(response.e);
                    }
                })
              }
            },
            saveBn: function() {
                // console.log(this.coordinates.length);
                this.parseCoordinates();
                console.log('this.rawCoordinates')
                console.log(this.rawCoordinates);
                this.parseBnInfo();
                let bnData = this.parsedBnInfo;
                if (bnData.eng == 0 && bnData.fi == 0 && bnData.ef == 0) {
                    window.alert('please choose language');
                    return;
                }
                if (bnData.gf == 0 && bnData.gt == 0) {
                    window.alert('please select grade')
                    return;
                }
                if (bnData.ele == 0 && bnData.mid == 0 && bnData.hgh == 0) {
                    window.alert('please select school level')
                    return;
                }
                bnData['bn'] = this.rawCoordinates;
                bnData['id'] = this.school._id;
                bnData['bnsIndex'] = this.bnsIndex;
                calSwNe();
                bnData['sw'] = this.sw;
                bnData['ne'] = this.ne;
                if (isEmpty(this.bn.bnid)) {
                    bnData['bnid'] = generateBnid();
                } else {
                    bnData['bnid'] = this.bn.bnid;
                }

                if (isEmpty(bnData.bn) || bnData.bn.length < 6) {
                    window.alert("No enough points for boundary" + bnData.bn.length)
                    return;
                }

                bnData['loc'] = [schPosition.lat(),schPosition.lng()]
                bnData['IsActive'] = this.school.IsActive;

                axios.put('/bkend/school/saveBn', bnData).then((response) => {
                    var r = parseResponse(response);
                    if (isEmpty(r.e)) {
                        flashMsg('Saved');
                        this.bn = { ...bnData}
                        this.bns[bnData['bnsIndex']] = { ...bnData }
                    } else {
                        window.alert(response.e);
                    }
                })

                console.log(bnData);

            },
            //adapter of bnInfo, to match the Database structure
            parseBnInfo: function() {
                let info = {
                    eng: 0,
                    fi: 0,
                    ef: 0,
                    gf: 0,
                    gt: 0,
                    ele: 0,
                    mid: 0,
                    hgh: 0,
                    ib:0,
                    ap:0,
                    art:0,
                    gif:0,
                    sport:0,
                    exclude: 0,
                    Comments: ''
                };
                if (isEmpty(this.bnInfo)) {
                    return {};
                }
                switch (this.bnInfo.language) {
                    case 'eng':
                        info['eng'] = 1;
                        break;
                    case 'fi':
                        info['fi'] = 1;
                        break;
                    case 'ef':
                        info['ef'] = 1;
                        break;
                    default:
                }

                info['gt'] = Number(this.bnInfo.gt);
                info['gf'] = Number(this.bnInfo.gf);
                info['ele'] = Number(this.bnInfo.ele);
                info['mid'] = Number(this.bnInfo.mid);
                info['hgh'] = Number(this.bnInfo.hgh);
                info['ib'] = Number(this.bnInfo.ib);
                info['ap'] = Number(this.bnInfo.ap);
                info['art'] = Number(this.bnInfo.art);
                info['sport'] = Number(this.bnInfo.sport);
                info['gif'] = Number(this.bnInfo.gif);                
                info['exclude'] = Number(this.bnInfo.exclude);
                info['Comments'] = this.bnInfo.Comments;
                this.parsedBnInfo = info;
            },
            clickBtnBack:function () {
                //window.location.href = "/html/selectSchool.html";
                this.school = {};
            },
            handleTabClick(tab) {
              // Prevent rapid clicking
              if (this.isTabChanging) return;
              this.isTabChanging = true;
              if (!window.Chart) {
                return;
              }
              this.curTab = tab.key;
              if (this.curTab === 'summary') {
                this.isTabChanging = false;
                return;
              }
              // Delay the chart creation slightly to allow for cleanup
              setTimeout(() => {
                this.drawChart(tab.labels, tab.dataValues);
                this.isTabChanging = false;
              }, 200);
            },
            drawChart(labels,dataValues) {
              const canvas = document.getElementById('myChart');
              if (!canvas) {
                console.log('Canvas not found, waiting...');
                // Try again shortly
                setTimeout(() => this.drawChart(labels, dataValues), 100);
                return;
              }
              if (this.chart) {
                toRaw(this.chart).destroy();
                this.chart = null;
              } 
              const data = {
                labels,
                datasets: [{
                  data: dataValues,
                  fill: false,
                  backgroundColor: '#E03131',
                  datalabels: {
                    align: 'end',
                    anchor: 'start'
                  },
                  borderColor: 'rgb(255, 99, 132)'
                }]
              };
              const config = {
                type: 'bar',
                data,
                plugins:[window.ChartDataLabels],
                options: {
                  
                  indexAxis: 'y',
                  toolTips:false,
                  maintainAspectRatio: false,
                  responsive:true,
                  scales: {
                    y: {
                      grid:{
                        display:false
                      }
                    },
                  },
                  plugins: {
                    tooltip: {
                      callbacks: {
                        label: (context) => this.getValueWithPercentage(context.raw, context.chart)
                      }
                    },
                    datalabels: {
                      color: '#fff',
                      textStrokeColor:'#E03131',
                      textStrokeWidth:'3px',
                      font: {
                        size: '10px',
                        weight: 'bold'
                      },
                      formatter: (value, context) => this.getValueWithPercentage(value, context.chart)
                    },
                    legend:{
                      display: false
                    }
                  },
                }
              };

              this.chart = new window.Chart(
                document.getElementById('myChart').getContext('2d'),
                config
              );
            },
            getValueWithPercentage(value, chart) {
              const total = chart.data.datasets[0].data.reduce((sum, val) => sum + val, 0);
              const percentage = ((value / total) * 100).toFixed(1);
              return `${Math.round(value)} (${percentage}%)`;
            }
        }

    });
    app.mixin(pageDataMixins);
    const vm = app.mount('#app');

    generateGrade("gradeFrom");
    generateGrade("gradeTo");
    function showBoundary() {
        flashMsg("Drawing Boundary");
        setTimeout(function(){
          resetBoundary();
          //createPolygon();
          for (let obj of vm.coordinatesOriginal) {
              let latLng = new google.maps.LatLng(obj.lat, obj.lng);
              addLatLng(latLng);
          }
          if (!isEmpty(vm.centerCoordinate)) {
              //map.setCenter(vm.centerCoordinate);
              map.fitBounds(new google.maps.LatLngBounds(vm.centerCoordinate.sw,vm.centerCoordinate.ne));
          }
          if (vm.bn.exclude == 1){
            boundaryPolygon.setOptions({strokeColor:'#0000FF',fillColor:'#0000FF'});
          }else{
            boundaryPolygon.setOptions({strokeColor:'#FF0000',fillColor:'#FF0000'});
          }
          console.log(vm.coordinates.length);
          flashMsg("Boundary Drew");
        },100);
    }

    window.gMapsCallback = function gMapsCallback() {
        map = new google.maps.Map(document.getElementById('map'), {
            zoom: 12,
            draggableCursor: 'crosshair',
            center: { lat: 43.6532, lng: -79.3832 },  // set a default center to prevent gray map
        });
        path = new google.maps.MVCArray();
        //map.data.setDrawingMode('Polygon');
        createPolygon();
        console.log(path);
        //add click listener
        map.addListener('click', function(e) {
            addLatLng(e.latLng);
        });
        document.getElementById('chooseBn').addEventListener('click', function(e) {
                showBoundary()
            })
        geocoder = new google.maps.Geocoder;
        locatePostion('Toronto',function(){vm.school = {};});

    }
    var geocoder = null;
    function locatePostion(addr,cb){
      //locate the position to Toronto
      geocoder.geocode({
          'address': addr
      }, function(results, status) {
          console.log(results);
          if (status === google.maps.GeocoderStatus.OK) {
              map.setCenter(results[0].geometry.location);
              if (cb){
                cb(results[0].geometry.location);
              }
          } else {
              window.alert('Geocode was not successful for the following reason: ' +
                  status);
          }
      });
    }

    //create a marker, and draw a line between markers
    //push marker to markers[], push latLng class to MVC Path Array
    function addLatLng(latLng) {
        // Because path is an MVCArray, we can simply append a new coordinate
        // and it will automatically appear.
        path.push(latLng);
        boundaryPolygon.setPath(path);
        let title = null,
            marker;
        if (markers.length == 0) {
            title = 'Begin';
        } else if (markers.length == 1) {
            title = 'End';
        } else {
            marker = markers[markers.length - 1];
        }
        if (title) {
            marker = new google.maps.Marker({
                position: latLng,
                title: title,
                label: title,
                map: map,
                draggable: true
            });
            markers.push(marker);
        } else {
            marker.setPosition(latLng);
        }

    }


    // function clickBtnDraw(){
    //   drawStatus=!drawStatus;
    //   var btnDrawText = document.getElementById("btnDraw").firstChild;
    //   if(btnDrawText.data==("draw")){
    //     btnDrawText.data="pause";
    //   }else{
    //     btnDrawText.data="draw";
    //   }
    // }

    function undo() {
        if (marker = markers.pop()) {
            marker.setMap(null);
        }
        path.pop();
    }

    // Deletes all markers in the array by removing references to them.
    function resetBoundary() {
        while (marker = markers.pop()) {
            marker.setMap(null);
        }
        path.clear();
    }

    function createPolygon() {
        // Construct the polygon.
        boundaryPolygon = new google.maps.Polygon({
            path: path,
            strokeColor: '#FF0000',
            strokeOpacity: 0.8,
            strokeWeight: 3,
            fillColor: '#FF0000',
            fillOpacity: 0.1,
            editable: true
        });
        boundaryPolygon.setMap(map);
        google.maps.event.addListener(boundaryPolygon, 'click', function (e) {
          if (e.vertex != null){
            if(confirm("Remove this point?")){
              removePointFromPath(e.vertex);
            }
          }
          //console.log(e);
        });
        google.maps.event.addListener(boundaryPolygon, 'rightclick', function (e) {
          if (e.vertex != null){
            removePointFromPath(e.vertex);
          }
        });
        return boundaryPolygon;
    }

    function removePointFromPath(i){
      path.removeAt(i,1);
    }

    function generateGrade(dropdownId) {
        var dropdown = document.getElementById(dropdownId);
        for (i = 0; i <= 12; i++) {
            var opt = document.createElement("option");
            opt.text = i;
            opt.value = i;
            dropdown.options.add(opt);
        }

    }


    // get parameter by name in url
    function getParameterByName(name, url) {
        if (!url) url = window.location.href;
        name = name.replace(/[\[\]]/g, "\\$&");
        var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
            results = regex.exec(url);
        if (!results) return null;
        if (!results[2]) return '';
        return decodeURIComponent(results[2].replace(/\+/g, " "));
    }

    function createNewBoundary(sch) {
        let info = {
            eng: sch.eng || 0,
            fi: sch.fi || 0,
            ef: sch.ef || 0,
            gf: sch.gf || 0,
            gt: sch.gt || 0,
            ele: sch.ele || 0,
            mid: sch.mid || 0,
            hgh: sch.hgh || 0,
            exclude: 0,
            Comments: ''
        };
        vm.bns.push(info);
        let chooseBn = document.getElementById('chooseBn');
        setTimeout(function() {
            chooseBn.options[vm.bnTitles.length - 1].click();
        }, 500)

    }

    function isEmpty(obj) {
        // null and undefined are "empty"
        if (obj == null) return true;

        // Assume if it has a length property with a non-zero value
        // that that property is correct.
        if (obj.length > 0) return false;
        if (obj.length === 0) return true;

        // Otherwise, does it have any properties of its own?
        // Note that this doesn't handle
        // toString and valueOf enumeration bugs in IE < 9
        for (var key in obj) {
            if (hasOwnProperty.call(obj, key)) return false;
        }
        return true;
    }

    function generateBnid() { // TODO: change
        let bnid = 0;
        if (!isEmpty(vm.school._id) && !isEmpty(vm.bnsIndex)) {
            bnid = this.school._id * 10 + vm.bnsIndex;
        }
        return bnid;
    }

    function test() {
        calSwNe();
        console.log(vm.coordinates);
        console.log(vm.coordinatesOriginal);
    }

    function calSwNe() {
        let lat = [];
        let lng = [];
        for (let position of path.getArray()) {
            lat.push(position.lat());
            lng.push(position.lng());
        }

        lat.sort();
        lng.sort();
        lng.reverse();
        let sw = vm.sw;
        sw[0] = lat[0];
        sw[1] = lng[0];
        let ne = vm.ne;
        ne[0] = lat[lat.length - 1]
        ne[1] = lng[lng.length - 1];
        console.log(sw);
        console.log(ne);
    }
    // get status element in order to set opacity of it, to hide it in certain seconds
    var statusElement = document.getElementById("status");
    var opacityOfStatus = 1;
    //function to hide the status element in certain seconds
    function flashMsg(msg){
      vm.message = msg;
      opacityOfStatus = 1;
      statusElement.style.opacity=opacityOfStatus;
      let handler = setInterval(function () {
        statusElement.style.opacity=opacityOfStatus;
        opacityOfStatus-=0.02;
        if (opacityOfStatus <= 0){
          vm.message = null;
          clearInterval(handler);
        }
      }, 100);
    }
    flashMsg("Select/Create Boundary to start");
  </script>
  <script src="/js/jquery-2.1.1.min.js"></script>
  <script src="/js/bootstrap.min.js"></script>
</body>
