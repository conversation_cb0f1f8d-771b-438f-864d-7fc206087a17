"use strict";function _regeneratorRuntime(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */_regeneratorRuntime=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var a=t&&t.prototype instanceof m?t:m,i=Object.create(a.prototype),c=new B(r||[]);return o(i,"_invoke",{value:R(e,n,c)}),i}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var h="suspendedStart",f="suspendedYield",p="executing",g="completed",v={};function m(){}function y(){}function w(){}var b={};u(b,i,(function(){return this}));var S=Object.getPrototypeOf,M=S&&S(S(_([])));M&&M!==n&&r.call(M,i)&&(b=M);var A=w.prototype=m.prototype=Object.create(b);function E(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function n(o,a,i,c){var l=d(e[o],e,a);if("throw"!==l.type){var u=l.arg,s=u.value;return s&&"object"==_typeof(s)&&r.call(s,"__await")?t.resolve(s.__await).then((function(e){n("next",e,i,c)}),(function(e){n("throw",e,i,c)})):t.resolve(s).then((function(e){u.value=e,i(u)}),(function(e){return n("throw",e,i,c)}))}c(l.arg)}var a;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return a=a?a.then(o,o):o()}})}function R(t,n,r){var o=h;return function(a,i){if(o===p)throw Error("Generator is already running");if(o===g){if("throw"===a)throw i;return{value:e,done:!0}}for(r.method=a,r.arg=i;;){var c=r.delegate;if(c){var l=k(c,r);if(l){if(l===v)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===h)throw o=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=p;var u=d(t,n,r);if("normal"===u.type){if(o=r.done?g:f,u.arg===v)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=g,r.method="throw",r.arg=u.arg)}}}function k(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,k(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var a=d(o,t.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,v;var i=a.arg;return i?i.done?(n[t.resultName]=i.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,v):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function L(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function I(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function B(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(L,this),this.reset(!0)}function _(t){if(t||""===t){var n=t[i];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return a.next=a}}throw new TypeError(_typeof(t)+" is not iterable")}return y.prototype=w,o(A,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:y,configurable:!0}),y.displayName=u(w,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,w):(e.__proto__=w,u(e,l,"GeneratorFunction")),e.prototype=Object.create(A),e},t.awrap=function(e){return{__await:e}},E(x.prototype),u(x.prototype,c,(function(){return this})),t.AsyncIterator=x,t.async=function(e,n,r,o,a){void 0===a&&(a=Promise);var i=new x(s(e,n,r,o),a);return t.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},E(A),u(A,l,"Generator"),u(A,i,(function(){return this})),u(A,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=_,B.prototype={constructor:B,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(I),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return c.type="throw",c.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],c=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=r.call(i,"catchLoc"),u=r.call(i,"finallyLoc");if(l&&u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,v):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),I(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;I(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:_(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),v}},t}function asyncGeneratorStep(e,t,n,r,o,a,i){try{var c=e[a](i),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(r,o)}function _asyncToGenerator(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){asyncGeneratorStep(a,r,o,i,c,"next",e)}function c(e){asyncGeneratorStep(a,r,o,i,c,"throw",e)}i(void 0)}))}}function _typeof(e){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_typeof(e)}var RMSrv;(RMSrv={getCookie:function(e){var t,n,r,o,a;for(a=e+"=",r=0,o=(n=document.cookie.split(";")).length;r<o;r++){for(t=n[r];" "===t.charAt(0);)t=t.substring(1);if(0===t.indexOf(a))return t.substring(a.length,t.length)}return""},init:function(){var e,t;return window.rmCall||(window.rmCall=function(e){return"string"!=typeof e&&(e=JSON.stringify(e)),window.parent.postMessage(e,"*")}),this,t=function(){var e;if(e=document.querySelectorAll('[data-modal="simPopUpModal"]'))return e.forEach((function(e){return e.parentNode.parentNode.removeChild(e.parentNode)}))},e=function(e){return/^\{/.test(e)||!/^\#/.test(e)},window.addEventListener("message",(function(n){var r,o;if(n&&n.data&&(n=n.data),/^:ctx/.test(n)&&(n=n.substr(5)),!n.vueDetected)return/^:cancel/.test(n)?(t(),void(RMSrv.cb=null)):e(n)?((r=RMSrv.cb)&&(r(n),RMSrv.cb=null),t()):"string"==typeof n&&(o=document.querySelector(n))?rmCall(o.innerHTML()):void 0}))},needApp:function(){return alert("Need RealMaster APP")},scanQR:function(){return RMSrv.needApp()},isIOS:function(){return/iPhone|iPad|iPod/i.test(navigator.userAgent)},isAndroid:function(){return/Android/i.test(navigator.userAgent)},isWeChat:function(){return/MicroMessenger/i.test(navigator.userAgent)},isBlackBerry:function(){return/BlackBerry/i.test(navigator.userAgent)},showInBrowser:function(e){return window.open(e,"_blank")},appendDomain:function(e){var t;return(t=window.location.href.split("/"))[0]+"//"+t[2]+e},htmlToElement:function(e){var t;return t=document.createElement("div"),e=e.trim(),t.innerHTML=e,t},getPageContent:function(e,t,n,r){var o,a,i,c,l,u,s,d,h,f;return/^(http|https)/.test(e)||(e=this.appendDomain(e)),null==r&&(r=n,n={wait:0,hide:!0}),o=Object.assign(n,{sel:t,tp:"pageContent",url:e}),f=n.width||"".concat(window.innerWidth,"px"),c=n.height||"".concat(window.innerHeight-44,"px"),s=n.modalId||"simPopUpModal",d=n.mountId?"#".concat(n.mountId):"body",l=document.querySelector("#".concat(s," #the_iframe")),h=function(e){return"string"!=typeof e&&(e=JSON.stringify(e)),l.contentWindow.postMessage(e,"*")},l?(l.src=e,toggleModal("".concat(s),"open"),void h(t)):(a='<a class="icon icon-close pull-right" href="javascript:;", onclick=\'toggleModal("'.concat(s,'","close")\'> </a>'),n.arrow&&(a='<span class="icon fa fa-arrow-'.concat(n.arrow," pull-").concat(n.arrow,'"onclick=\'toggleModal("').concat(s,'","close")\'></span>')),u='<header class="bar bar-nav '.concat(n.white?"common":void 0,'">\n  ').concat(a,'\n  <h1 class="title">').concat(o.title||"RealMaster","</h1>\n</header>"),n.hide&&(u=""),i=this.htmlToElement('<div id="'.concat(s,'" class="modal active" data-modal="simPopUpModal" style="z-index:9999">\n  ').concat(u,"\n  ").concat(a,'\n  <div class="content">\n    <iframe src="').concat(e,'" id="the_iframe" style="width:').concat(f,"; height:").concat(c,'">\n  </div>\n</div>')),document.querySelector(d).appendChild(i),this,r&&(RMSrv.cb=r),n.wait?setTimeout((function(){return h(t)}),n.wait):void 0)},openInAppBrowser:function(e){return window.open(encodeURI(e),"_blank")},openTBrowser:function(e){return window.open(e,"_blank")},dialogAlert:function(e){return alert(e.toString())},dialogConfirm:function(e,t,n,r){var o,a,i,c;return c='<div id="confirm-message-box" style="top:0;left:0;right:0;bottom:0;position:absolute;">\n  <div class="backdrop"></div>\n  <div style="border-radius:10px;padding-top:15px;position:absolute;width:66%;top:45%;left:50%;transform:translate(-50%,-50%);z-index:20;font-size:16px;background:white;text-align:center;">\n    <div style="padding-left:20px;padding-right:20px;">'.concat(e.toString(),'</div>\n    <div style="display:flex;justify-content:space-between;border-top:1px solid #f1f1f1;padding-top:10px;">\n      <button id="rmbtn1"  style="border:none;color:#157AFB;font-size:14px;background:transparent;padding-top:13px;padding-right:11px;padding-bottom:13px;padding-left:11px;width:49.5%;text-align:center;border-radius:13px;">\n        ').concat(r[0],'\n      </button>\n      <div style="display:inline-block;border-left-width:1px;border-left-style:solid;border-left-color:rgb(241, 241, 241);"></div>\n      <button id="rmbtn2"  style="font-weight:bold;border:none;color:#157AFB;font-size:14px;background:transparent;padding-top:13px;padding-right:11px;padding-bottom:13px;padding-left:11px;width:49.5%;text-align:center;border-radius:13px;">\n          ').concat(r[1],"\n        </button>\n    </div>\n  </div>\n</div>"),a=this.htmlToElement(c),document.body.appendChild(a),i=function(){return(a=document.querySelector("#confirm-message-box")).parentNode.removeChild(a)},o=function(){return i(),t(2)},document.querySelector("#rmbtn1").addEventListener("click",i),document.querySelector("#rmbtn2").addEventListener("click",o)},fDoc:function(){var e,t,n;n=document.getElementById("iframe");try{null==(e=n.contentDocument||n.contentWindow)&&(e=document),e.document&&(e=e.document)}catch(e){t=e,console.log(t)}return e},getMeta:function(e){var t,n,r,o,a;for(o=e.querySelectorAll("meta"),a={title:e.title},t=0,n=o.length;t<n;t++)a[(r=o[t]).getAttribute("name")]=r.getAttribute("content");return a},getShareImage:function(e){var t,n,r,o,a;if(t=e.getElementById("content_div")||e.body)for(n=0,o=(a=t.getElementsByTagName("img")).length;n<o;n++)if(null!=(r=a[n])?r.src:void 0)return r.src;return"https://realmaster.com/img/logo.png"},logoImg:"data:image/png;base64,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",resizeImage:function(e,t,n,r){var o,a,i;return o=e.createElement("canvas"),(a=e.createElement("img")).onload=function(){var e,t,a,i,c,l;try{return l=c=this.width,i=a=this.height,c>a?c>n&&(l=n,i=Math.round(a/c*n)):a>n&&(i=n,l=Math.round(c/a*n)),o.width=l,o.height=i,o.getContext("2d").drawImage(this,0,0,l,i),e=o.toDataURL("image/png"),r(e)}catch(e){return t=e,"undefined"!=typeof console&&null!==console&&console.log(t),r(RMSrv.logoImg)}},a.onerror=function(e){return"undefined"!=typeof console&&null!==console&&console.log(e),r(RMSrv.logoImg)},a.setAttribute("crossOrigin","anonymous"),a.crossOrigin="Anonymous",(i=/^(.*\?v=)\d+$/g.exec(t))?a.src=i[1]+Date.now():a.src=t,null},wechatShare:function(e,t){return RMSrv.resizeImage(e,t.image,100,(function(e){return{title:t.title||"RealMaster Sharing",description:t.description||"RealMaster App Sharing",thumbData:e.split(",")[1],url:t.url.replace("realmaster.com","realmaster.cn")},RMSrv.needApp()}))},qrcodeShare:function(e,t,n){var r,o,a;if(null==n&&(n="id_share_qrcode"),"show"===e){if(r=document.getElementById(n))return r.style.display="block",o=function(){var e;return(e=document.getElementById(n+"_holder")).innerHTML="",new QRCode(e,t)},"undefined"!=typeof QRCode&&null!==QRCode?o():((a=document.createElement("script")).type="text/javascript",a.src="/js/qrcode/qrcode.min.js",document.getElementsByTagName("head")[0].appendChild(a),a.onload=o)}else if(r=document.getElementById(n))return r.style.display="none"},showSMB:function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"share-";return RMSrv._shareMask||(RMSrv._shareMask=document.getElementById("backdrop"),RMSrv._shareMask.addEventListener("click",(function(){return RMSrv.showSMB("hide")}))),"show"===e?(RMSrv._sharePrefix=n,(t=document.getElementById(n+"placeholder"))&&t.appendChild(document.getElementById("shareDialog")),document.body.classList.add("smb-open"),RMSrv._shareMask.style.display="block",RMSrv.shareLang()):"hide"===e?(document.body.classList.remove("smb-open"),RMSrv._shareMask.style.display="none"):void 0},_shareLang:null,shareLang:function(e){var t,n,r,o,a,i,c,l,u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.document;return"en"===e||!e&&(null!=(n=document.getElementById("id_share_lang_en"))?n.classList.contains("active"):void 0)?(RMSrv._shareMap={"title-en":"title","desc-en":"description",url:"url",image:"image"},null!=(r=document.getElementById("id_share_lang_en"))&&r.classList.add("active"),null!=(o=document.getElementById("id_share_lang_cur"))&&o.classList.remove("active")):(RMSrv._shareMap={title:"title",desc:"description",url:"url",image:"image"},null!=(a=document.getElementById("id_share_lang_cur"))&&a.classList.add("active"),null!=(i=document.getElementById("id_share_lang_en"))&&i.classList.remove("active")),t=RMSrv._getShareInfo(u,RMSrv.getMeta(u)),null!=(c=document.getElementById("id_share_title"))&&(c.value=t.title),null!=(l=document.getElementById("id_share_desc"))&&(l.value=t.description),RMSrv._shareLang=null!=e&&"cur"!==e?e:null},_getShareInfo:function(e,t){var n,r,o;for(r in n=function(n,r){var o,a;try{if(a=e.getElementById(RMSrv._sharePrefix+n)||e.getElementById("share-"+n)||e.getElementById("alt-"+n))return t[r||n]=a.value||a.textContent}catch(e){return o=e,"undefined"!=typeof console&&null!==console?console.log(o):void 0}},o=RMSrv._shareMap)n(r,o[r]);return null==t.image&&(t.image=RMSrv.getShareImage(e)),null==t.url&&(t.url=e.URL||window.location.href),t},share:function(e){var t,n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.document;switch(t=function(){var e,t,n,o,a,i;return e=RMSrv._getShareInfo(r,RMSrv.getMeta(r)),(i=null!=(o=r.getElementById("id_share_title"))?o.value:void 0)&&(e.title=i),(t=null!=(a=r.getElementById("id_share_desc"))?a.value:void 0)&&(e.description=t),RMSrv._shareLang&&((n=/\?.*(lang\=[a-zA-Z\-]+)/.exec(e.url))?e.url=e.url.replace(n[0],n[0].replace(n[1],"lang=".concat(RMSrv._shareLang))):/\?[a-z0-9]\=/i.test(e.url)?e.url+="&lang="+RMSrv._shareLang:e.url+="?lang="+RMSrv._shareLang,e.url),e},e){case"show":case"hide":return RMSrv.showSMB(e);case"lang-en":return RMSrv.shareLang("en",r);case"lang-cur":return RMSrv.shareLang("cur",r);case"lang-kr":return RMSrv.shareLang("kr",r);case"qr-code":return n=t(),RMSrv.qrcodeShare("show",n.url);case"qr-code-close":return RMSrv.qrcodeShare("hide");case"wechat-friend":return n=t(),RMSrv.wechatShare(r,n,0);case"wechat-moment":return n=t(),RMSrv.wechatShare(r,n,1);case"facebook-feed":return n=t(),RMSrv.facebookShare(r,n,"feed")}},clearCache:function(){},setFileChooser:function(){},onReady:function(e){return e(),!0},getKeyboard:function(e){},fetch:function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0;if("string"!=typeof e)throw new TypeError("fetch方法的第一个参数必须是URL字符串，实际类型: ".concat(_typeof(e)));return"function"==typeof n&&(r=n,n={}),null==n.method&&(n.method="POST"),null==n.credentials&&(n.credentials="same-origin"),null==n.headers&&(n.headers={}),n.headers.Accept="application/json","GET"===n.method.toUpperCase()?delete n.body:("POST"===n.method.toUpperCase()&&null==n.body&&(n.body={}),n.body&&"object"===_typeof(n.body)&&(n.headers["Content-Type"]="application/json",n.body=JSON.stringify(n.body))),n.useNativeFetch?(delete n.useNativeFetch,RMSrv.action({tp:"fetch",opt:n,url:e},r)):(t=function(){var t=_asyncToGenerator(_regeneratorRuntime().mark((function t(){var o,a,i;return _regeneratorRuntime().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,window.fetch(e,n);case 3:if((a=t.sent).ok){t.next=8;break}throw(o=new Error("HTTP ".concat(a.status,": ").concat(a.statusText))).response=a,o;case 8:return t.next=10,a.json();case 10:if(i=t.sent,!r){t.next=14;break}return r(null,i),t.abrupt("return");case 14:return t.abrupt("return",i);case 17:if(t.prev=17,t.t0=t.catch(0),o=t.t0,!r){t.next=24;break}r(o,null),t.next=25;break;case 24:throw o;case 25:case"end":return t.stop()}}),t,null,[[0,17]])})));return function(){return t.apply(this,arguments)}}(),t())}}).init();
