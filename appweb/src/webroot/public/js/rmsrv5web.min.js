"use strict";function _regeneratorRuntime(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */_regeneratorRuntime=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},a="function"==typeof Symbol?Symbol:{},i=a.iterator||"@@iterator",c=a.asyncIterator||"@@asyncIterator",l=a.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var a=t&&t.prototype instanceof m?t:m,i=Object.create(a.prototype),c=new C(r||[]);return o(i,"_invoke",{value:E(e,n,c)}),i}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=s;var f="suspendedStart",h="suspendedYield",p="executing",v="completed",g={};function m(){}function y(){}function S(){}var w={};u(w,i,(function(){return this}));var R=Object.getPrototypeOf,M=R&&R(R(I([])));M&&M!==n&&r.call(M,i)&&(w=M);var b=S.prototype=m.prototype=Object.create(w);function k(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function A(e,t){function n(o,a,i,c){var l=d(e[o],e,a);if("throw"!==l.type){var u=l.arg,s=u.value;return s&&"object"==_typeof(s)&&r.call(s,"__await")?t.resolve(s.__await).then((function(e){n("next",e,i,c)}),(function(e){n("throw",e,i,c)})):t.resolve(s).then((function(e){u.value=e,i(u)}),(function(e){return n("throw",e,i,c)}))}c(l.arg)}var a;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return a=a?a.then(o,o):o()}})}function E(t,n,r){var o=f;return function(a,i){if(o===p)throw Error("Generator is already running");if(o===v){if("throw"===a)throw i;return{value:e,done:!0}}for(r.method=a,r.arg=i;;){var c=r.delegate;if(c){var l=L(c,r);if(l){if(l===g)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===f)throw o=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=p;var u=d(t,n,r);if("normal"===u.type){if(o=r.done?v:h,u.arg===g)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=v,r.method="throw",r.arg=u.arg)}}}function L(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,L(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;var a=d(o,t.iterator,n.arg);if("throw"===a.type)return n.method="throw",n.arg=a.arg,n.delegate=null,g;var i=a.arg;return i?i.done?(n[t.resultName]=i.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,g):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}function _(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function x(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function C(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(_,this),this.reset(!0)}function I(t){if(t||""===t){var n=t[i];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,a=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return a.next=a}}throw new TypeError(_typeof(t)+" is not iterable")}return y.prototype=S,o(b,"constructor",{value:S,configurable:!0}),o(S,"constructor",{value:y,configurable:!0}),y.displayName=u(S,l,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,S):(e.__proto__=S,u(e,l,"GeneratorFunction")),e.prototype=Object.create(b),e},t.awrap=function(e){return{__await:e}},k(A.prototype),u(A.prototype,c,(function(){return this})),t.AsyncIterator=A,t.async=function(e,n,r,o,a){void 0===a&&(a=Promise);var i=new A(s(e,n,r,o),a);return t.isGeneratorFunction(n)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},k(b),u(b,l,"Generator"),u(b,i,(function(){return this})),u(b,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=I,C.prototype={constructor:C,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(x),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return c.type="throw",c.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var a=this.tryEntries.length-1;a>=0;--a){var i=this.tryEntries[a],c=i.completion;if("root"===i.tryLoc)return o("end");if(i.tryLoc<=this.prev){var l=r.call(i,"catchLoc"),u=r.call(i,"finallyLoc");if(l&&u){if(this.prev<i.catchLoc)return o(i.catchLoc,!0);if(this.prev<i.finallyLoc)return o(i.finallyLoc)}else if(l){if(this.prev<i.catchLoc)return o(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return o(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,g):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),x(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;x(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:I(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),g}},t}function asyncGeneratorStep(e,t,n,r,o,a,i){try{var c=e[a](i),l=c.value}catch(e){return void n(e)}c.done?t(l):Promise.resolve(l).then(r,o)}function _asyncToGenerator(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var a=e.apply(t,n);function i(e){asyncGeneratorStep(a,r,o,i,c,"next",e)}function c(e){asyncGeneratorStep(a,r,o,i,c,"throw",e)}i(void 0)}))}}function _typeof(e){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_typeof(e)}var RMSrv,confirmJump,handleOpenURL,jumpURL;RMSrv={getCookie:function(e){var t,n,r,o,a;for(a=e+"=",r=0,o=(n=document.cookie.split(";")).length;r<o;r++){for(t=n[r];" "===t.charAt(0);)t=t.substring(1);if(0===t.indexOf(a))return t.substring(a.length,t.length)}return""},cError:function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.href;console.error(e);try{t=JSON.stringify(e)}catch(e){e}return window.onerror(e.toString(),n,t||e.toString())},_actCallbacks:{next:Date.now()},_actListeners:{},_emitEvent:function(e){var t,n,r,o,a;if((n=RMSrv._actListeners[null!=e?e.tp:void 0])&&n.length>0){for(a=[],r=o=n.length-1;o>=0;r=o+=-1)t=n[r],setTimeout((function(){return t.fn(e.p)}),0),t.once?a.push(n.pop()):a.push(void 0);return a}},action:function(e,t){if("string"==typeof e&&(e={tp:e}),"object"!==_typeof(e))throw new Error("RM: wrong action type");return t&&(e.cb=RMSrv._actCallbacks.next,RMSrv._actCallbacks[RMSrv._actCallbacks.next++]=t,"function"==typeof t&&setTimeout((function(){return t({ok:1})}),10)),window.rmCall(JSON.stringify(e))},reaction:function(e){var t,n;if("{"===e.charAt(0))return(n=JSON.parse(e)).cb&&(t=RMSrv._actCallbacks[n.cb])?(delete RMSrv._actCallbacks[n.cb],setTimeout((function(){return t(n.p)}),0)):n.tp?"ready"!==n.tp?RMSrv._emitEvent(n):null!=n.next?RMSrv._actCallbacks.next=n.next:void 0:console.log({warn:"Undealt reaction",data:e})},listen:function(e,t,n){var r,o,a,i,c;for(n||(n=t,t=!1),i=0,c=(a=null!=(r=RMSrv._actListeners)[e]?r[e]:r[e]=[]).length;i<c;i++)if((o=a[i]).fn===n){if(o.once!==t)throw new Error("Conflict listen once flag for ".concat(e));return}return t?a.unshift({fn:n}):a.push({fn:n,once:t})},setFileChooser:function(e,t){},removeListner:function(e,t){var n,r,o,a,i;if(t){for(o=a=0,i=(r=null!=(n=RMSrv._actListeners)[e]?n[e]:n[e]=[]).length;a<i;o=++a)if(r[o].fn===t)return void r.splice(o,1)}else delete RMSrv._actListeners[e]},init:function(){var e,t,n;return window.rmCall||(window.rmCall=function(e){return"string"!=typeof e&&(e=JSON.stringify(e)),window.parent.postMessage(e,"*")}),window.iframRmCall=function(e){return window.rmCall(e)},n=function(){var e;if(e=document.querySelector("#simPopUpModal"))return e.parentNode.removeChild(e)},t=function(e){return/^\{/.test(e)||!/^\#/.test(e)},window.addEventListener("message",(function(e){var r,o;if(e&&e.data&&(e=e.data),/^:ctx/.test(e)&&(e=e.substr(5)),!e.vueDetected&&!/vue-devtools/.test(e.source))return/^:cancel/.test(e)?(n(),void(RMSrv.cb=null)):t(e)?((r=RMSrv.cb)&&(r(e),RMSrv.cb=null),n()):"string"==typeof e&&(o=document.querySelector(e))?rmCall(o.innerHTML()):void 0})),RMSrv.ready=!0,(e=RMSrv.androidVersion())&&parseFloat(e)<4.4&&(window.oldVerBrowser=!0),RMSrv.listen("pushToken",RMSrv.pushToken),RMSrv.listen("pushNotice",RMSrv.pushNotice),RMSrv.listen("openURL",handleOpenURL),RMSrv.listen("native.keyboardshow",RMSrv.handleKeyboardshow)},goReady:function(){return setTimeout((function(){var e,t,n,r,o,a,i;if(RMSrv.ready=!0,RMSrv._callStack){for(t=0,n=(a=RMSrv._callStack).length;t<n;t++)e=a[t],window.rmCall(e);delete RMSrv._callStack}if(RMSrv._whenReady){for(o=0,r=(i=RMSrv._whenReady).length;o<r;o++)(0,i[o])();return delete RMSrv._whenReady}}),1)},onReady:function(e){return RMSrv.ready?setTimeout((function(){return e()}),0):(null!=RMSrv._whenReady?RMSrv._whenReady:RMSrv._whenReady=[]).push(e)},enableBackButton:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return RMSrv._disableBackKey=!e},setupBackBtn:function(){return RMSrv.listen("backbutton",(function(e){if(!RMSrv._disableBackKey)return document.getElementById("top-page")?(e.preventDefault(),navigator.notification.confirm("Quit?",(function(e){if(1===e)return navigator.app.exitApp()}))):document.getElementById("news-page")?(e.preventDefault(),window.location="/1.5/index"):document.getElementById("wecard-list-page")?(e.preventDefault(),window.location="/1.5/settings"):document.getElementById("dl-share-content")?window.location.href=document.referrer:document.getElementById("srvEle")?(e.preventDefault(),toggleModal("propDetailModal","close")):navigator.app.backHistory()}),!1)},getGeoPosition:function(e){return RMSrv.action("geoPosition",e)},scanQR:function(e){return RMSrv.action("qrcode",(function(t){return"function"==typeof e?e(t):"string"==typeof e?window.location=(e||"/1.5/iframe?u=")+encodeURIComponent(t):RMSrv.cError("Unknown scanQR parameter type")}))},isIOS:function(){return/iPhone|iPad|iPod/i.test(navigator.userAgent)},isAndroid:function(){return/Android/i.test(navigator.userAgent)},isWeChat:function(){return/MicroMessenger/i.test(navigator.userAgent)},isBlackBerry:function(){return/BlackBerry/i.test(navigator.userAgent)},androidVersion:function(){var e;return!!(e=navigator.userAgent.toLowerCase().match(/android\s([0-9\.]*)/))&&e[1]},appendDomain:function(e){var t;return/^\//.test(e)?(t=window.location.href.split("/"))[0]+"//"+t[2]+e:(/^(http|https)/.test(e)||(e="https://"+e),e)},showInBrowser:function(e){return/^(www\.)?realmaster/.test(e)&&(e="https://"+e),/^(http|https)/.test(e)||(e=this.appendDomain(e)),RMSrv.action({tp:"openInBrowser",url:e}),window.open(e,"_blank")},setAppLang:function(e){return RMSrv.action({tp:"setAppLang",lang:e})},showInMap:function(e){var t;return t={tp:"map",lat:e.lat,lng:e.lng,title:e.title},null!=e.marker&&(t.marker=e.marker),e.zoom&&(t.zoom=e.zoom),e.which&&(t.which=e.which),t.mapTypeId=e.mapTypeId||"standard",RMSrv.action(t)},closeAndRedirect:function(e){return RMSrv.action({tp:"closeAndRedirect",url:e})},closeAndRedirectRoot:function(e){return window.location=e},fetch:function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0;if("string"!=typeof e)throw new TypeError("fetch方法的第一个参数必须是URL字符串，实际类型: ".concat(_typeof(e)));return"function"==typeof n&&(r=n,n={}),null==n.method&&(n.method="POST"),null==n.credentials&&(n.credentials="same-origin"),null==n.headers&&(n.headers={}),n.headers.Accept="application/json","GET"===n.method.toUpperCase()?delete n.body:("POST"===n.method.toUpperCase()&&null==n.body&&(n.body={}),n.body&&"object"===_typeof(n.body)&&(n.headers["Content-Type"]="application/json",n.body=JSON.stringify(n.body))),n.useNativeFetch?(delete n.useNativeFetch,RMSrv.action({tp:"fetch",opt:n,url:e},r)):(t=function(){var t=_asyncToGenerator(_regeneratorRuntime().mark((function t(){var o,a,i;return _regeneratorRuntime().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,window.fetch(e,n);case 3:if((a=t.sent).ok){t.next=8;break}throw(o=new Error("HTTP ".concat(a.status,": ").concat(a.statusText))).response=a,o;case 8:return t.next=10,a.json();case 10:if(i=t.sent,!r){t.next=13;break}return t.abrupt("return",r(null,i));case 13:return t.abrupt("return",i);case 16:if(t.prev=16,t.t0=t.catch(0),o=t.t0,!r){t.next=23;break}r(o,null),t.next=24;break;case 23:throw o;case 24:case"end":return t.stop()}}),t,null,[[0,16]])})));return function(){return t.apply(this,arguments)}}(),t())},coreVer:function(e){return e("6.1.2")},onAppStateChange:function(e){if(e)return e()},permissions:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"location",n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"check",r=arguments.length>2?arguments[2]:void 0;if("request"===n||"check"===n){if("location"===t||"notification"===t)return e="permissions."+n+"."+t,RMSrv.action({tp:e},r);RMSrv.dialogAlert("Not Supported permission type: "+t)}else RMSrv.dialogAlert("Not Supported action: "+n)},openSettings:function(){return RMSrv.action({tp:"permissions.openSettings"})},getItemObj:function(e,t,n){var r;return n||(n=t,t=!1),(r=window.localStorage[e])&&n?n(r):(n&&n(null),!0)},setItemObj:function(e,t){var n=e.key,r=e.value;return window.localStorage[n]=JSON.stringify(r),t&&t(),!0},removeItemObj:function(e,t){if(delete window.localStorage[e],t)return t()},openTBrowser:function(e,t,n){return/^(www\.)?realmaster/.test(e)&&(e="https://"+e),/^(http|https)/.test(e)||(e=this.appendDomain(e)),window.open(e,"_blank"),RMSrv.action({tp:"popup",url:e,cfg:t}),null},setCookie:function(){return console.log("setcookie value to native!",document.cookie)},openInAppBrowser:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"location=false";return RMSrv.action({tp:"popup",url:e,cfg:t}),window.open(e,"_blank")},dialogAlert:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return RMSrv.action({tp:"alert",msg:"string"==typeof e?e:e.message||e.toString()}),alert("string"==typeof e?e:e.message||e.toString())},dialogConfirm:function(e,t,n,r){if(r||n&&Array.isArray(n)&&(r=n,n="Confirm"),confirm("".concat(n," : ").concat(e))){if(null!=t)return t(2)}else if(null!=t)return t(1)},isDBG:!1,fDoc:function(){var e,t,n;if(e=null,n=document.getElementById("iframe"))try{null==(e=n.contentDocument)&&(e=document),e.document&&(e=e.document)}catch(e){t=e,console.log(t)}return e},getMeta:function(e){var t,n,r,o,a;for(o=e.querySelectorAll("meta"),a={title:e.title},t=0,n=o.length;t<n;t++)a[(r=o[t]).getAttribute("name")]=r.getAttribute("content");return a},getShareImage:function(e){var t,n,r,o,a;if(t=e.getElementById("content_div")||e.body)for(r=0,o=(a=t.getElementsByTagName("img")).length;r<o;r++)if(null!=(n=a[r])?n.src:void 0)return n.src;return"https://realmaster.com/img/logo.png"},logoImg:"data:image/png;base64,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",hasWechat:function(e){return e(/MicroMessenger/i.test(navigator.userAgent))},hasGoogleService:function(e){return e(!1)},wechatAuth:function(){return RMSrv.action("wechat.auth",(function(e){var t;if((null!=e&&null!=(t=e.code)?t.length:void 0)>10)return window.location.href="/scheme?code="+e.code}))},wechatShareError:function(e){return"ERR_WECHAT_NOT_INSTALLED"===(null!=e?e.toString():void 0)?RMSrv.dialogAlert("WeChat Not Installed"):"object"===_typeof(e)&&-2===e.code?RMSrv.dialogAlert("Cancelled"):RMSrv.dialogAlert(JSON.stringify(e))},wechatShare:function(e,t,n,r){var o,a;return o={title:t.title||"RealMaster Sharing",description:t.description||"RealMaster App Sharing",thumb:t.image,url:t.url,tp:n||0,type:r||"news"},/d\d\.realmaster/.test(t.url)||(o.url=null!=(a=t.url)?a.replace("realmaster.com","realmaster.cn"):void 0),RMSrv.shared(t,!0),RMSrv.action({tp:"wechat.share",p:o},(function(e){return(null!=e?e.err:void 0)?RMSrv.wechatShareError(e.err):(null!=e?e.ok:void 0)?RMSrv.shared(t):void 0}))},facebookShare:function(e,t){return RMSrv.shared(t,!0),RMSrv.action({tp:"facebook.share",content:{contentType:"link",contentUrl:t.url,contentDescription:(t.title||"")+(t.description||"RealMaster App Sharing")}},(function(e){return e.err?RMSrv.dialogAlert(err.toString()):e.cancelled?RMSrv.dialogAlert("Cancelled"):RMSrv.shared(t)}))},socialShare:function(e,t){var n;return n={title:t.title||t.description||"Shared with RealMaster App",message:t.title||t.description||"RealMaster App Sharing"},t.title&&(n.message+=" \n"+t.description),t.url&&(n.message+=" URL: "+t.url),RMSrv.shared(t,!0),RMSrv.action({tp:"share",p:n},(function(e){return e.cancelled?RMSrv.dialogAlert("Cancelled"):e.ok?RMSrv.shared(t):void 0}))},qrcodeShare:function(e,t,n){var r,o,a;if(null==n&&(n="id_share_qrcode"),"show"===e){if(r=document.getElementById(n))return r.style.display="block",o=function(){var e;return(e=document.getElementById(n+"_holder")).innerHTML="",new QRCode(e,t)},"undefined"!=typeof QRCode&&null!==QRCode?o():((a=document.createElement("script")).type="text/javascript",a.src="/js/qrcode/qrcode.min.js",document.getElementsByTagName("head")[0].appendChild(a),a.onload=o)}else if(r=document.getElementById(n))return r.style.display="none"},showSMB:function(e){var t,n,r,o,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"share-";return RMSrv._shareMask||(RMSrv._shareMask=document.getElementById("backdrop"),null!=(n=RMSrv._shareMask)&&n.addEventListener("click",(function(){return RMSrv.showSMB("hide")}))),"show"===e?(RMSrv._sharePrefix=a,(t=document.getElementById(a+"placeholder"))&&t.appendChild(document.getElementById("shareDialog")),document.body.classList.add("smb-open"),null!=(r=RMSrv._shareMask)&&(r.style.display="block"),RMSrv.shareLang()):"hide"===e?(document.body.classList.remove("smb-open"),null!=(o=RMSrv._shareMask)?o.style.display="none":void 0):void 0},_shareMap:{title:"title",desc:"description",url:"url",image:"image",data:"data",dnurl:"dnurl"},_shareLang:null,shareLang:function(e){var t,n,r,o,a,i,c,l,u,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.document;return o=document.getElementById("id_share_lang_en"),i=document.getElementById("id_share_lang_zh"),a=document.getElementById("id_share_lang_kr"),r=document.getElementById("id_share_lang_cur"),null!=o&&o.classList.remove("active"),null!=r&&r.classList.remove("active"),null!=i&&i.classList.remove("active"),null!=a&&a.classList.remove("active"),n=null!=r&&null!=(c=r.dataset)?c.lang:void 0,e&&"cur"!==e?"en"===e?null!=o&&o.classList.add("active"):"zh-cn"===e||"zh"===e?null!=i&&i.classList.add("active"):"kr"===e&&null!=a&&a.classList.add("active"):(e=n,null!=r&&r.classList.add("active")),RMSrv._shareMap="en"===e?{"title-en":"title","desc-en":"description",url:"url",image:"image",data:"data",dnurl:"dnurl"}:{title:"title",desc:"description",url:"url",image:"image",data:"data",dnurl:"dnurl"},t=RMSrv._getShareInfo(s,RMSrv.getMeta(s)),null!=(l=document.getElementById("id_share_title"))&&(l.value=t.title),null!=(u=document.getElementById("id_share_desc"))&&(u.value=t.description),RMSrv._shareLang=null!=e&&"cur"!==e?e:null},_getShareInfo:function(e,t){var n,r,o;for(r in n=function(n,r){var o,a;try{if(a=e.getElementById(RMSrv._sharePrefix+n)||e.getElementById("share-"+n)||e.getElementById("alt-"+n))return t[r||n]=a.value||a.textContent}catch(e){return o=e,"undefined"!=typeof console&&null!==console?console.log(o):void 0}},o=RMSrv._shareMap)n(r,o[r]);return null==t.image&&(t.image=RMSrv.getShareImage(e)),null==t.url&&(t.url=e.URL||window.location.href),t},isCommandAvailable:function(e,t){return RMSrv.action({tp:"isCommandAvailable",cmd:e},t)},share:function(e,t,n){var r;switch("null"!==t&&null!=t||(t=window.document),r=function(e,n){var r,o,a,i,c,l,u,s,d,f,h,p=e.to,v=e.channel;a=function(e){return a=null,n(e)},r=RMSrv._getShareInfo(t,RMSrv.getMeta(t)),(f=null!=(l=t.getElementById("id_share_title"))?l.value:void 0)&&(r.title=f),(o=null!=(u=t.getElementById("id_share_desc"))?u.value:void 0)&&(r.description=o),RMSrv._shareLang&&((c=r.url.match(/\?.*(lang\=[a-zA-Z\-]+)/))?r.url=r.url.replace(c[0],c[0].replace(c[1],"lang=".concat(RMSrv._shareLang))):/\?[a-z0-9]+\=/i.test(r.url)?r.url+="&lang="+RMSrv._shareLang:r.url+="?lang="+RMSrv._shareLang,(c=null!=(s=r.data)?s.match(/.*(lang\=[a-zA-Z\-]+)/):void 0)&&(r.data=r.data.replace(c[0],c[0].replace(c[1],"lang=".concat(RMSrv._shareLang)))));try{if(!r.data&&!(r.data=null!=(d=document.querySelector("#share-data"))?d.innerHTML:void 0))return a(r);p&&(r.data+="&to=".concat(p)),v&&(r.data+="&channel=".concat(v)),(h=new XMLHttpRequest).open("POST","/1.5/api/rm/shareInfo",!0),h.setRequestHeader("Content-type","application/x-www-form-urlencoded"),h.timeout=8e3,h.ontimeout=function(){return RMSrv.dialogAlert("Timeout! Try again Later")},h.onreadystatechange=function(){var e;if(4===h.readyState){if(200===h.status)if(e=JSON.parse(h.responseText),"undefined"!=typeof console&&null!==console&&console.log(e.url),e.ok)r.url2=r.url,r.url=e.url;else if(e.err)return RMSrv.dialogAlert(e.err);if(a&&"share"!==v)return a(r)}},h.send(r.data)}catch(e){i=e,"undefined"!=typeof console&&null!==console&&console.log(i),a&&a(r)}},e){case"show":case"hide":return RMSrv.showSMB(e);case"lang-en":return RMSrv.shareLang("en",t);case"lang-cur":return RMSrv.shareLang("cur",t);case"lang-zh-cn":return RMSrv.shareLang("zh-cn",t);case"lang-kr":return RMSrv.shareLang("kr",t);case"qr-code":return RMSrv.showSMB("hide"),r({channel:e},(function(e){return RMSrv.qrcodeShare("show",e.url)}));case"qr-code-close":return RMSrv.qrcodeShare("hide");case"wechat-friend":return RMSrv.showSMB("hide"),r({channel:e},(function(e){return RMSrv.wechatShare(t,e,0)}));case"wechat-moment":return RMSrv.showSMB("hide"),r({channel:e},(function(e){return RMSrv.wechatShare(t,e,1)}));case"wechat-cust":return RMSrv.showSMB("hide"),r({channel:e},(function(e){return RMSrv.wechatShare(t,e,n.tp,n.type)}));case"facebook-feed":return RMSrv.showSMB("hide"),r({channel:e},(function(e){return RMSrv.facebookShare(t,e)}));default:return RMSrv.showSMB("hide"),r({channel:e},(function(e){return RMSrv.socialShare(t,e)}))}},origin:function(){return window.location.origin||window.location.protocol+"//"+window.location.hostname+(window.location.port?":"+window.location.port:"")},shared:function(e,t){var n,r,o,a,i;return n=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return e.indexOf("ec=")>1&&/propDetailPage|(1\.5\/prop\/detail)/.test(e)?e.split("ec=")[1].split("&")[0]:/projDetailPage/.test(e)?"proj:"+e.split("id=")[1].split("&")[0]:/prop\/detail\/inapp/.test(e)?e.split("id=")[1].split("&")[0]:e.split("?")[0].split("/").pop()},a=RMSrv.origin()+"/1.5/user/update?act=share",null!=e.dnurl&&(a=e.dnurl,/^\//.test(e.dnurl)&&(a=RMSrv.origin()+e.dnurl)),e.url&&(r=n(e.url),o=a.indexOf("?")>0?"&":"?",a+=o+"data="+r,t&&(a+="&pre=1")),(i=new XMLHttpRequest).onreadystatechange=function(){var e;if(4===i.readyState&&200===i.status)try{if((null!=(e=JSON.parse(i.responseText))?e.j:void 0)&&(window.location.href=e.j),null!=e?e.r:void 0)return window.rmCall(e.r)}catch(e){e}},i.open("POST",a,!0),i.send()},sendSMS:function(e){return"string"==typeof e&&(e={body:e,recipients:[e]}),RMSrv.action({tp:"sendSMS",p:e})},clearCache:function(){},keyboard:{close:function(){return RMSrv.action("keyboard.dismiss")},disableScroll:function(e){return RMSrv.action({tp:"disableScroll",p:e})},isVisible:!1},getUniqueId:function(e){var t;return"xxxxxx-xxxxx-xxxxx-xxx"+(null!=(t=localStorage.translateCache)?t.length:void 0)},getKeyboard:function(e){return setTimeout((function(){return e(RMSrv.keyboard)}),0)},handleKeyboardshow:function(e){var t;if(RMSrv.keyboard.isVisible=e.isShow,e.isShow&&window.CustomEvent)return t=new CustomEvent("native.keyboardshow",{keyboardHeight:e.keyboardHeight}),window.dispatchEvent(t)},pushToken:function(e){var t,n,r,o,a,i,c;if((null!=e?e.token:void 0)&&(a=e.os+":"+e.token,localStorage.pn=a,(c=new XMLHttpRequest).onreadystatechange=function(){if(4===c.readyState&&200===c.status)return"undefined"!=typeof console&&null!==console?console.log(c.responseText):void 0},c.open("POST","/1.5/user/updPn"),c.setRequestHeader("Content-type","application/x-www-form-urlencoded"),c.send("pn="+a),RMSrv._RegFinished=!0,n=RMSrv._fnWhenReg)){for(i=[],r=0,o=n.length;r<o;r++){t=n[r];try{i.push(t(a))}catch(e){e}}return i}},htmlToElement:function(e){var t;return t=document.createElement("div"),e=e.trim(),t.innerHTML=e,console.log(t),t},pushNotice:function(e){var t,n,r;if(RMSrv.action("vibrate"),r=e.url||(null!=(t=e.data)?t.url:void 0))return e.foreground?confirmJump((null!=(n=e.data)?n.message:void 0)||e.message,r):jumpURL(r)},whenReg:function(e){return RMSrv._RegFinished?e():(null!=RMSrv._fnWhenReg?RMSrv._fnWhenReg:RMSrv._fnWhenReg=[]).push(e)},getTranslate:function(){var e=_asyncToGenerator(_regeneratorRuntime().mark((function e(t){var n,r,o;return _regeneratorRuntime().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,fetch("/translate",{method:"POST",headers:{"Content-type":"application/json;charset=UTF-8"},body:JSON.stringify({m:encodeURIComponent(t)})});case 3:if(!(r=e.sent).ok){e.next=16;break}return e.next=7,r.json();case 7:if(!(o=e.sent).ok){e.next=12;break}return e.abrupt("return",o);case 12:return o.err?RMSrv.dialogAlert(o.err):RMSrv.dialogAlert("Please contact admin!"),e.abrupt("return",null);case 14:e.next=17;break;case 16:throw new Error("HTTP error: ".concat(r.status));case 17:e.next=24;break;case 19:return e.prev=19,e.t0=e.catch(0),n=e.t0,RMSrv.dialogAlert("An error occurred: ".concat(n.message)),e.abrupt("return",null);case 24:case"end":return e.stop()}}),e,null,[[0,19]])})));return function(t){return e.apply(this,arguments)}}(),getPageContentIframe:function(e,t,n,r){return this.getPageContent(e,t,n,r)},getPageContent:function(e,t,n,r){var o,a,i,c,l,u,s,d;return/^(http|https)/.test(e)||(e=this.appendDomain(e)),null==r&&(r=n,n={wait:0,hide:!0}),a=Object.assign(n,{sel:t,tp:"pageContent",url:e}),d=window.innerWidth,l=window.innerHeight-44,u=document.getElementById("the_iframe"),c="",o="",a.transparent&&(l=window.innerHeight,o="background-color: transparent;",a.hide=!0),s=function(e){return"string"!=typeof e&&(e=JSON.stringify(e)),u.contentWindow.postMessage(e,"*")},u?(u.src=e,toggleModal("simPopUpModal","open"),void s(t)):(a.hide||(c='<header class="bar bar-nav">\n  <a class="icon icon-close pull-right" href="javascript:;", onclick=\'toggleModal("simPopUpModal","close")\'> </a>\n  <h1 class="title">'.concat(a.title||"RealMaster","</h1>\n</header>")),i=this.htmlToElement('<div id="simPopUpModal" class="modal active" style="z-index:9999; '.concat(o,'">\n  ').concat(c,'\n  <div class="content" style="').concat(o,'">\n    <iframe src="').concat(e,'" id="the_iframe" style="width:').concat(d,"px; height:").concat(l,'px;border:0;">\n  </div>\n</div>')),setTimeout((function(){return document.body.appendChild(i)}),500),u=document.getElementById("the_iframe"),this,r&&(RMSrv.cb=r),n.wait?setTimeout((function(){return s(t)}),n.wait):void 0)}},jumpURL=function(e){return window.location=RMSrv.origin()+"/scheme/jump?u="+encodeURIComponent(e)},confirmJump=function(e,t){var n;return n=function(e){if(2===e)return jumpURL(t)},RMSrv.dialogConfirm(e,n,"Message",["Cancel","Open"])},handleOpenURL=function(e){if("string"==typeof e)return setTimeout((function(){var t,n;if((t=e.indexOf("?"))>=0)return n=e.substr(t),window.location=RMSrv.origin()+"/scheme"+n;n=""}),0)},window._errorSent={},window.onerror=function(e,t,n){var r,o,a;if(o=e+"\n"+t+"\n"+n,/d[0-9]\.realmaster/.test(window.location.href)&&alert(o),!window._errorSent[o]){window._errorSent[o]=1;try{return(a=new XMLHttpRequest).onreadystatechange=function(){if(4===a.readyState&&200===a.status)return"undefined"!=typeof console&&null!==console?console.log(a.responseText):void 0},a.open("POST","/cError"),a.setRequestHeader("Content-type","application/x-www-form-urlencoded"),a.send("m="+encodeURIComponent(o))}catch(e){return r=e,"undefined"!=typeof console&&null!==console?console.log(r):void 0}}},RMSrv.init(),setTimeout((function(){return RMSrv.goReady()}),30);
