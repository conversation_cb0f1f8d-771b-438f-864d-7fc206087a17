var adDetail={computed:{calculatedWith:function(){return Math.max(400,20*this.currentAnalysis.length)},activeTime:function(){return this.curBanner.st&&this.curBanner.et?(new Date(this.curBanner.et).getTime()-new Date(this.curBanner.st).getTime())/864e5:""},isAdActive:function(){if(!this.curBanner.st||!this.curBanner.et)return!1;var e=new Date(this.curBanner.et).getTime(),t=new Date(this.curBanner.st).getTime();return e>this.todayts&&this.todayts>t}},data:()=>({todayts:(new Date).getTime(),curBanner:{},currentAnalysis:[],mode:"edit",provs:[],bannerTypes:[{k:"banner",v:"banner"},{k:"project",v:"project(index ad)"},{k:"realtor",v:"realtor"},{k:"mortgage",v:"mortgage"}],dispVar:{edmAdmin:!1,edmApprove:!1,splashAdmin:!1,languageList:[]},datas:["edmAdmin","edmApprove","splashAdmin","adFieldsRequired","languageList"],batch:["weekly","daily","saved"],batchRunTime:{weekly:"Sent at 1am on Saturday",daily:"Sent every afternoon at 5pm",saved:"Sent every afternoon at 6pm"},position:["top","middle","bottom"],baseTemp:{prov:["CA"],pauseHist:[],zh:!0,en:!0},adFieldsRequired:{},imageSize:{splash:{width:1080,height:2340},banner:{width:640,height:180},edm:"width:height=4:3"},area:["china","nochina"],language:[]}),mounted(){if(window.bus){this.$getTranslate(this);var e=window.bus,t=this;this.getPageData(this.datas,{},!0),e.$on("pagedata-retrieved",(function(e){t.dispVar=Object.assign(t.dispVar,e),t.adFieldsRequired=e.adFieldsRequired,(e.edmAdmin||e.edmApprove)&&t.bannerTypes.push({k:"edm",v:"edm"}),e.splashAdmin&&t.bannerTypes.push({k:"splash",v:"splash"}),t.language=e.languageList})),e.$on("image-selected",(({images:e})=>{this.handleImageSelected(e,"src")})),e.$on("image-selected-en",(({images:e})=>{this.handleImageSelected(e,"src_en")})),e.$on("set-city",(e=>{var{o:r,p_ab:a}=e.city;t.curBanner.prov[0]===a?-1==t.curBanner.cities.indexOf(r)&&t.curBanner.cities.push(r):RMSrv.dialogAlert(`The selected city ${r} is not in the current province`)})),t.getProvs(),ADDetail._id?(this.curBanner=ADDetail,this.curBanner.prov||(this.curBanner.prov=["CA"])):(this.curBanner=this.baseTemp,this.curBanner.type=vars.type),vars.create&&(this.mode="create")}else console.error("global bus is required!")},methods:{deleteFieldsByType(){var e=this.adFieldsRequired[this.curBanner.type],t=this;e.forEach((e=>{delete t.curBanner[e]})),t.curBanner.prov=["CA"]},pause(e){var t={type:this.curBanner.type,id:this.curBanner._id,pause:e};fetchData("/1.5/ads/pause",{body:t},(function(e,t){e||t.e?window.bus.$emit("flash-message",e||t.e):window.bus.$emit("flash-message","OK")}))},checkInappUrl(e){!0!==e.target.checked||/^\//.test(this.curBanner.tgt)||(RMSrv.dialogAlert("url should start with /"),setTimeout((()=>{this.curBanner.inapp=!1}),1e3))},selectCity(){var e="/1.5/city/select?search=1",t=this.curBanner.prov[0];t&&(e+=`&prov=${t}`),RMSrv.getPageContent(e,"#callBackString",{hide:!1},(function(e){if(":cancel"!=e)try{var t=JSON.parse(e);window.bus.$emit("set-city",t)}catch(e){console.error(e)}}))},selectProv(e){var t=this.curBanner.prov.indexOf(e);if("CA"!=e){var r=this.curBanner.prov.indexOf("CA");r>-1&&this.curBanner.prov.splice(r,1),t>-1?this.curBanner.prov.splice(t,1):this.curBanner.prov.push(e)}else this.curBanner.prov=[e]},isProvSelected(e){return this.curBanner.prov&&this.curBanner.prov.indexOf(e)>-1},getProvs(){var e=this;fetchData("/1.5/props/provs.json",{body:{}},(function(t,r){t||r.e?window.bus.$emit("flash-message",t||r.e):r.ok&&(e.provs=r.p)}))},save(){if(!Object.keys(this.curBanner))return;if("create"!==this.mode&&!this.curBanner._id)return;var e=Object.assign(this.curBanner,{mode:this.mode});["st","et","tgt","type","grp"].forEach((t=>{if(!e[t]||0==e[t].length)return RMSrv.dialogAlert("广告需要"+t)}));if(["language","area"].forEach((t=>{if(!this[t].some((t=>e.hasOwnProperty(t)&&1==e[t])))return RMSrv.dialogAlert("广告需要"+t)})),"edm"==e.type&&["position","batch"].forEach((t=>{if(!this[t].some((t=>e.hasOwnProperty(t)&&1==e[t])))return RMSrv.dialogAlert("edm广告需要"+t)})),"project"==e.type&&("all"==e.grp||"realtor"==e.grp))return RMSrv.dialogAlert("不要展示project类型的广告给agent");const t=[];e.src&&t.push(this.validateImageSizeAsync(e.src,e.type,"Image size is not qualified")),e.src_en&&t.push(this.validateImageSizeAsync(e.src_en,e.type,"English image size is not qualified")),Promise.all(t).then((t=>{t.some((e=>!e))||this.performSave(e)})).catch((e=>{console.error("图片验证失败:",e),window.bus.$emit("flash-message","图片验证失败")}))},performSave(e){(!e.prov||0==e.prov.length||e.prov.indexOf("CA")>-1)&&delete e.prov;fetchData("/1.5/ads/detail",{body:e},(function(e,t){if(e||t.e)return window.bus.$emit("flash-message",e||t.e);window.bus.$emit("flash-message","Saved"),trackEventOnGoogle("new ad model","create or edit")}))},openImageModel(){this.openImageSelector("image-selected")},openImageModelEn(){this.openImageSelector("image-selected-en")},handleImageSelected(e,t){if(!Array.isArray(e.picUrls))return;const r=e.picUrls[0];this.curBanner[t]=r},openImageSelector(e){RMSrv.getPageContent("/1.5/img/insert","#callBackString",{},(t=>{try{const r=JSON.parse(t);if(!r.picUrls||!Array.isArray(r.picUrls))return;window.bus.$emit(e,{images:r})}catch(e){console.error(e)}}))},validateImageSizeAsync(e,t,r){return new Promise(((a,i)=>{if(!e||!this.imageSize[t])return void a(!0);let n=this.imageSize[t],s=new Image;s.onload=()=>{try{if("edm"==t){var e=s.width/s.height;if(Math.abs(e-4/3)>.01)return RMSrv.dialogAlert(r),void a(!1)}else if(s.width!=n.width||s.height!=n.height)return RMSrv.dialogAlert(r),void a(!1);a(!0)}catch(e){console.error("图片尺寸验证出错:",e),i(e)}},s.onerror=()=>{console.error("图片加载失败:",e),RMSrv.dialogAlert("图片加载失败，无法验证尺寸"),a(!1)},setTimeout((()=>{!1===s.complete&&(console.warn("图片加载超时:",e),RMSrv.dialogAlert("图片加载超时，无法验证尺寸"),a(!1))}),1e4),s.src=e}))}}};initUrlVars();var app=Vue.createApp(adDetail);app.component("flash-message",flashMessage),trans.install(app,{ref:Vue.ref}),app.mixin(pageDataMixins),app.mount("#adDetail");
