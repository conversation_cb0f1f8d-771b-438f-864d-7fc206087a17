const MOBILE_MODE_WIDTH=540;class FrameManager{constructor(t,i,e=300,s=500){this.t=t,this.i=i,this.h=e,this.o=s,this.l=this.o/1e3,this.m(),this._=[],i?this.u(i):this.p(),this.v(),this.M()}p(){const t=document.getElementById(this.t),i=t.innerHTML;t.innerHTML="",this.u(i)}m(){this.C="display: inline-block; flex-shrink:0; white-space: nowrap; float:left; overflow:hidden;",this.F?this.C+="opacity:1;transition: opacity "+this.l+"s;":this.C+="border-left: 1px solid lightgray;transition: width "+this.l+"s;",this.C=this.k(this.C),this.P=this.k("display: inline-block; white-space: nowrap; float:left;border: none; transition: width "+this.l+"s; width: 100%; height: 100%;")}v(){let t=window.innerHeight,i=window.innerWidth,e=this.k("display: flex; flex-wrap: nowrap;  position: absolute; top: 0; left: 0; width: "+i+"px; height: "+t+"px; overflow-y: hidden; overflow-x: auto; white-space: nowrap;");document.getElementById(this.t).setAttribute("style",e)}get I(){return document.getElementById(this.t).clientWidth}get F(){return this.I<=540}T(t){t||(t=this._[this._.length-1]);for(let i=0;i<this._.length&&t!=this._[i];i++){let t=this._[i].S;"1"==t.style.opacity&&(t.style.opacity="0"),"none"!=t.style.display&&(t.style.display="none")}t.S.style.display="inline-block",this.F&&(t.S.style.width="100%",t.S.style.height="100%",t.S.style.opacity="1")}L(t){let i=this.I;return t>0&&(i/=t),i<this.h&&(i=this.h),i}async A(t,i){if(this.F)return this.T(i);t||(t=this._.length);let e=!1,s=this.L(t);for(let n=0;n<t;n++){let t=this._[n]||i,a=Math.min(this.I,Math.max(t.width,s));a!==t.S.clientWidth&&(e=!0),t.S.style.width=a+"px",t.S.style.height=window.innerHeight+"px",t.D.style.width=a-4+"px",t.D.style.height=window.innerHeight-4+"px"}return!e||new Promise((t=>setTimeout(t,this.o)))}async O(t=0){if(this.F)return this.T(this._[t]);t<0&&(t=0),t>=this._.length&&(t=this._.length-1);let i=0;for(let e=0;e<t;e++)i+=this._[e].S.offsetWidth;let e=i+this._[t].S.offsetWidth,s=document.getElementById(this.t),n=e-s.offsetWidth;n<0&&0===s.scrollLeft||await this.U(s,n)}async U(t,i){var e=t.scrollLeft,s=i-e,n=this.o;await new Promise((i=>{var a=null;window.requestAnimationFrame((function animateScroll(h){a||(a=h);var o=(h-a)/n;o=Math.min(o,1),t.scrollLeft=e+s*function easeInOutQuad(t){return t<.5?2*t*t:(4-2*t)*t-1}(o),o<1?window.requestAnimationFrame(animateScroll):i()}))}))}W(){this.m(),this.v(),this.A()}M(){window.addEventListener("resize",this.W.bind(this)),window.addEventListener("message",this.$.bind(this))}k(t){return t.replace(/\/\/.*$/gm,"").replace(/\/\*[\s\S]*?\*\//gm,"").replace(/\n/g,"").replace(/\s{2,}/g," ").replace(/\s*\{\s*/g,"{").replace(/\s*\}\s*/g,"}").replace(/\s*\(\s*/g,"(").replace(/\s*\)\s*/g,")").replace(/\s*==\s*/g,"==").replace(/\s*=\s*/g,"=").replace(/\s*;\s*/g,";").replace(/\s*:\s*/g,":").replace(/\s*,\s*/g,",").trim()}async $(t){if("open"===t.data.type)this.u(t.data.url,t.data.index+1,t.data.width,t.data.call);else if("close"===t.data.type){this.A(t.data.index),await this.j(t.data.index-1);let i=this._[t.data.index-1];i&&i.call&&i.D.contentWindow.postMessage({type:"call",data:t.data.result||{}},"*")}else if("resize"===t.data.type)this._[t.data.index].width=Math.min(t.data.width,this.I),await this.A(),t.data.scroll&&await this.O(t.data.index);else if("msgChild"===t.data.type){let i=this._[t.data.index+1];i&&i.D.contentWindow.postMessage({type:"msgChild",data:t.data.data},"*")}else if("msgParent"===t.data.type){let i=this._[t.data.index-1];i&&i.D.contentWindow.postMessage({type:"msgParent",data:t.data.data},"*")}}N(t){let i=t.D,e=t.index;i.onload=()=>{var t=i.contentDocument||i.contentWindow.document,s=t.createElement("script");let n=function(t){function openUrl(i,e){let s=-1,n=!1;switch(typeof e){case"function":window._fmOnChildEnd=e,n=!0;break;case"object":s=e.width,e.call&&(window._fmOnChildEnd=e.call,n=!0);break;case"number":s=e;break;case"undefined":n=!1;break;case"string":"function"==typeof window[e]&&(window._fmOnChildEnd=window[e],n=!0)}window.parent.postMessage({type:"open",url:i,index:t,width:s,call:n},"*")}let i="";return[openUrl,function exitPage(i=null){window.parent.postMessage({type:"close",index:t,result:i},"*")},function resizeFrames(i,e=!0){window.parent.postMessage({type:"resize",index:t,width:i,scroll:e},"*")},function msgChild(i){window.parent.postMessage({type:"msgChild",index:t,data:i},"*")},function msgParent(i){window.parent.postMessage({type:"msgParent",index:t,data:i},"*")},function convertLinks(t="inFrame"){document.querySelectorAll("a."+t).forEach((t=>{t.addEventListener("click",(function(i){i.preventDefault(),openUrl(this.href,window._fmOnChildEnd||t.getAttribute("onend"))}))}))},'window.addEventListener("message", (e) => {if(e.data.type=="msgChild" && onMsgFromParent){onMsgFromParent(e.data.data);};if(e.data.type=="msgParent" && onMsgFromChild){onMsgFromChild(e.data.data);};if(e.data.type=="call" && window._fmOnChildEnd){window._fmOnChildEnd(e.data.data);}});convertLinks();'].forEach((e=>i+=e.toString().replace(/,\s*index:\s*[a-zA-Z0-9_]+\s*,/g,`,index:${t},`)+";")),i}(e);s.textContent=this.k(n),t.body.appendChild(s)}}async u(t,i=-1,e=-1,s){if(i=parseInt(i),isNaN(i)&&(i=-1),0===i&&this._.length>0)return await this.j(0),void this.q(this._[0].D,t);i<0||i>this._.length?i=this._.length:await this.j(i-1);let n={url:t,index:i,B:"_frame_"+i},a=document.createElement("iframe");a.setAttribute("id",n.B),a.setAttribute("style",this.P),a.setAttribute("data-frame-type","managed"),this.q(a,t);let h=document.createElement("div");h.setAttribute("id",n.B+"_container"),h.setAttribute("style",this.C),h.appendChild(a),document.getElementById(this.t).appendChild(h),n.S=h,n.D=a,n.width=e,this.N(n),this._.push(n);let o=this._[i-1];return o&&(o.call=s),await this.A(i+1,n),this.O(i),n}q(t,i){(i=i.replace(/^\s+|\s+$/g,"")).startsWith("http")||i.startsWith("/")||i.startsWith(".")?t.src=i:t.srcdoc=i}async j(t){if(!(t<0||t>=this._.length-1)){await this.O(t);for(let i=this._.length-1;i>t;i--)this._[i].D.remove(),this._[i].S.remove();this._=this._.slice(0,t+1)}}async H(t){try{await this.j(0),t&&(this._[0].D.src=t,this._[0].url=t,this.i=t,this.A())}catch(t){}}}let __fm__=null;function getFrameManager(t,i,e){let s=window.frameElement?window.frameElement.getAttribute("data-frame-type"):null;if(window.openUrl||window.parent&&window.parent!==window&&"managed"===s)return null;if(__fm__){if(t){if(t!==__fm__.Z)throw new Error("The anchorDivId must be the same");i!==__fm__.i&&__fm__.H(i),e!==__fm__.G&&(__fm__.G=e)}}else{if(!t)throw new Error("The anchorDivId is required");__fm__=new FrameManager(t,i,e)}return __fm__}