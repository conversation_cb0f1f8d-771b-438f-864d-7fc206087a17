should = require('should')
sinon = require('sinon')
helpers = require('../00_common/helpers')
# ./test.sh -f lib/translator.js
{createTranslatorManager,findLanguageValue} = require '../../lib/translator/translatorManager'

describe 'TranslatorManager test',->
  before (done) ->
    @timeout(30000)
    config = helpers.CONFIG(['azure','deepseek','deepL','openAI','gemini','claude','grok','rm'])
    # console.log '++++++',config
    # configFake = {
    #   azure: { subscriptionKey: 'fake-azure-key', endpoint: 'azure-endpoint', region: 'westus' },
    #   deepL: { apiKey: 'fake-deepl-key', endpoint: 'deepl-endpoint' },
    #   deepseek: { key: 'fake-deepseek-key', endpoint: 'deepseek-endpoint' },
    #   openAI: { key: 'fake-openai-key', endpoint: 'openai-endpoint', orgID: 'org-id', projectID: 'project-id' },
    #   gemini: { key: 'fake-gemini-key' },
    #   claude: { key: 'fake-claude-key', endpoint: 'claude-endpoint' }
    # }
    @translatorManager = createTranslatorManager(config)
    done()

  describe 'TranslatorManager constructor test', ->
    it 'should create translator instances for each service', (done) ->
      @translatorManager.translators.should.have.properties(['azure', 'deepl', 'deepseek', 'openAI', 'gemini', 'claude'])
      done()

  describe 'TranslatorManager translate test', ->
    tests = [
      {
        desc: 'should attempt translation with the first available service'
        setup: ->
          @mockTranslate = sinon.stub(@translatorManager.translators.gemini, 'translate').resolves('Translated text','gemini')
        input:
          message: "Hello world"
          translatorList: ['gemini']
          fromLang: 'en'
          toLang: 'zh-cn'
        assertion: (result) ->
          should.deepEqual result, ['Translated text','gemini']
          @mockTranslate.calledOnce.should.be.true()
          @mockTranslate.firstCall.args[0].should.equal('Hello world')
          @mockTranslate.firstCall.args[1].should.equal('English')
          @mockTranslate.firstCall.args[2].should.equal('Chinese')
        teardown: ->
          @mockTranslate.restore()  # Restore the stub to its original function
      },
      {
        desc: 'should fall back to the next service if the first one fails'
        setup: ->
          @failingTranslate = sinon.stub(@translatorManager.translators.gemini, 'translate').rejects(new Error('Translation failed'))
          @successTranslate = sinon.stub(@translatorManager.translators.openAI, 'translate').resolves('Translated text','openAI')
        input:
          message: 'Hello world'
          translatorList: ['gemini', 'openAI']
          fromLang: 'en'
          toLang: 'zh-cn'
        assertion: (result) ->
          should.deepEqual result, ['Translated text','openAI']
          # result.should.equal(['Translated text','openAI'])
          @failingTranslate.calledOnce.should.be.true()
          @successTranslate.calledOnce.should.be.true()
        teardown: ->
          @failingTranslate.restore()
          @successTranslate.restore()
      },
      {
        desc: 'should throw an error if all services fail'
        setup: ->
          @failingTranslate = sinon.stub().rejects(new Error('Translation failed'))
          for translator in Object.values(@translatorManager.translators)
            sinon.stub(translator, 'translate').rejects(new Error('Translation failed'))
        input:
          message: 'Hello world'
          translatorList: ['gemini', 'openAI', 'azure', 'deepL', 'claude']
          fromLang: 'en'
          toLang: 'zh-cn'
        assertion: (error) ->
          error.should.be.an.instanceOf(Error)
          error.message.should.equal('Translation failed with all services')
        teardown: ->
          for translator in Object.values(@translatorManager.translators)
            translator.translate.restore()  # Restore each stub to its original function
      }
    ]

    tests.forEach (test) ->
      it test.desc, () ->
        @timeout(30000)
        test.setup.call(this)

        try
          result = await @translatorManager.translate(test.input.message, test.input.translatorList, test.input.fromLang, test.input.toLang)
          test.assertion.call(this, result)
        catch error
          test.assertion.call(this, error)
        finally
          test.teardown?.call(this)


  describe 'findLanguageValue test', ->
    tests = [
      {
        desc: 'should return correct language value for azure'
        input:
          k: 'en'
          service: 'azure'
        output: 'en'
      },
      {
        desc: 'should return correct language value for deepl'
        input:
          k: 'zh-cn'
          service: 'deepl'
        output: 'ZH'
      },
      {
        desc: 'should return correct language value for openAI'
        input:
          k: 'kr'
          service: 'openAI'
        output: 'Korean'
      },
      {
        desc: 'should throw an error for unsupported service'
        input:
          k: 'en'
          service: 'unsupported'
        error: 'Service \'unsupported\' not supported'
      },
      {
        desc: 'should throw an error for unknown language key'
        input:
          k: 'unknown'
          service: 'azure'
        error: 'Key \'unknown\' not found in LANGUAGE_OBJ'
      }
    ]

    tests.forEach (test) ->
      it test.desc, (done) ->
        @timeout(30000)
        if test.error
          (-> findLanguageValue(test.input.k, test.input.service)).should.throw(test.error)
        else
          result = findLanguageValue(test.input.k, test.input.service)
          should.equal(result, test.output)
        done()

  describe 'TranslatorManager Grok, RM, Gemini API test', ->
    tests = [
      # {
      #   desc: 'should translate text using Grok API'
      #   input:
      #     message: "TWO LEGAL LOTS on a CORNER the location with LANE ACCESS with THREE EXPOSURES!"
      #     translatorList: ['grok']
      #     fromLang: 'en'
      #     toLang: 'zh-cn'
      #   minLength: 5
      #   assertion: ([result,engine]) ->
      #     result.should.be.a.String()
      #     result.should.not.be.empty()
      #     result.length.should.be.above tests[0].minLength
      #     result.should.match(/[\u4e00-\u9fff]/)  # check if the result contains Chinese characters
      #     engine.should.equal('grok')
      # },
      {
        desc: 'should translate text using RM API'
        input:
          message: "TWO LEGAL LOTS on a CORNER the location with LANE ACCESS with THREE EXPOSURES!"
          translatorList: ['rm']
          fromLang: 'en'
          toLang: 'zh-cn'
        minLength: 5
        assertion: ([result,engine]) ->
          result.should.be.a.String()
          result.should.not.be.empty()
          result.length.should.be.above tests[0].minLength
          result.should.match(/[\u4e00-\u9fff]/)  # check if the result contains Chinese characters
          engine.should.equal('rm')
        setup: ->
          @originalFetch = global.fetch
          global.fetch = sinon.stub()
          global.fetch.resolves({
            ok: true
            json: -> Promise.resolve({
              response: '["翻译结果"]'
            })
          })
        teardown: ->
          global.fetch = @originalFetch
      },
      {
        desc: 'should translate text using Gemini API'
        input:
          message: 'TWO LEGAL LOTS on a CORNER the location with LANE ACCESS with THREE EXPOSURES!'
          translatorList: ['gemini']
          fromLang: 'en'
          toLang: 'zh-cn'
        minLength: 5
        assertion: ([result,engine]) ->
          result.should.be.a.String()
          result.should.not.be.empty()
          result.length.should.be.above tests[0].minLength
          result.should.match(/[\u4e00-\u9fff]/)  # check if the result contains Chinese characters
          engine.should.equal('gemini')
      },
      {
        desc: 'should throw an error if Grok API key is missing'
        input:
          message: 'Hello world'
          translatorList: ['grok']
          fromLang: 'en'
          toLang: 'zh-cn'
        setup: ->
          @translatorManager.translators.grok?.key = "test" # remove Grok API key
        assertion: (error) ->
          error.should.be.an.instanceOf(Error)
          error.error.should.equal('Invalid Authorization')
      },
      {
        desc: 'should throw an error if Gemini service is down'
        input:
          message: 'Hello world'
          translatorList: ['gemini']
          fromLang: 'en'
          toLang: 'zh-cn'
        setup: ->
          sinon.stub(@translatorManager.translators.gemini, 'translate').rejects(new Error('Service unavailable'))
        assertion: (error) ->
          error.should.be.an.instanceOf(Error)
          error.message.should.containEql('Translation failed with all services')
      },
      {
        desc: 'should throw an error if RM service is down'
        input:
          message: 'Hello world'
          translatorList: ['rm']
          fromLang: 'en'
          toLang: 'zh-cn'
        setup: ->
          @originalFetch = global.fetch
          global.fetch = sinon.stub()
          global.fetch.rejects(new Error('Service unavailable'))
        assertion: (error) ->
          error.should.be.an.instanceOf(Error)
          error.message.should.containEql('Translation failed with all services')
        teardown: ->
          global.fetch = @originalFetch
      }
    ]

    tests.forEach (test) ->
      it test.desc, () ->
        @timeout(30000)
        if test.setup?
          test.setup.call(this)

        try
          result = await @translatorManager.translate(test.input.message, test.input.translatorList, test.input.fromLang, test.input.toLang)
          test.assertion.call(this, result)
        catch error
          test.assertion.call(this, error)
        finally
          if test.teardown?
            test.teardown.call(this)

  describe 'TranslatorManager availability', ->
    beforeEach ->
      @translatorManager.translators.rm.maxUsage = 2
      @translatorManager.translators.rm.usageCount = 0

    it 'should return available translator', (done) ->
      service = @translatorManager.getAvailableTranslator(['rm', 'azure'])
      service.should.equal('rm')
      done()

    it 'should return null when no translator is available', (done) ->
      @translatorManager.translators.rm.usageCount = 2
      service = @translatorManager.getAvailableTranslator(['rm'])
      should.not.exist(service)
      done()

    it 'should respect translator order in list', (done) ->
      @translatorManager.translators.azure.maxUsage = 2
      @translatorManager.translators.azure.usageCount = 0
      service = @translatorManager.getAvailableTranslator(['azure', 'rm'])
      service.should.equal('azure')
      done()
