should = require('should')
sinon = require('sinon')
helpers = require('../00_common/helpers')
llmModeration = require '../../built/libapp/llmModeration'
translatorManagerLib = require '../../built/lib/translator/translatorManager'

# ./test.sh -f libapp/llmModeration.js
describe 'LLM Moderation test', ->
  before (done) ->
    @timeout(30000)
    config = helpers.CONFIG()
    llmModeration._config(config)
    done()

  describe 'textModerationLLM method tests', ->
    beforeEach ->
      # Mock translatorManager
      @mockTranslatorManager = {
        filterContent: sinon.stub()
      }

      # Store original translatorManager
      @originalTranslatorManager = llmModeration._getTranslatorManager()

      # Replace the module's translatorManager directly
      llmModeration._setTranslatorManager(@mockTranslatorManager)

    afterEach ->
      # Restore original translatorManager
      llmModeration._setTranslatorManager(@originalTranslatorManager)

    it 'should moderate text content successfully - pass', ->
      @timeout(30000)

      @mockTranslatorManager.filterContent.resolves({
        success: true
        passed: true
        reason: 'Content is appropriate'
        usedService: 'gemini'
      })

      content = 'This is a normal comment'
      promptTemplates = [{
        _id: 'template1'
        nm: 'Comment Filter'
        status: 'active'
        scenario: 'comment_filter'
        m_cfg: { m_nm: 'gpt' }
        tpl: { main: 'Filter: {text}', sys: 'You are a content moderator' }
      }]

      result = await llmModeration.textModerationLLM(content, promptTemplates)
      result.should.have.property('block', false)
      result.should.have.property('labels').which.is.an.Array()
      result.labels.should.have.length(0)
      result.should.have.property('msg', '')
      @mockTranslatorManager.filterContent.calledOnce.should.be.true()

    it 'should moderate text content successfully - reject', ->
      @timeout(30000)

      @mockTranslatorManager.filterContent.resolves({
        success: true
        passed: false
        reason: 'Contains inappropriate content'
        usedService: 'gemini'
      })

      content = 'This is inappropriate content'
      promptTemplates = [{
        _id: 'template1'
        status: 'active'
        scenario: 'comment_filter'
        tpl: { main: 'Filter: {text}', sys: 'You are a content moderator' }
      }]

      result = await llmModeration.textModerationLLM(content, promptTemplates)
      result.should.have.property('block', true)
      result.should.have.property('labels').which.is.an.Array()
      result.labels.should.have.length(1)
      result.labels[0].should.have.property('label', 'customized')
      result.labels[0].should.have.property('content', content)
      result.labels[0].should.have.property('details')
      result.labels[0].details.should.have.property('reason', 'Contains inappropriate content')

    it 'should handle array content', ->
      @timeout(30000)

      @mockTranslatorManager.filterContent.resolves({
        success: true
        passed: true
        reason: 'Content is appropriate'
      })

      contentArray = ['First comment', 'Second comment', 'Third comment']
      promptTemplates = [{
        _id: 'template1',
        status: 'active',
        scenario: 'comment_filter',
        tpl: { main: 'Filter: {text}', sys: 'You are a content moderator' }
      }]

      result = await llmModeration.textModerationLLM(contentArray, promptTemplates)
      result.should.have.property('block', false)
      # Verify that content was combined
      expectedContent = 'First comment\nSecond comment\nThird comment'
      @mockTranslatorManager.filterContent.firstCall.args[0].should.equal(expectedContent)

    it 'should handle empty array content', ->
      @timeout(30000)

      result = await llmModeration.textModerationLLM([], [])
      result.should.have.property('block', false)
      result.should.have.property('labels').which.is.an.Array()
      result.labels.should.have.length(0)
      result.should.have.property('msg', '')

    it 'should handle empty content', ->
      @timeout(30000)

      result = await llmModeration.textModerationLLM('', [])
      result.should.have.property('block', false)
      result.should.have.property('labels').which.is.an.Array()
      result.labels.should.have.length(0)
      result.should.have.property('msg', '')

    it 'should handle null content', ->
      @timeout(30000)

      result = await llmModeration.textModerationLLM(null, [])
      result.should.have.property('block', false)
      result.should.have.property('labels').which.is.an.Array()
      result.labels.should.have.length(0)
      result.should.have.property('msg', '')

    it 'should handle empty prompt templates', ->
      @timeout(30000)

      result = await llmModeration.textModerationLLM('Test content', [])
      result.should.have.property('block', false)
      result.should.have.property('labels').which.is.an.Array()
      result.labels.should.have.length(0)
      result.should.have.property('msg', '')

    it 'should handle filter service failure', ->
      @timeout(30000)

      @mockTranslatorManager.filterContent.rejects(new Error('Service unavailable'))

      content = 'Test content'
      promptTemplates = [{
        _id: 'template1',
        status: 'active',
        scenario: 'comment_filter',
        tpl: { main: 'Filter: {text}', sys: 'You are a content moderator' }
      }]

      result = await llmModeration.textModerationLLM(content, promptTemplates)
      result.should.have.property('block', false)
      result.should.have.property('labels').which.is.an.Array()
      result.labels.should.have.length(0)
      result.should.have.property('msg', '')

    it 'should handle filter service error response', ->
      @timeout(30000)

      @mockTranslatorManager.filterContent.resolves({
        success: false
        error: 'API error'
      })

      content = 'Test content'
      promptTemplates = [{
        _id: 'template1',
        status: 'active',
        scenario: 'comment_filter',
        tpl: { main: 'Filter: {text}', sys: 'You are a content moderator' }
      }]

      result = await llmModeration.textModerationLLM(content, promptTemplates)
      result.should.have.property('block', false)
      result.should.have.property('labels').which.is.an.Array()
      result.labels.should.have.length(0)
      result.should.have.property('msg', '')

    it 'should respect bypass configuration', ->
      @timeout(30000)

      @mockTranslatorManager.filterContent.resolves({
        success: true
        passed: false
        reason: 'Contains customized content'
      })

      content = 'Customized content'
      promptTemplates = [{
        _id: 'template1',
        status: 'active',
        scenario: 'comment_filter',
        tpl: { main: 'Filter: {text}', sys: 'You are a content moderator' }
      }]
      options = { bypass: ['customized'] }

      result = await llmModeration.textModerationLLM(content, promptTemplates, options)
      result.should.have.property('block', false)
      result.should.have.property('labels').which.is.an.Array()
      result.labels.should.have.length(0)

  describe 'imageModerationLLM method tests', ->
    beforeEach ->
      # Mock translatorManager
      @mockTranslatorManager = {
        filterContent: sinon.stub()
      }

      # Store original translatorManager
      @originalTranslatorManager = llmModeration._getTranslatorManager()

      # Replace the module's translatorManager directly
      llmModeration._setTranslatorManager(@mockTranslatorManager)

    afterEach ->
      # Restore original translatorManager
      llmModeration._setTranslatorManager(@originalTranslatorManager)

    it 'should moderate image content successfully - pass', ->
      @timeout(30000)

      @mockTranslatorManager.filterContent.resolves({
        success: true
        passed: true
        reason: 'Image is appropriate'
        usedService: 'gemini'
      })

      imageUrl = 'https://example.com/image.jpg'
      promptTemplates = [{
        _id: 'template1'
        nm: 'Image Filter'
        status: 'active'
        scenario: 'comment_filter'
        m_cfg: { m_nm: 'gpt' }
        tpl: { main: 'Filter image', sys: 'You are a content moderator' }
      }]

      result = await llmModeration.imageModerationLLM(imageUrl, promptTemplates)
      result.should.have.property('block', false)
      result.should.have.property('labels').which.is.an.Array()
      result.labels.should.have.length(0)
      result.should.have.property('msg', '')

      # Verify image input structure
      @mockTranslatorManager.filterContent.calledOnce.should.be.true()
      callArgs = @mockTranslatorManager.filterContent.firstCall.args
      callArgs[0].should.have.property('type', 'image')
      callArgs[0].should.have.property('url', imageUrl)
      callArgs[2].should.have.property('context', 'image')
      callArgs[2].should.have.property('filterType', 'comment_filter')

    it 'should moderate image content successfully - reject', ->
      @timeout(30000)

      @mockTranslatorManager.filterContent.resolves({
        success: true
        passed: false
        reason: 'Image contains inappropriate content'
        usedService: 'gemini'
      })

      imageUrl = 'https://example.com/inappropriate.jpg'
      promptTemplates = [{
        _id: 'template1'
        status: 'active'
        scenario: 'comment_filter'
        tpl: { main: 'Filter image', sys: 'You are a content moderator' }
      }]

      result = await llmModeration.imageModerationLLM(imageUrl, promptTemplates)
      result.should.have.property('block', true)
      result.should.have.property('labels').which.is.an.Array()
      result.labels.should.have.length(1)
      result.labels[0].should.have.property('label', 'customized')
      result.labels[0].should.have.property('content', imageUrl)
      result.labels[0].should.have.property('details')
      expectedReason = 'Image contains inappropriate content'
      result.labels[0].details.should.have.property('reason', expectedReason)

    it 'should handle empty image URL', ->
      @timeout(30000)

      result = await llmModeration.imageModerationLLM('', [])
      result.should.have.property('block', false)
      result.should.have.property('labels').which.is.an.Array()
      result.labels.should.have.length(0)
      result.should.have.property('msg', '')

    it 'should handle null image URL', ->
      @timeout(30000)

      result = await llmModeration.imageModerationLLM(null, [])
      result.should.have.property('block', false)
      result.should.have.property('labels').which.is.an.Array()
      result.labels.should.have.length(0)
      result.should.have.property('msg', '')

    it 'should handle filter service failure', ->
      @timeout(30000)

      @mockTranslatorManager.filterContent.rejects(new Error('Service unavailable'))

      imageUrl = 'https://example.com/image.jpg'
      promptTemplates = [{
        _id: 'template1',
        status: 'active',
        scenario: 'comment_filter',
        tpl: { main: 'Filter image', sys: 'You are a content moderator' }
      }]

      result = await llmModeration.imageModerationLLM(imageUrl, promptTemplates)
      result.should.have.property('block', false)
      result.should.have.property('labels').which.is.an.Array()
      result.labels.should.have.length(0)
      result.should.have.property('msg', '')

  describe 'edge cases and boundary tests', ->
    beforeEach ->
      # Mock translatorManager
      @mockTranslatorManager = {
        filterContent: sinon.stub()
      }

      # Store original translatorManager
      @originalTranslatorManager = llmModeration._getTranslatorManager()

      # Replace the module's translatorManager directly
      llmModeration._setTranslatorManager(@mockTranslatorManager)

    afterEach ->
      # Restore original translatorManager
      llmModeration._setTranslatorManager(@originalTranslatorManager)

    it 'should handle very long text content', ->
      @timeout(30000)

      @mockTranslatorManager.filterContent.resolves({
        success: true
        passed: true
        reason: 'Long content is appropriate'
      })

      longContent = 'A'.repeat(50000)
      promptTemplates = [{
        _id: 'template1',
        status: 'active',
        scenario: 'comment_filter',
        tpl: { main: 'Filter: {text}', sys: 'You are a content moderator' }
      }]

      result = await llmModeration.textModerationLLM(longContent, promptTemplates)
      result.should.have.property('block', false)
      @mockTranslatorManager.filterContent.firstCall.args[0].should.equal(longContent)

    it 'should handle special characters in text', ->
      @timeout(30000)

      @mockTranslatorManager.filterContent.resolves({
        success: true
        passed: false
        reason: 'Contains special characters'
      })

      specialContent = 'Content with 中文 and émojis 🚀 and "quotes" and \'apostrophes\''
      promptTemplates = [{
        _id: 'template1',
        status: 'active',
        scenario: 'comment_filter',
        tpl: { main: 'Filter: {text}', sys: 'You are a content moderator' }
      }]

      result = await llmModeration.textModerationLLM(specialContent, promptTemplates)
      result.should.have.property('block', true)
      result.labels[0].should.have.property('content', specialContent)

    it 'should handle mixed array with null and empty values', ->
      @timeout(30000)

      @mockTranslatorManager.filterContent.resolves({
        success: true
        passed: true
        reason: 'Content is appropriate'
      })

      mixedArray = [null, '', 'Valid comment', undefined, 'Another comment', '   ', 'Third comment']
      promptTemplates = [{
        _id: 'template1',
        status: 'active',
        scenario: 'comment_filter',
        tpl: { main: 'Filter: {text}', sys: 'You are a content moderator' }
      }]

      result = await llmModeration.textModerationLLM(mixedArray, promptTemplates)
      result.should.have.property('block', false)
      # Should filter out falsy values and whitespace-only strings
      expectedContent = 'Valid comment\nAnother comment\nThird comment'
      @mockTranslatorManager.filterContent.firstCall.args[0].should.equal(expectedContent)

    it 'should handle multiple bypass labels with l10n', ->
      @timeout(30000)

      @mockTranslatorManager.filterContent.resolves({
        success: true
        passed: false
        reason: 'Contains customized content'
      })

      content = 'Customized content'
      promptTemplates = [{
        _id: 'template1',
        status: 'active',
        scenario: 'comment_filter',
        tpl: { main: 'Filter: {text}', sys: 'You are a content moderator' }
      }]

      mockL10n = sinon.stub()
      mockL10n.withArgs('customized', 'policy').returns('自定义内容')
      mockL10n.withArgs('Your submission has %s contents').returns('您的提交包含%s内容')

      options = {
        bypass: ['spam'], # customized is not in bypass, so should be blocked
        l10n: mockL10n
      }

      result = await llmModeration.textModerationLLM(content, promptTemplates, options)
      result.should.have.property('block', true)
      result.should.have.property('msg')
      result.msg.should.containEql('自定义内容')
      result.msg.should.containEql('Contains customized content')

    it 'should handle image URL with special characters', ->
      @timeout(30000)

      @mockTranslatorManager.filterContent.resolves({
        success: true
        passed: true
        reason: 'Image is appropriate'
      })

      specialImageUrl = 'https://example.com/images/test.jpg?param=value'
      promptTemplates = [{
        _id: 'template1',
        status: 'active',
        scenario: 'comment_filter',
        tpl: { main: 'Filter image', sys: 'You are a content moderator' }
      }]

      result = await llmModeration.imageModerationLLM(specialImageUrl, promptTemplates)
      result.should.have.property('block', false)
      # Verify image input structure with special URL
      callArgs = @mockTranslatorManager.filterContent.firstCall.args
      callArgs[0].should.have.property('type', 'image')
      callArgs[0].should.have.property('url', specialImageUrl)

    it 'should handle configuration check disabled', ->
      @timeout(30000)

      # Store original config and temporarily disable check
      originalConfig = llmModeration._config_sections
      llmModeration._config({ check: false })

      content = 'Any content'
      promptTemplates = [{ _id: 'template1', status: 'active', scenario: 'comment_filter' }]

      result = await llmModeration.textModerationLLM(content, promptTemplates)
      result.should.have.property('block', false)
      result.should.have.property('labels').which.is.an.Array()
      result.labels.should.have.length(0)
      result.should.have.property('msg', '')
      # Should not call filterContent when check is disabled
      @mockTranslatorManager.filterContent.called.should.be.false()

      # Restore config
      llmModeration._config({ check: true })
