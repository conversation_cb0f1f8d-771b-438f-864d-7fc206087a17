should = require('should')
sinon = require('sinon')
helpers = require('../00_common/helpers')
OpenAITranslator = require '../../built/lib/translator/openAITranslator'

# ./test.sh -f translator/openAITranslator.js
describe 'OpenAITranslator translateWithCustomPrompt test', ->
  before (done) ->
    @timeout(30000)
    config = helpers.CONFIG()
    # 修复构造函数调用，传递正确的参数
    openAIConfig = config.openAI ? {}
    @openAITranslator = new OpenAITranslator(
      openAIConfig.key ? 'test-api-key',
      openAIConfig.endpoint ? 'https://api.openai.com/v1/chat/completions',
      'gpt-3.5-turbo',
      'You are a professional translator',
      openAIConfig.orgID ? 'test-org-id',
      openAIConfig.projectID ? 'test-project-id',
      openAIConfig.maxUsage ? 10
    )
    done()

  describe 'translateWithCustomPrompt method tests', ->
    beforeEach ->
      @originalFetch = global.fetch
      @mockUse = sinon.stub(@openAITranslator, 'use')
      @mockRelease = sinon.stub(@openAITranslator, 'release')

    afterEach ->
      global.fetch = @originalFetch
      @mockUse.restore()
      @mockRelease.restore()

    it 'should translate with custom prompt successfully', ->
      @timeout(30000)

      # Mock successful API response
      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          choices: [{
            message: {
              content: 'This is a translated result\n\n'
            }
          }]
        })
      })

      customPrompt = 'Translate this text: Hello world'
      systemPrompt = 'You are a professional translator'

      @openAITranslator.translateWithCustomPrompt(customPrompt, systemPrompt)
        .then (result) =>
          result.should.equal('This is a translated result')
          global.fetch.calledOnce.should.be.true()

          # Verify request structure
          callArgs = global.fetch.firstCall.args
          callArgs[0].should.equal(@openAITranslator.endpoint)

          requestBody = JSON.parse(callArgs[1].body)
          requestBody.should.have.property('model', @openAITranslator.model)
          requestBody.should.have.property('stream', false)
          requestBody.messages.should.have.length(2)
          requestBody.messages[0].should.deepEqual({role: 'system', content: systemPrompt})
          requestBody.messages[1].should.deepEqual({role: 'user', content: customPrompt})

          # Verify headers
          headers = callArgs[1].headers
          headers.should.have.property('Authorization', "Bearer #{@openAITranslator.apiKey}")
          headers.should.have.property('OpenAI-Organization', @openAITranslator.orgID)
          headers.should.have.property('OpenAI-Project', @openAITranslator.projectID)

    it 'should work without system prompt', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          choices: [{
            message: {
              content: 'Translated without system prompt'
            }
          }]
        })
      })

      customPrompt = 'Translate: Hello'

      @openAITranslator.translateWithCustomPrompt(customPrompt)
        .then (result) ->
          result.should.equal('Translated without system prompt')

          requestBody = JSON.parse(global.fetch.firstCall.args[1].body)
          requestBody.messages.should.have.length(1)
          requestBody.messages[0].should.deepEqual({role: 'user', content: customPrompt})

    it 'should handle API error response', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: false
        json: -> Promise.resolve({
          message: 'API rate limit exceeded'
        })
      })

      @openAITranslator.translateWithCustomPrompt('Test prompt')
        .then (result) ->
          throw new Error('Should have thrown an error')
        .catch (error) ->
          error.message.should.equal('API rate limit exceeded')

    it 'should handle API error response without message', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: false
        json: -> Promise.resolve('Error object without message')
      })

      @openAITranslator.translateWithCustomPrompt('Test prompt')
        .then (result) ->
          throw new Error('Should have thrown an error')
        .catch (error) ->
          error.message.should.equal('Error object without message')

    it 'should handle network error', ->
      @timeout(30000)

      global.fetch = sinon.stub().rejects(new Error('Network error'))

      @openAITranslator.translateWithCustomPrompt('Test prompt')
        .then (result) ->
          throw new Error('Should have thrown an error')
        .catch (error) ->
          error.message.should.equal('Network error')

    it 'should handle empty custom prompt', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          choices: [{
            message: {
              content: ''
            }
          }]
        })
      })

      @openAITranslator.translateWithCustomPrompt('')
        .then (result) ->
          result.should.equal('')

    it 'should handle null custom prompt', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          choices: [{
            message: {
              content: 'null handled'
            }
          }]
        })
      })

      @openAITranslator.translateWithCustomPrompt(null)
        .then (result) ->
          result.should.equal('null handled')
          requestBody = JSON.parse(global.fetch.firstCall.args[1].body)
          content = requestBody.messages[0].content
          if content?
            content.should.equal(null)
          else
            should.not.exist(content)

    it 'should remove trailing newlines from result', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          choices: [{
            message: {
              content: 'Result with newlines\n\n\n'
            }
          }]
        })
      })

      @openAITranslator.translateWithCustomPrompt('Test')
        .then (result) ->
          result.should.equal('Result with newlines')

    it 'should call use() and release() methods', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          choices: [{
            message: {
              content: 'Success'
            }
          }]
        })
      })

      @openAITranslator.translateWithCustomPrompt('Test')
        .then (result) =>
          @mockUse.calledOnce.should.be.true()
          @mockRelease.calledOnce.should.be.true()

    it 'should call release() even when error occurs', ->
      @timeout(30000)

      global.fetch = sinon.stub().rejects(new Error('Test error'))

      @openAITranslator.translateWithCustomPrompt('Test')
        .then (result) ->
          throw new Error('Should have thrown an error')
        .catch (error) =>
          @mockUse.calledOnce.should.be.true()
          @mockRelease.calledOnce.should.be.true()

  describe 'translateWithCustomPrompt parameter validation', ->
    beforeEach ->
      @originalFetch = global.fetch
      @mockUse = sinon.stub(@openAITranslator, 'use')
      @mockRelease = sinon.stub(@openAITranslator, 'release')

    afterEach ->
      global.fetch = @originalFetch
      @mockUse.restore()
      @mockRelease.restore()

    it 'should handle very long custom prompt', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          choices: [{
            message: {
              content: 'Long prompt handled'
            }
          }]
        })
      })

      longPrompt = 'A'.repeat(10000)

      @openAITranslator.translateWithCustomPrompt(longPrompt)
        .then (result) ->
          result.should.equal('Long prompt handled')

    it 'should handle special characters in prompt', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          choices: [{
            message: {
              content: 'Special chars handled'
            }
          }]
        })
      })

      specialPrompt = 'Test with 中文 and émojis 🚀 and "quotes" and \'apostrophes\''

      @openAITranslator.translateWithCustomPrompt(specialPrompt)
        .then (result) ->
          result.should.equal('Special chars handled')
          requestBody = JSON.parse(global.fetch.firstCall.args[1].body)
          requestBody.messages[0].content.should.equal(specialPrompt)
