should = require('should')
sinon = require('sinon')
helpers = require('../00_common/helpers')
GrokTranslator = require '../../built/lib/translator/grokTranslator'

# ./test.sh -f translator/grokTranslator.js
describe 'GrokTranslator translateWithCustomPrompt test', ->
  before (done) ->
    @timeout(30000)
    config = helpers.CONFIG()
    # 修复构造函数调用，传递正确的参数
    grokConfig = config.grok ? {}
    @grokTranslator = new GrokTranslator(
      grokConfig.key ? 'test-api-key',
      grokConfig.endpoint ? 'https://api.x.ai/v1/chat/completions',
      'grok-beta',
      'You are a professional translator',
      grokConfig.maxUsage ? 10
    )
    done()

  describe 'translateWithCustomPrompt method tests', ->
    beforeEach ->
      @mockUse = sinon.stub(@grokTranslator, 'use')
      @mockRelease = sinon.stub(@grokTranslator, 'release')
      @mockInvokeTranslateApi = sinon.stub(@grokTranslator, '_invokeTranslateApi')

    afterEach ->
      @mockUse.restore()
      @mockRelease.restore()
      @mockInvokeTranslateApi.restore()

    it 'should translate with custom prompt successfully', ->
      @timeout(30000)

      # Mock successful API response
      @mockInvokeTranslateApi.resolves({
        choices: [{
          message: {
            content: 'This is a translated result\n\n'
          }
        }]
      })

      customPrompt = 'Translate this text: Hello world'
      systemPrompt = 'You are a professional translator'

      @grokTranslator.translateWithCustomPrompt(customPrompt, systemPrompt)
        .then (result) =>
          result.should.equal('This is a translated result')
          @mockInvokeTranslateApi.calledOnce.should.be.true()

          # Verify request structure
          callArgs = @mockInvokeTranslateApi.firstCall.args[0]
          callArgs.should.have.property('model', @grokTranslator.model)
          callArgs.should.have.property('stream', false)
          callArgs.messages.should.have.length(2)
          callArgs.messages[0].should.deepEqual({role: 'system', content: systemPrompt})
          callArgs.messages[1].should.deepEqual({role: 'user', content: customPrompt})

    it 'should work without system prompt', ->
      @timeout(30000)

      @mockInvokeTranslateApi.resolves({
        choices: [{
          message: {
            content: 'Translated without system prompt'
          }
        }]
      })

      customPrompt = 'Translate: Hello'

      @grokTranslator.translateWithCustomPrompt(customPrompt)
        .then (result) =>
          result.should.equal('Translated without system prompt')

          callArgs = @mockInvokeTranslateApi.firstCall.args[0]
          callArgs.messages.should.have.length(1)
          callArgs.messages[0].should.deepEqual({role: 'user', content: customPrompt})

    it 'should handle API error response', ->
      @timeout(30000)

      @mockInvokeTranslateApi.rejects(new Error('API rate limit exceeded'))

      @grokTranslator.translateWithCustomPrompt('Test prompt')
        .then (result) ->
          throw new Error('Should have thrown an error')
        .catch (error) ->
          error.message.should.equal('API rate limit exceeded')

    it 'should handle empty choices response', ->
      @timeout(30000)

      @mockInvokeTranslateApi.resolves({
        choices: []
      })

      @grokTranslator.translateWithCustomPrompt('Test prompt')
        .then (result) ->
          throw new Error('Should have thrown an error')
        .catch (error) ->
          error.message.should.equal('No translation result returned')

    it 'should handle missing choices in response', ->
      @timeout(30000)

      @mockInvokeTranslateApi.resolves({})

      @grokTranslator.translateWithCustomPrompt('Test prompt')
        .then (result) ->
          throw new Error('Should have thrown an error')
        .catch (error) ->
          error.message.should.equal('No translation result returned')

    it 'should handle empty custom prompt', ->
      @timeout(30000)

      @mockInvokeTranslateApi.resolves({
        choices: [{
          message: {
            content: ''
          }
        }]
      })

      @grokTranslator.translateWithCustomPrompt('')
        .then (result) ->
          result.should.equal('')

    it 'should handle null custom prompt', ->
      @timeout(30000)

      @mockInvokeTranslateApi.resolves({
        choices: [{
          message: {
            content: 'null handled'
          }
        }]
      })

      @grokTranslator.translateWithCustomPrompt(null)
        .then (result) =>
          result.should.equal('null handled')
          callArgs = @mockInvokeTranslateApi.firstCall.args[0]
          callArgs.messages.should.have.length(1)
          # When null is passed, it becomes null in the content
          should.equal(callArgs.messages[0].content, null)

    it 'should remove trailing newlines from result', ->
      @timeout(30000)

      @mockInvokeTranslateApi.resolves({
        choices: [{
          message: {
            content: 'Result with newlines\n\n\n'
          }
        }]
      })

      @grokTranslator.translateWithCustomPrompt('Test')
        .then (result) ->
          result.should.equal('Result with newlines')

    it 'should call use() and release() methods', ->
      @timeout(30000)

      @mockInvokeTranslateApi.resolves({
        choices: [{
          message: {
            content: 'Success'
          }
        }]
      })

      @grokTranslator.translateWithCustomPrompt('Test')
        .then (result) =>
          @mockUse.calledOnce.should.be.true()
          @mockRelease.calledOnce.should.be.true()

    it 'should call release() even when error occurs', ->
      @timeout(30000)

      @mockInvokeTranslateApi.rejects(new Error('Test error'))

      @grokTranslator.translateWithCustomPrompt('Test')
        .then (result) ->
          throw new Error('Should have thrown an error')
        .catch (error) =>
          @mockUse.calledOnce.should.be.true()
          @mockRelease.calledOnce.should.be.true()

  describe 'translateWithCustomPrompt parameter validation', ->
    beforeEach ->
      @mockUse = sinon.stub(@grokTranslator, 'use')
      @mockRelease = sinon.stub(@grokTranslator, 'release')
      @mockInvokeTranslateApi = sinon.stub(@grokTranslator, '_invokeTranslateApi')

    afterEach ->
      @mockUse.restore()
      @mockRelease.restore()
      @mockInvokeTranslateApi.restore()

    it 'should handle very long custom prompt', ->
      @timeout(30000)

      @mockInvokeTranslateApi.resolves({
        choices: [{
          message: {
            content: 'Long prompt handled'
          }
        }]
      })

      longPrompt = 'A'.repeat(10000)

      @grokTranslator.translateWithCustomPrompt(longPrompt)
        .then (result) ->
          result.should.equal('Long prompt handled')

    it 'should handle special characters in prompt', ->
      @timeout(30000)

      @mockInvokeTranslateApi.resolves({
        choices: [{
          message: {
            content: 'Special chars handled'
          }
        }]
      })

      specialPrompt = 'Test with 中文 and émojis 🚀 and "quotes" and \'apostrophes\''

      @grokTranslator.translateWithCustomPrompt(specialPrompt)
        .then (result) =>
          result.should.equal('Special chars handled')
          callArgs = @mockInvokeTranslateApi.firstCall.args[0]
          callArgs.messages[0].content.should.equal(specialPrompt)

    it 'should bypass batch processing mechanism', ->
      @timeout(30000)

      @mockInvokeTranslateApi.resolves({
        choices: [{
          message: {
            content: 'Direct API call'
          }
        }]
      })

      @grokTranslator.translateWithCustomPrompt('Test direct call')
        .then (result) =>
          result.should.equal('Direct API call')
          # Verify that _invokeTranslateApi was called directly
          @mockInvokeTranslateApi.calledOnce.should.be.true()
