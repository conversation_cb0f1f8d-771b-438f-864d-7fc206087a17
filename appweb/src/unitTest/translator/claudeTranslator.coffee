should = require('should')
sinon = require('sinon')
helpers = require('../00_common/helpers')
ClaudeTranslator = require '../../built/lib/translator/claudeTranslator'

# ./test.sh -f translator/claudeTranslator.js
describe 'ClaudeTranslator translateWithCustomPrompt test', ->
  before (done) ->
    @timeout(30000)
    config = helpers.CONFIG()
    # 修复构造函数调用，传递正确的参数
    claudeConfig = config.claude ? {}
    @claudeTranslator = new ClaudeTranslator(
      claudeConfig.key ? 'test-api-key',
      claudeConfig.endpoint ? 'https://api.anthropic.com/v1/messages',
      'claude-3-haiku-20240307',
      'You are a professional translator',
      claudeConfig.maxUsage ? 10
    )
    done()

  describe 'translateWithCustomPrompt method tests', ->
    beforeEach ->
      @originalFetch = global.fetch
      @mockUse = sinon.stub(@claudeTranslator, 'use')
      @mockRelease = sinon.stub(@claudeTranslator, 'release')

    afterEach ->
      global.fetch = @originalFetch
      @mockUse.restore()
      @mockRelease.restore()

    it 'should translate with custom prompt successfully', ->
      @timeout(30000)

      # Mock successful API response
      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          content: [{
            text: 'This is a translated result\n\n'
          }]
        })
      })

      customPrompt = 'Translate this text: Hello world'
      systemPrompt = 'You are a professional translator'

      @claudeTranslator.translateWithCustomPrompt(customPrompt, systemPrompt)
        .then (result) =>
          result.should.equal('This is a translated result')
          global.fetch.calledOnce.should.be.true()

          # Verify request structure
          callArgs = global.fetch.firstCall.args
          callArgs[0].should.equal(@claudeTranslator.endpoint)

          requestBody = JSON.parse(callArgs[1].body)
          requestBody.should.have.property('model', @claudeTranslator.model)
          requestBody.should.have.property('max_tokens', 1024)
          requestBody.messages.should.have.length(2)
          requestBody.messages[0].should.deepEqual({role: 'system', content: systemPrompt})
          requestBody.messages[1].should.deepEqual({role: 'user', content: customPrompt})

    it 'should work without system prompt', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          content: [{
            text: 'Translated without system prompt'
          }]
        })
      })

      customPrompt = 'Translate: Hello'

      @claudeTranslator.translateWithCustomPrompt(customPrompt)
        .then (result) ->
          result.should.equal('Translated without system prompt')

          requestBody = JSON.parse(global.fetch.firstCall.args[1].body)
          requestBody.messages.should.have.length(1)
          requestBody.messages[0].should.deepEqual({role: 'user', content: customPrompt})

    it 'should handle API error response', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: false
        json: -> Promise.resolve({
          message: 'API rate limit exceeded'
        })
      })

      @claudeTranslator.translateWithCustomPrompt('Test prompt')
        .then (result) ->
          throw new Error('Should have thrown an error')
        .catch (error) ->
          error.message.should.equal('API rate limit exceeded')

    it 'should handle network error', ->
      @timeout(30000)

      global.fetch = sinon.stub().rejects(new Error('Network error'))

      @claudeTranslator.translateWithCustomPrompt('Test prompt')
        .then (result) ->
          throw new Error('Should have thrown an error')
        .catch (error) ->
          error.message.should.equal('Network error')

    it 'should handle empty custom prompt', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          content: [{
            text: ''
          }]
        })
      })

      @claudeTranslator.translateWithCustomPrompt('')
        .then (result) ->
          result.should.equal('')

    it 'should handle null custom prompt', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          content: [{
            text: 'null handled'
          }]
        })
      })

      @claudeTranslator.translateWithCustomPrompt(null)
        .then (result) ->
          # 验证返回结果
          should.exist(result)
          result.should.equal('null handled')

          # 验证请求体 - null会被传递给content字段
          requestBody = JSON.parse(global.fetch.firstCall.args[1].body)
          content = requestBody.messages[0].content
          if content?
            content.should.equal(null)
          else
            should.not.exist(content)

    it 'should remove trailing newlines from result', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          content: [{
            text: 'Result with newlines\n\n\n'
          }]
        })
      })

      @claudeTranslator.translateWithCustomPrompt('Test')
        .then (result) ->
          result.should.equal('Result with newlines')

    it 'should call use() and release() methods', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          content: [{
            text: 'Success'
          }]
        })
      })

      @claudeTranslator.translateWithCustomPrompt('Test')
        .then (result) =>
          @mockUse.calledOnce.should.be.true()
          @mockRelease.calledOnce.should.be.true()

    it 'should call release() even when error occurs', ->
      @timeout(30000)

      global.fetch = sinon.stub().rejects(new Error('Test error'))

      @claudeTranslator.translateWithCustomPrompt('Test')
        .then (result) ->
          throw new Error('Should have thrown an error')
        .catch (error) =>
          @mockUse.calledOnce.should.be.true()
          @mockRelease.calledOnce.should.be.true()

  describe 'translateWithCustomPrompt parameter validation', ->
    it 'should handle very long custom prompt', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          content: [{
            text: 'Long prompt handled'
          }]
        })
      })

      longPrompt = 'A'.repeat(10000)

      @claudeTranslator.translateWithCustomPrompt(longPrompt)
        .then (result) ->
          result.should.equal('Long prompt handled')

    it 'should handle special characters in prompt', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          content: [{
            text: 'Special chars handled'
          }]
        })
      })

      specialPrompt = 'Test with 中文 and émojis 🚀 and "quotes" and \'apostrophes\''

      @claudeTranslator.translateWithCustomPrompt(specialPrompt)
        .then (result) ->
          result.should.equal('Special chars handled')
          requestBody = JSON.parse(global.fetch.firstCall.args[1].body)
          requestBody.messages[0].content.should.equal(specialPrompt)
