should = require('should')
sinon = require('sinon')
helpers = require('../00_common/helpers')
{createTranslatorManager} = require '../../built/lib/translator/translatorManager'

# ./test.sh -f translator/translatorManager.js -e -b
describe 'TranslatorManager new methods test', ->
  before (done) ->
    @timeout(30000)
    config = helpers.CONFIG()
    @translatorManager = createTranslatorManager(config)
    done()

  describe 'translateWithCustomPrompt method tests', ->
    beforeEach ->
      # Mock all translators - ensure they exist first
      @translatorManager.translators.gemini = @translatorManager.translators.gemini or {}
      @translatorManager.translators.openAI = @translatorManager.translators.openAI or {}
      @translatorManager.translators.claude = @translatorManager.translators.claude or {}
      @translatorManager.translators.azure = @translatorManager.translators.azure or {}

      for service, translator of @translatorManager.translators
        if translator
          # 确保方法存在，如果不存在则添加空方法
          translator.translateWithCustomPrompt = translator.translateWithCustomPrompt or (-> Promise.resolve(''))
          translator.isAvailable = translator.isAvailable or (-> true)

          sinon.stub(translator, 'translateWithCustomPrompt')
          sinon.stub(translator, 'isAvailable').returns(true)

    afterEach ->
      # Restore all stubs
      for service, translator of @translatorManager.translators
        if translator
          translator.translateWithCustomPrompt?.restore?()
          translator.isAvailable?.restore?()

    it 'should translate with custom prompt using first available service', ->
      @timeout(30000)

      @translatorManager.translators.gemini.translateWithCustomPrompt.resolves('Custom translated text')

      translatorPrompts = [{
        service: 'gemini'
        prompt: 'Translate this: Hello world'
        systemPrompt: 'You are a translator'
        templateId: 'test-template-1'
      }]

      result = await @translatorManager.translateWithCustomPrompt(translatorPrompts)
      result.should.deepEqual(['Custom translated text', 'gemini'])
      @translatorManager.translators.gemini.translateWithCustomPrompt.calledOnce.should.be.true()
      args = @translatorManager.translators.gemini.translateWithCustomPrompt.firstCall.args
      args[0].should.equal('Translate this: Hello world')
      args[1].should.equal('You are a translator')

    it 'should work without system prompt', ->
      @timeout(30000)

      openAITranslator = @translatorManager.translators.openAI
      openAITranslator.translateWithCustomPrompt.resolves('No system prompt result')

      translatorPrompts = [{
        service: 'openAI'
        prompt: 'Translate this: Hello'
        systemPrompt: null
        templateId: 'test-template-2'
      }]

      result = await @translatorManager.translateWithCustomPrompt(translatorPrompts)
      result.should.deepEqual(['No system prompt result', 'openAI'])
      openAITranslator.translateWithCustomPrompt.calledOnce.should.be.true()
      args = openAITranslator.translateWithCustomPrompt.firstCall.args
      args[0].should.equal('Translate this: Hello')
      should.not.exist(args[1])

    it 'should fall back to next service if first one fails', ->
      @timeout(30000)

      geminiTranslator = @translatorManager.translators.gemini
      openAITranslator = @translatorManager.translators.openAI
      geminiTranslator.translateWithCustomPrompt.rejects(new Error('Service failed'))
      openAITranslator.translateWithCustomPrompt.resolves('Fallback result')

      translatorPrompts = [
        {
          service: 'gemini'
          prompt: 'Test prompt'
          systemPrompt: 'System prompt'
          templateId: 'test-template-3a'
        }
        {
          service: 'openAI'
          prompt: 'Test prompt'
          systemPrompt: 'System prompt'
          templateId: 'test-template-3b'
        }
      ]

      result = await @translatorManager.translateWithCustomPrompt(translatorPrompts)
      result.should.deepEqual(['Fallback result', 'openAI'])
      geminiTranslator.translateWithCustomPrompt.calledOnce.should.be.true()
      openAITranslator.translateWithCustomPrompt.calledOnce.should.be.true()

    it 'should throw error if all services fail', ->
      @timeout(30000)

      geminiTranslator = @translatorManager.translators.gemini
      openAITranslator = @translatorManager.translators.openAI
      geminiTranslator.translateWithCustomPrompt.rejects(new Error('Service 1 failed'))
      openAITranslator.translateWithCustomPrompt.rejects(new Error('Service 2 failed'))

      translatorPrompts = [
        {
          service: 'gemini'
          prompt: 'Test prompt'
          systemPrompt: 'System prompt'
          templateId: 'test-template-4a'
        }
        {
          service: 'openAI'
          prompt: 'Test prompt'
          systemPrompt: 'System prompt'
          templateId: 'test-template-4b'
        }
      ]

      try
        await @translatorManager.translateWithCustomPrompt(translatorPrompts)
        throw new Error('Should have thrown an error')
      catch error
        error.message.should.equal('Translation failed with all services')

    it 'should handle authentication error', ->
      @timeout(30000)

      authError = new Error('Invalid API key')
      authError.name = 'authentication_error'
      @translatorManager.translators.gemini.translateWithCustomPrompt.rejects(authError)

      translatorPrompts = [{
        service: 'gemini'
        prompt: 'Test prompt'
        systemPrompt: 'System prompt'
        templateId: 'test-template-5'
      }]

      try
        await @translatorManager.translateWithCustomPrompt(translatorPrompts)
        throw new Error('Should have thrown an error')
      catch error
        error.name.should.equal('authentication_error')

    it 'should skip unavailable translators', ->
      @timeout(30000)

      @translatorManager.translators.gemini.isAvailable.returns(false)
      @translatorManager.translators.openAI.translateWithCustomPrompt.resolves('Available service result')

      translatorPrompts = [
        {
          service: 'gemini'
          prompt: 'Test prompt'
          systemPrompt: 'System prompt'
          templateId: 'test-template-6a'
        }
        {
          service: 'openAI'
          prompt: 'Test prompt'
          systemPrompt: 'System prompt'
          templateId: 'test-template-6b'
        }
      ]

      result = await @translatorManager.translateWithCustomPrompt(translatorPrompts)
      result.should.deepEqual(['Available service result', 'openAI'])
      @translatorManager.translators.gemini.translateWithCustomPrompt.called.should.be.false()
      @translatorManager.translators.openAI.translateWithCustomPrompt.calledOnce.should.be.true()

    it 'should handle empty translation result', ->
      @timeout(30000)

      @translatorManager.translators.gemini.translateWithCustomPrompt.resolves('')
      @translatorManager.translators.openAI.translateWithCustomPrompt.resolves('Non-empty result')

      translatorPrompts = [
        {
          service: 'gemini'
          prompt: 'Test prompt'
          systemPrompt: 'System prompt'
          templateId: 'test-template-7a'
        }
        {
          service: 'openAI'
          prompt: 'Test prompt'
          systemPrompt: 'System prompt'
          templateId: 'test-template-7b'
        }
      ]

      result = await @translatorManager.translateWithCustomPrompt(translatorPrompts)
      result.should.deepEqual(['Non-empty result', 'openAI'])
      @translatorManager.translators.gemini.translateWithCustomPrompt.calledOnce.should.be.true()
      @translatorManager.translators.openAI.translateWithCustomPrompt.calledOnce.should.be.true()

  describe 'translateContent method tests', ->
    beforeEach ->
      # Mock translateWithCustomPrompt
      sinon.stub(@translatorManager, 'translateWithCustomPrompt')

    afterEach ->
      @translatorManager.translateWithCustomPrompt.restore()

    it 'should translate content successfully', ->
      @timeout(30000)

      @translatorManager.translateWithCustomPrompt.resolves(['Translated content', 'gemini'])

      content = 'Hello world'
      targetLanguage = 'zh-cn'
      promptTemplates = [{
        _id: 'template1'
        nm: 'Universal Translation'
        ver: '1.0'
        status: 'active'
        scenario: 'universal_translation'
        m_cfg: { m_nm: 'gpt' }
        tpl: {
          main: 'Translate "{text}" from {source_language} to {target_language}'
          sys: 'You are a professional translator'
        }
        vars: [
          { nm: 'text', tp: 'string', req: true }
          { nm: 'source_language', tp: 'string', req: true }
          { nm: 'target_language', tp: 'string', req: true }
        ]
        ts: new Date()
        _mt: new Date()
      }]

      result = await @translatorManager.translateContent(content, targetLanguage, promptTemplates)
      result.should.have.property('success', true)
      result.should.have.property('translatedText', 'Translated content')
      result.should.have.property('usedService', 'gemini')
      @translatorManager.translateWithCustomPrompt.calledOnce.should.be.true()

    it 'should return error for empty content', ->
      result = await @translatorManager.translateContent('', 'zh-cn', [])
      result.should.have.property('success', false)
      result.should.have.property('error', '内容不能为空')

    it 'should return error for empty target language', ->
      result = await @translatorManager.translateContent('Hello', '', [])
      result.should.have.property('success', false)
      result.should.have.property('error', '目标语言不能为空')

    it 'should return error for empty prompt templates', ->
      result = await @translatorManager.translateContent('Hello', 'zh-cn', [])
      result.should.have.property('success', false)
      result.should.have.property('error', '提示词模板列表不能为空')

    it 'should return error for unsupported source language', ->
      promptTemplates = [{
        _id: 'template1'
        status: 'active'
        scenario: 'universal_translation'
      }]

      options = {sourceLanguage: 'unsupported'}
      result = await @translatorManager.translateContent('Hello', 'zh-cn', promptTemplates, options)
      result.should.have.property('success', false)
      result.should.have.property('error', '不支持的源语言: unsupported')

    it 'should return error for unsupported target language', ->
      promptTemplates = [{
        _id: 'template1'
        status: 'active'
        scenario: 'universal_translation'
      }]

      result = await @translatorManager.translateContent('Hello', 'unsupported', promptTemplates)
      result.should.have.property('success', false)
      result.should.have.property('error', '不支持的目标语言: unsupported')

  describe 'filterContent method tests', ->
    beforeEach ->
      # Mock translateWithCustomPrompt
      sinon.stub(@translatorManager, 'translateWithCustomPrompt')

    afterEach ->
      @translatorManager.translateWithCustomPrompt.restore()

    it 'should filter content successfully - pass', ->
      @timeout(30000)

      @translatorManager.translateWithCustomPrompt.resolves(['PASS: Content is appropriate', 'gemini'])

      input = 'This is a normal comment'
      promptTemplates = [{
        _id: 'filter1'
        nm: 'Comment Filter'
        ver: '1.0'
        status: 'active'
        scenario: 'comment_filter'
        m_cfg: { m_nm: 'gpt' }
        tpl: {
          main: 'Filter this content: {text}'
          sys: 'You are a content moderator'
        }
        vars: [
          { nm: 'text', tp: 'string', req: true }
        ]
        ts: new Date()
        _mt: new Date()
      }]

      result = await @translatorManager.filterContent(input, promptTemplates)
      result.should.have.property('success', true)
      result.should.have.property('passed', true)
      result.should.have.property('reason', '内容通过审核')
      result.should.have.property('usedService', 'gemini')
      @translatorManager.translateWithCustomPrompt.calledOnce.should.be.true()

    it 'should filter content successfully - reject', ->
      @timeout(30000)

      @translatorManager.translateWithCustomPrompt.resolves(['REJECT: Contains inappropriate content', 'gemini'])

      input = 'This is inappropriate content'
      promptTemplates = [{
        _id: 'filter1'
        status: 'active'
        scenario: 'comment_filter'
        m_cfg: { m_nm: 'gpt' }
        tpl: { main: 'Filter: {text}' }
      }]

      result = await @translatorManager.filterContent(input, promptTemplates)
      result.should.have.property('success', true)
      result.should.have.property('passed', false)
      result.should.have.property('reason', 'Contains inappropriate content')
      result.should.have.property('usedService', 'gemini')

    it 'should return error for empty input', ->
      result = await @translatorManager.filterContent('', [])
      result.should.have.property('success', false)
      result.should.have.property('error', '输入内容不能为空')

    it 'should return error for empty prompt templates', ->
      result = await @translatorManager.filterContent('Hello', [])
      result.should.have.property('success', false)
      result.should.have.property('error', '提示词模板列表不能为空')

    it 'should handle image input', ->
      @timeout(30000)

      @translatorManager.translateWithCustomPrompt.resolves(['PASS: Image is appropriate', 'gemini'])

      imageInput = {
        type: 'image'
        url: 'https://example.com/image.jpg'
      }
      promptTemplates = [{
        _id: 'filter1'
        status: 'active'
        scenario: 'comment_filter'
        m_cfg: { m_nm: 'gpt' }
        tpl: { main: 'Filter image' }
      }]

      result = await @translatorManager.filterContent(imageInput, promptTemplates)
      result.should.have.property('success', true)
      result.should.have.property('passed', true)

    it 'should handle unsupported input type', ->
      unsupportedInput = {
        type: 'video'
        url: 'https://example.com/video.mp4'
      }
      promptTemplates = [{
        _id: 'filter1'
        status: 'active'
        scenario: 'comment_filter'
      }]

      result = await @translatorManager.filterContent(unsupportedInput, promptTemplates)
      result.should.have.property('success', false)
      result.should.have.property('error', '不支持的输入类型')

    it 'should handle translation service failure in filterContent', ->
      @timeout(30000)

      @translatorManager.translateWithCustomPrompt.rejects(new Error('All services failed'))

      input = 'Test content'
      promptTemplates = [{
        _id: 'filter1'
        status: 'active'
        scenario: 'comment_filter'
        m_cfg: { m_nm: 'gpt' }
        tpl: { main: 'Filter: {text}' }
      }]

      result = await @translatorManager.filterContent(input, promptTemplates)
      result.should.have.property('success', false)
      result.should.have.property('passed', false)
      result.should.have.property('reason', '过滤服务异常，默认拒绝')
      result.should.have.property('error', 'All services failed')

  describe 'edge cases and error handling', ->
    beforeEach ->
      # Mock all translators
      for service, translator of @translatorManager.translators
        if translator
          # 确保方法存在，如果不存在则添加空方法
          translator.translateWithCustomPrompt = translator.translateWithCustomPrompt or (-> Promise.resolve(''))
          translator.isAvailable = translator.isAvailable or (-> true)

          sinon.stub(translator, 'translateWithCustomPrompt')
          sinon.stub(translator, 'isAvailable').returns(true)

    afterEach ->
      # Restore all stubs
      for service, translator of @translatorManager.translators
        if translator
          translator.translateWithCustomPrompt?.restore?()
          translator.isAvailable?.restore?()

    it 'should handle waiting queue when no translators available', ->
      @timeout(5000)

      # Make all translators unavailable
      for service, translator of @translatorManager.translators
        if translator
          translator.isAvailable.returns(false)

      translatorPrompts = [{
        service: 'gemini'
        prompt: 'Test prompt'
        systemPrompt: 'System prompt'
        templateId: 'test-template-8'
      }]

      # Stub the waiting queue mechanism to resolve immediately
      originalMethod = @translatorManager.translateWithCustomPrompt
      @translatorManager.translateWithCustomPrompt = (prompts) ->
        # Simulate the waiting queue logic but resolve immediately
        translatorList = prompts.map (item) -> item.service
        service = @getAvailableTranslator(translatorList)
        if not service?
          # Simulate waiting queue resolution
          service = @getAvailableTranslator(translatorList)
          if not service?
            throw new Error('No translator available after waiting')
        throw new Error('Should not reach here in test')

      try
        result = await @translatorManager.translateWithCustomPrompt(translatorPrompts)
        throw new Error('Should have thrown an error when no translators available')
      catch error
        error.message.should.match(/No translator available after waiting/)
      finally
        # Restore original method
        @translatorManager.translateWithCustomPrompt = originalMethod

    it 'should handle null translator in list', ->
      @timeout(30000)

      @translatorManager.translators.openAI.translateWithCustomPrompt.resolves('Success with valid translator')

      translatorPrompts = [
        {
          service: 'nonexistent'
          prompt: 'Test prompt'
          systemPrompt: 'System prompt'
          templateId: 'test-template-9a'
        }
        {
          service: 'openAI'
          prompt: 'Test prompt'
          systemPrompt: 'System prompt'
          templateId: 'test-template-9b'
        }
      ]

      result = await @translatorManager.translateWithCustomPrompt(translatorPrompts)
      result.should.deepEqual(['Success with valid translator', 'openAI'])
