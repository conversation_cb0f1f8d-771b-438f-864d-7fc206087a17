should = require('should')
sinon = require('sinon')
helpers = require('../00_common/helpers')
RMTranslator = require '../../built/lib/translator/rmTranslator'

# ./test.sh -f translator/rmTranslator.js
describe 'RMTranslator translateWithCustomPrompt test', ->
  before (done) ->
    @timeout(30000)
    config = helpers.CONFIG()
    @rmTranslator = new RMTranslator(
      config.rm?.endpoint or 'http://test-endpoint',
      config.rm?.model or 'test-model',
      config.rm?.prompt or 'test-prompt',
      config.rm?.maxUsage or 10
    )
    done()

  describe 'translateWithCustomPrompt method tests', ->
    beforeEach ->
      @originalFetch = global.fetch
      @mockUse = sinon.stub(@rmTranslator, 'use')
      @mockRelease = sinon.stub(@rmTranslator, 'release')

    afterEach ->
      global.fetch = @originalFetch
      @mockUse.restore()
      @mockRelease.restore()

    it 'should translate with custom prompt successfully', ->
      @timeout(30000)

      # Mock successful API response
      global.fetch = sinon.stub().resolves({
        ok: true
        status: 200
        json: -> Promise.resolve({
          response: 'This is a translated result\n'
        })
      })

      customPrompt = 'Translate this text: Hello world'
      systemPrompt = 'You are a professional translator'

      result = await @rmTranslator.translateWithCustomPrompt(customPrompt, systemPrompt)
      result.should.equal('This is a translated result')
      global.fetch.calledOnce.should.be.true()

      # Verify request structure
      callArgs = global.fetch.firstCall.args
      callArgs[0].should.equal(@rmTranslator.endpoint)

      requestBody = JSON.parse(callArgs[1].body)
      requestBody.should.have.property('model', @rmTranslator.model)
      requestBody.should.have.property('stream', false)

      # RM model combines system and user prompts
      expectedPrompt = "#{systemPrompt}\n\n#{customPrompt}"
      requestBody.should.have.property('prompt', expectedPrompt)

    it 'should work without system prompt', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        status: 200
        json: -> Promise.resolve({
          response: 'Translated without system prompt'
        })
      })

      customPrompt = 'Translate: Hello'

      result = await @rmTranslator.translateWithCustomPrompt(customPrompt)
      result.should.equal('Translated without system prompt')

      requestBody = JSON.parse(global.fetch.firstCall.args[1].body)
      requestBody.should.have.property('prompt', customPrompt)

    it 'should handle HTTP error response', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: false
        status: 500
        json: -> Promise.resolve({
          error: 'Internal server error'
        })
      })

      try
        await @rmTranslator.translateWithCustomPrompt('Test prompt')
        throw new Error('Should have thrown an error')
      catch error
        error.message.should.equal('HTTP error! status: 500')

    it 'should handle network error', ->
      @timeout(30000)

      global.fetch = sinon.stub().rejects(new Error('Network error'))

      try
        await @rmTranslator.translateWithCustomPrompt('Test prompt')
        throw new Error('Should have thrown an error')
      catch error
        error.message.should.equal('Network error')

    it 'should handle empty response', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        status: 200
        json: -> Promise.resolve({})
      })

      try
        await @rmTranslator.translateWithCustomPrompt('Test prompt')
        throw new Error('Should have thrown an error')
      catch error
        error.message.should.equal('Empty response from translation API')

    it 'should handle missing response field', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        status: 200
        json: -> Promise.resolve({
          data: 'some other field'
        })
      })

      try
        await @rmTranslator.translateWithCustomPrompt('Test prompt')
        throw new Error('Should have thrown an error')
      catch error
        error.message.should.equal('Empty response from translation API')

    it 'should handle empty custom prompt', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        status: 200
        json: -> Promise.resolve({
          response: ''
        })
      })

      result = await @rmTranslator.translateWithCustomPrompt('')
      result.should.equal('')

    it 'should handle null custom prompt', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        status: 200
        json: -> Promise.resolve({
          response: 'null handled'
        })
      })

      result = await @rmTranslator.translateWithCustomPrompt(null)
      result.should.equal('null handled')
      requestBody = JSON.parse(global.fetch.firstCall.args[1].body)
      should.equal(requestBody.prompt, null)

    it 'should remove newlines from result', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        status: 200
        json: -> Promise.resolve({
          response: 'Result\nwith\nnewlines\n'
        })
      })

      result = await @rmTranslator.translateWithCustomPrompt('Test')
      result.should.equal('Resultwithnewlines')

    it 'should call use() and release() methods', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        status: 200
        json: -> Promise.resolve({
          response: 'Success'
        })
      })

      result = await @rmTranslator.translateWithCustomPrompt('Test')
      @mockUse.calledOnce.should.be.true()
      @mockRelease.calledOnce.should.be.true()

    it 'should call release() even when error occurs', ->
      @timeout(30000)

      global.fetch = sinon.stub().rejects(new Error('Test error'))

      try
        await @rmTranslator.translateWithCustomPrompt('Test')
        throw new Error('Should have thrown an error')
      catch error
        @mockUse.calledOnce.should.be.true()
        @mockRelease.calledOnce.should.be.true()

  describe 'translateWithCustomPrompt parameter validation', ->
    beforeEach ->
      @originalFetch = global.fetch
      @mockUse = sinon.stub(@rmTranslator, 'use')
      @mockRelease = sinon.stub(@rmTranslator, 'release')

    afterEach ->
      global.fetch = @originalFetch
      @mockUse.restore()
      @mockRelease.restore()

    it 'should handle very long custom prompt', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        status: 200
        json: -> Promise.resolve({
          response: 'Long prompt handled'
        })
      })

      longPrompt = 'A'.repeat(10000)

      result = await @rmTranslator.translateWithCustomPrompt(longPrompt)
      result.should.equal('Long prompt handled')

    it 'should handle special characters in prompt', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        status: 200
        json: -> Promise.resolve({
          response: 'Special chars handled'
        })
      })

      specialPrompt = 'Test with 中文 and émojis 🚀 and "quotes" and \'apostrophes\''

      result = await @rmTranslator.translateWithCustomPrompt(specialPrompt)
      result.should.equal('Special chars handled')
      requestBody = JSON.parse(global.fetch.firstCall.args[1].body)
      requestBody.prompt.should.equal(specialPrompt)

    it 'should handle system prompt with special characters', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        status: 200
        json: -> Promise.resolve({
          response: 'System prompt with special chars handled'
        })
      })

      customPrompt = 'Translate this'
      systemPrompt = 'System prompt with 中文 and émojis 🚀'

      result = await @rmTranslator.translateWithCustomPrompt(customPrompt, systemPrompt)
      result.should.equal('System prompt with special chars handled')
      requestBody = JSON.parse(global.fetch.firstCall.args[1].body)
      requestBody.prompt.should.equal("#{systemPrompt}\n\n#{customPrompt}")
