should = require('should')
sinon = require('sinon')
helpers = require('../00_common/helpers')
DeepSeekTranslator = require '../../built/lib/translator/deepseekTranslator'

# ./test.sh -f translator/deepseekTranslator.js
describe 'DeepSeekTranslator translateWithCustomPrompt test', ->
  before (done) ->
    @timeout(30000)
    config = helpers.CONFIG()
    # 修复构造函数调用，传递正确的参数
    deepseekConfig = config.deepseek ? {}
    @deepseekTranslator = new DeepSeekTranslator(
      deepseekConfig.key ? 'test-api-key',
      deepseekConfig.endpoint ? 'https://api.deepseek.com/v1/chat/completions',
      'deepseek-chat',
      'You are a professional translator',
      deepseekConfig.maxUsage ? 10
    )
    done()

  describe 'translateWithCustomPrompt method tests', ->
    beforeEach ->
      @originalFetch = global.fetch
      @originalAbortSignal = global.AbortSignal
      global.AbortSignal = { timeout: -> {} }
      @mockUse = sinon.stub(@deepseekTranslator, 'use')
      @mockRelease = sinon.stub(@deepseekTranslator, 'release')

    afterEach ->
      global.fetch = @originalFetch
      global.AbortSignal = @originalAbortSignal
      @mockUse.restore()
      @mockRelease.restore()

    it 'should translate with custom prompt successfully', ->
      @timeout(30000)

      # Mock successful API response
      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          choices: [{
            message: {
              content: 'This is a translated result\n\n'
            }
          }]
        })
      })

      customPrompt = 'Translate this text: Hello world'
      systemPrompt = 'You are a professional translator'
      translator = @deepseekTranslator

      @deepseekTranslator.translateWithCustomPrompt(customPrompt, systemPrompt)
        .then (result) ->
          result.should.equal('This is a translated result')
          global.fetch.calledOnce.should.be.true()

          # Verify request structure
          callArgs = global.fetch.firstCall.args
          callArgs[0].should.equal(translator.endpoint)

          requestBody = JSON.parse(callArgs[1].body)
          requestBody.should.have.property('model', translator.model)
          requestBody.should.have.property('stream', false)
          requestBody.messages.should.have.length(2)
          requestBody.messages[0].should.deepEqual({role: 'system', content: systemPrompt})
          requestBody.messages[1].should.deepEqual({role: 'user', content: customPrompt})

    it 'should work without system prompt', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          choices: [{
            message: {
              content: 'Translated without system prompt'
            }
          }]
        })
      })

      customPrompt = 'Translate: Hello'

      @deepseekTranslator.translateWithCustomPrompt(customPrompt)
        .then (result) ->
          result.should.equal('Translated without system prompt')

          requestBody = JSON.parse(global.fetch.firstCall.args[1].body)
          requestBody.messages.should.have.length(1)
          requestBody.messages[0].should.deepEqual({role: 'user', content: customPrompt})

    it 'should handle API error response', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: false
        json: -> Promise.resolve({
          error: {
            message: 'API rate limit exceeded',
            type: 'rate_limit_error'
          }
        })
      })

      @deepseekTranslator.translateWithCustomPrompt('Test prompt')
        .then (result) ->
          throw new Error('Should have thrown an error')
        .catch (error) ->
          error.message.should.equal('API rate limit exceeded')
          error.name.should.equal('rate_limit_error')

    it 'should handle network error', ->
      @timeout(30000)

      global.fetch = sinon.stub().rejects(new Error('Network error'))

      @deepseekTranslator.translateWithCustomPrompt('Test prompt')
        .then (result) ->
          throw new Error('Should have thrown an error')
        .catch (error) ->
          error.message.should.equal('Network error')

    it 'should handle timeout error', ->
      @timeout(30000)

      timeoutError = new Error('The operation was aborted')
      timeoutError.name = 'TimeoutError'
      global.fetch = sinon.stub().rejects(timeoutError)

      @deepseekTranslator.translateWithCustomPrompt('Test prompt')
        .then (result) ->
          throw new Error('Should have thrown an error')
        .catch (error) ->
          error.name.should.equal('TimeoutError')

    it 'should handle authentication error', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: false
        json: -> Promise.resolve({
          error: {
            message: 'Invalid API key',
            type: 'authentication_error'
          }
        })
      })

      @deepseekTranslator.translateWithCustomPrompt('Test prompt')
        .then (result) ->
          throw new Error('Should have thrown an error')
        .catch (error) ->
          error.name.should.equal('authentication_error')

    it 'should handle empty custom prompt', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          choices: [{
            message: {
              content: ''
            }
          }]
        })
      })

      @deepseekTranslator.translateWithCustomPrompt('')
        .then (result) ->
          result.should.equal('')

    it 'should remove trailing newlines from result', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          choices: [{
            message: {
              content: 'Result with newlines\n\n\n'
            }
          }]
        })
      })

      @deepseekTranslator.translateWithCustomPrompt('Test')
        .then (result) ->
          result.should.equal('Result with newlines')

    it 'should call use() and release() methods', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          choices: [{
            message: {
              content: 'Success'
            }
          }]
        })
      })

      @deepseekTranslator.translateWithCustomPrompt('Test')
        .then (result) =>
          @mockUse.calledOnce.should.be.true()
          @mockRelease.calledOnce.should.be.true()

    it 'should call release() even when error occurs', ->
      @timeout(30000)

      global.fetch = sinon.stub().rejects(new Error('Test error'))

      @deepseekTranslator.translateWithCustomPrompt('Test')
        .then (result) ->
          throw new Error('Should have thrown an error')
        .catch (error) =>
          @mockUse.calledOnce.should.be.true()
          @mockRelease.calledOnce.should.be.true()

  describe 'translateWithCustomPrompt parameter validation', ->
    it 'should handle very long custom prompt', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          choices: [{
            message: {
              content: 'Long prompt handled'
            }
          }]
        })
      })

      longPrompt = 'A'.repeat(10000)

      @deepseekTranslator.translateWithCustomPrompt(longPrompt)
        .then (result) ->
          result.should.equal('Long prompt handled')

    it 'should handle special characters in prompt', ->
      @timeout(30000)

      global.fetch = sinon.stub().resolves({
        ok: true
        json: -> Promise.resolve({
          choices: [{
            message: {
              content: 'Special chars handled'
            }
          }]
        })
      })

      specialPrompt = 'Test with 中文 and émojis 🚀 and "quotes" and \'apostrophes\''

      @deepseekTranslator.translateWithCustomPrompt(specialPrompt)
        .then (result) ->
          result.should.equal('Special chars handled')
          requestBody = JSON.parse(global.fetch.firstCall.args[1].body)
          requestBody.messages[0].content.should.equal(specialPrompt)
