###
Generate config of website
read web settings from foder/file, update Site collection

Usage:
  ./start.sh lib/batchBase.coffee batch/webSetting/addNewSite.coffee [filename]

Author:         Julie
Run duration: 1min
Run frequency:  when need
Created:        2022/05/23

# TODO: use json5/toml

NOTE: site.lang 是强制语言，如果site语言不支持，强制使用这个语言， 不是指默认语言
###
Site = COLLECTION 'chome', 'site'
PropertiesCol = COLLECTION 'vow','properties'
PropertiesCol.createIndex( { addr: "text" } )
config = CONFIG(['serverBase'])
debug = DEBUG()

path = require 'path'
fs = require 'fs'
settings = []
pathRoot = path.resolve(__dirname)
directoryPath = config.serverBase?.srcPath+'/webSettingDatas'
debug.debug 'directoryPath',directoryPath
isDev = config.serverBase?.developer_mode
REQUIRED_FILEDS = ['_dm','meta','site','theme','logoPath','termsPath','langPrefix']

if AVGS.length > 3
  filepath = AVGS[3]
  debug.info 'read file path', filepath
  filename = directoryPath+'/' + filepath
  file = fs.readFileSync(filename)
  settings.push JSON.parse(file)
else
  for fileName in fs.readdirSync(directoryPath)
    console.log 'read file', fileName
    if isDev and not /^\_dev\_/.test fileName
      continue
    if fileName isnt '.DS_Store'
      file = fs.readFileSync(directoryPath+'/'+fileName)
      try
        setting = JSON.parse(file)
      catch error
        debug.error error
        EXIT(error)
      for fld in REQUIRED_FILEDS
        if not setting[fld]
          debug.error "missing fld #{fld} for file #{fileName}"
          skip = true
      if not skip
        settings.push setting
main = ()->
  for setting in settings
    debug.info 'update setting', setting
    try
      await Site.updateOne {_dm:setting._dm},{$set:setting},{upsert:true}
    catch error
      if /E11000/.test error.toString()
        # debug.error 'duplicate key', setting._dm
        # Site.updateOne {_dm:setting._dm},{$set:setting},{upsert:true}
        _dm = setting._dm
        delete setting._dm
        ret = await Site.updateOne {_dm},{$set:setting},{upsert:false}
        if ret.result.ok isnt 1
          debug.error 'update failed', setting._dm
          EXIT(ret)
      else
        debug.error error
        EXIT(error)
  EXIT(0)

main()
