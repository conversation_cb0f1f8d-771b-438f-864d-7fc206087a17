###
Advertisement Model
###
debug = DEBUG()

AdsColl = COLLECTION 'chome', 'advertisement'
AdsCollOld = COLLECTION 'chome','ads'

{genNanoId,deepCopyObject} = INCLUDE 'lib.helpers'
{date2num,dateNum2str,dateFormat} = INCLUDE 'lib.helpers_date'
{ handleAdCounts } = INCLUDE 'libapp.advertisement'
{ removeHostFromUrl,replaceRM2REImagePath } = INCLUDE 'libapp.propertyImage'

ensureIndexErr =(err)->
  if err
    debug.error err
    throw err unless err.code is 85
config = CONFIG(['serverBase'])
if config.serverBase?.masterMode
  AdsColl.createIndex {'active':1},ensureIndexErr
  AdsColl.createIndex {'ts':1},ensureIndexErr

MultipartData =
  batch:['weekly','daily','saved'],
  posn:['top','middle','bottom'],
  area:['china','nochina']
  lang:['zh','en','zh-cn','kr']

AdRole={
  banner:'marketAdmin',
  project:'marketAdmin',
  realtor:'marketAdmin',
  mortgage:'marketAdmin',
  edm:['edmAdmin','edmApprove'],
  splash:'splashAdmin'
}

format2Array = (body,update)->
  for key in Object.keys MultipartData
    arr = []
      # 前台传入信息，将选项变成array保存数据库
    for fld in MultipartData[key]
      if body[fld]
        arr.push fld
    update[key] = arr if arr.length
  return update

format2Obj = (doc)->
  for key in Object.keys MultipartData
    # 数据库找出信息，改成obj形式在前台显示
    if doc[key]
      for fld in doc[key]
        doc[fld] = true
      delete doc[key]
  for time in ['st','et']
    if doc[time]
      doc[time] = dateNum2str doc[time]
  return doc
addQueryByStatus = ({query,status})->
  today = date2num(new Date())
  if status is 'A'
    query.pause = {$ne:'A'}
    query.st = {$lte:today}
    query.et = {$gte:today}
  else if status is 'U'
    query.et = {$lt:today}
  return query

class AdvertisementNew
  ###
  根据页面类型判断用户是否有权限
  @param {object} user
  @param {string} type =>'banner','project','realtor','mortgage','edm','splash'
  @return {boolean} true/false
  ###
  @checkRoles:({req,type,user})->
    if type
      if Array.isArray AdRole[type]
        for role in AdRole[type]
          if req.isAllowed role,user
            return true
      else #string
        if req.isAllowed AdRole[type],user
          return true
    return false

  ###
  根据类型获取广告列表
  @param ['banner','project','realtor','mortgage','edm','splash']
  @return {array} [{}]
  ###
  @getList:(type)->
    if typeof type is 'string'
      type = [type]
    list = await AdsColl.findToArray {type:{$in:type}},{sort: {'ts': -1}}
    return [] if not list
    adCounts = list.map (ad)->
      if ad.type is 'splash'
        count = handleAdCounts({ad,type:'splash'})
      else
        count = handleAdCounts({ad,type:'ad'})
      {n: imageSrcName, v: records} = count
      if ad.type is 'edm'
        ad.sendc = records[0].v
        ad.showc = records[1].v
        ad.click = records[2].v
        ad.ratio = records[3].v
      else
        ad.impression = records[0].v
        ad.click = records[1].v
        ad.ratio = records[2].v
      return ad
    return adCounts
  ###
  根据id获取广告列表
  @param 根据id返回数据
  @return {object} {}
  ###
  @findById:(id)->
    return {} if not id
    detail = await AdsColl.findOne {_id:id}
    return format2Obj detail

  ###
    添加或修改广告内容
    @param {object} body
    @param {string} uid
  ###
  @updateOne:(body,uid)->
    id = body._id or genNanoId()
    update = {uid}
    unset = {}
    update = format2Array body,update
    for time in ['st','et']
      if body[time]
        ts = body[time]
        update[time] = date2num(new Date(ts))
    for fld in ['name','name_en','builder','desc_en','desc','dsclsr_en','dsclsr',\
    'noend','prov','city','src','src_en','tl','tl_en','type','tgt','inapp','rm','mls','grp']
      if body[fld]
        update[fld] = body[fld]
        if fld is 'src'
          update[fld] = removeHostFromUrl body[fld]
      else
        unset[fld] = 1
    if not body.ts #新创建的模版
      update.ts = new Date()
    await AdsColl.findOneAndUpdate {_id:id},{$set:update,$unset:unset},{upsert:true,returnDocument:'after'}
    return id

  ###
  查询最近活跃的edm广告
  @param {string} batch 当前发送邮件的batch ('daily','weekly','saved')
  @return {object} 广告数据
    key: grp.lang.area.posn [all/noFollow/notRealtor/realtor].[en/zh].[china/nochina].[bottom/middle/top]
    val:{tgt,desc,dsclsr,src}

    eg:{
      all.en.china.bottom:{tgt,desc,dsclsr,src}
    }
  ###
  @edmFindActiveAdsObj:(batch,sharehost)->
    today = date2num(new Date())
    query = {batch:{$in:[batch]}}
    query = addQueryByStatus {query,status:'A'}
    adObj={}
    formatEdmData=(lang,doc)->
      ret =
        sendc: 0
        posn: {}
      if lang is 'zh'
        ret.desc = doc.desc
        ret.dsclsr = doc.dsclsr
        ret.ad = '广告'
        ret.src = replaceRM2REImagePath doc.src
      else
        ret.desc = doc.desc_en
        ret.dsclsr = doc.dsclsr_en
        ret.ad = 'AD'
        ret.src = replaceRM2REImagePath doc.src_en
      return ret
    adList = await AdsColl.findToArray query,{sort: {'et': -1}}

    return adObj unless adList?.length
    for ad in adList
      adInfoEn = formatEdmData 'en',ad
      adInfoZh = formatEdmData 'zh',ad
      route = {}
      ad.area ?= ['china','nochina']
      lastKey = ''
      grp = ad.grp
      # 这一步在拼path，出现的字段都是必要字段，必须存在，否则在edm中匹配不到对应广告
      for key in ['lang','area','posn']
        # 数据库找出信息，处理id
        route[key] = []
        if not ad[key]
          return throw ad._id + ' ad not has ' + key
        for fld in ad[key]
          if lastKey
            for r in route[lastKey]
              route[key].push "#{r}.#{fld}"
          else
            route[key].push "#{grp}.#{fld}"
        lastKey = key
      # route.pson 中有完整的id
      ids = route.posn
      tgt = sharehost + '/adJump/' + ad._id + '?utm_source=edm'
      for r in ids
        posn = r.substring( r.lastIndexOf('.') + 1)
        urlPosn = "&posn=#{posn}"
        if /en/.test r
          adInfoEn.tgt = tgt + urlPosn
          adObj[r] ?= deepCopyObject adInfoEn
        else
          adInfoZh.tgt = tgt + urlPosn
          adObj[r] ?= deepCopyObject adInfoZh
    return adObj

  ###
  更新广告点击和查看计数
  @param {array} gBannerList 当前活跃的所有广告
  ###
  @updateADCounter:(gBannerList)->
    date = dateFormat new Date(), 'YYYY_MM_DD'
    done = 0
    toDo = gBannerList.length
    for ad in gBannerList
      {vinc,cinc,cipc,showc} = ad
      posn = deepCopyObject ad.posn
      if vinc > 0 or cinc > 0 or cipc > 0 or showc > 0
        id = ad._id
        ad.vinc = 0
        ad.cinc = 0
        ad.cipc = 0
        ad.showc = 0
        ad.posn = {}
        do (id,vinc,cinc,cipc,showc,posn)->
          inc = {vc:vinc,"stat.#{date}vc":vinc,cc:cinc,"stat.#{date}cc":cinc}
          if cipc > 0
            inc.cipc = cipc
            inc["stat.#{date}Cipc"] = cipc
          if ad.type is 'edm'
            # 邮件广告不统计vc字段
            delete inc.vc
            delete inc["stat.#{date}vc"]
            if showc > 0
              inc.showc = showc
              inc["stat.#{date}Showc"] = showc
            if posn and Object.keys(posn).length > 0
              for pson,cnt of posn
                inc["stat.#{date}#{pson}"] = cnt
          await AdsColl.updateOne {_id:id},{$inc:inc}
          return if ++done >= toDo
      else
        return if ++done >= toDo
  ###
  根据id查询广告，更新统计数据
  @param {string} id
  @param {number} sendCnt 当前广告发送的邮件数量
  ###
  @updateEdmADCount:(adsObj)->
    obj = {}
    for k,v of adsObj
      tgt = v.tgt.split('?')[0]
      id = tgt.split('/adJump/')[1]
      {sendc,posn} = v
      obj[id] ?= {sendc:0,posn:{}}
      obj[id].sendc += sendc
      for pson,cnt of posn
        obj[id].posn[pson] ?= 0
        obj[id].posn[pson] += cnt
    date = dateFormat new Date(), 'YYYY_MM_DD'
    done = 0
    toDo = Object.keys(obj).length
    for id,v of obj
      {sendc,posn} = v
      if sendc > 0
        do (id,sendc,posn)->
          inc = {sendc:sendc,"stat.#{date}Sendc":sendc}
          for pson,cnt of posn
            inc["stat.#{date}#{pson}Sendc"] = cnt
          await AdsColl.updateOne {_id:id},{$inc:inc}
          return if ++done >= toDo
      else
        return if ++done >= toDo

  ###
  暂停/开启广告，添加操作历史
  @param {string} id 广告id
  @param {object id} uid 操作用户id
  ###
  @pause:({id,uid,pause})->
    if pause
      pause = 'A'
    else
      pause = 'U'
    hist = {uid,ts:new Date(),pause}
    return await AdsColl.findOneAndUpdate {_id:id},{$push:{pauseHist:hist},$set:{pause}},\
      {upsert:false}

  @getADCountsById:(id)->
    ad = await AdsColl.findOne {_id:id}
    stats = handleAdCounts({ad,type:'ad',fullData:true})
    return stats

  @getADCounts:(status)->
    self = @
    ads = await self.findAdsByStatus(status)
    splash = await self.findSplash()
    # NOTE: new splash mechanism not ready yet
    # splash = await self.findSplashOld()
    adCounts = ads.map (ad)-> handleAdCounts({ad,type:'ad'})
    return adCounts if not splash
    splashCounts = handleAdCounts({ad:splash,type:'splash'})
    return [...adCounts, splashCounts]

  @findAdsByStatus:(status, time)->
    query = addQueryByStatus {query:{},status}
    query.ts = {$gt:time} if time
    ads = await AdsColl.findToArray query, {sort:{ts:-1}}
    return ads
  
  @findSplashOld:()->
    splash = await AdsCollOld.findOne {_id:'splashStat'}
    return splash

  @findSplash:()->
    query = addQueryByStatus {query:{type:'splash'},status:'A'}
    splash = await AdsColl.findOne query,{sort:{ts:-1}}
    return splash
  
  @findWithCounts:(status,time)->
    self = @
    ads = await self.findAdsByStatus status, time
    adCounts = ads.map (ad)->
      count = handleAdCounts({ad,type:'ad'})
      {n: imageSrcName, v: records} = count
      if ad.type is 'edm'
        ad.sendc = records[0].v
        ad.showc = records[1].v
        ad.click = records[2].v
        ad.ratio = records[3].v
      else
        ad.impression = records[0].v
        ad.click = records[1].v
        ad.ratio = records[2].v
      ad.analysis = records
      return ad
    return adCounts

  ###
    根据用户权限获取当前用户可以操作的广告类型
  ###
  @getAdTypesByRole:(req,user)->
    type = []
    if req.isAllowed 'marketAdmin'
      type = ['banner','project','realtor','mortgage']
    if ((req.isAllowed 'edmAdmin',user) or (req.isAllowed 'edmApprove',user))
      type.push 'edm'
    if req.isAllowed 'splashAdmin'
      type.push 'splash'
    return type

  @createProjAd:({req,tgt,_id,LANGUAGE_LIST,uid})->
    existingAd = await AdsColl.findOne {tgt:tgt}
    st = date2num new Date()
    ad = {
      type:'project',
      ts:new Date(),
      st:st,
      lang:LANGUAGE_LIST,
      #city:'',
      desc:'',#抢手楼盘，内部独家户型，比VVIP优先
      inapp:true,
      pid:_id
      uid
      #cOnly:true,
    }
    q = {tgt,type:'project',pid:_id,st:{$lt:st}}
    if existingAd?._id
      # ad._id = existingAd._id
      q = {_id: existingAd._id}
    else
      ad._id = genNanoId()
    # ad.tl = req.param('tl') if req.param('tl')
    ad.src = req.param('src') if req.param('src')
    ad.src_en = req.param('src_en') if req.param('src_en')
    ad.tgt = req.param('tgt') if req.param('tgt')
    ad.builder = req.param('builder') if req.param('builder')
    ad.name = req.param('nm') if req.param('nm')
    ad.name_en = req.param('nm_en') if req.param('nm_en')
    ad.tl = req.param('tl') if req.param('tl')
    ad.tl_en = req.param('tl_en') if req.param('tl_en')
    ad.desc = req.param('desc') if req.param('desc')
    ad.desc_en = req.param('desc_en') if req.param('desc_en')
    ad.linkText = req.param('linkText') if req.param('linkText')
    return await AdsColl.replaceOne q, ad, {upsert:true}

MODEL 'AdvertisementNew',AdvertisementNew
