###
Prompts Model
用于管理LLM提示词模板的完整CRUD操作

功能特性:
- 支持提示词模板的增删改查操作
- 使用upsert模式实现存在则更新、不存在则插入
- 支持多种查询方式：按ID、场景、状态等
- 自动管理创建和修改时间戳
- 完整的数据验证和错误处理

数据结构:
  _id: String - 唯一标识符
  nm: String - 模板名称
  desc: String - 模板描述
  ver: Number - 版本号
  status: String - 状态 (active/inactive)
  scenario: String - 使用场景
  tags: Array - 标签数组
  m_cfg: Object - 模型配置 {m_nm, params}
  tpl: Object - 模板内容 {main, sys}
  vars: Array - 变量定义数组
  ts: Date - 创建时间
  _mt: Date - 修改时间

索引字段:
  scenario: 1 - 场景查询索引
  status: 1 - 状态查询索引
###

# 导入必要的库和工具函数
{isAlphaNumeric} = INCLUDE 'lib.helpers_string'

# 模块级缓存变量
promptTemplatesCache = {}

# 缓存配置
CACHE_TTL = 30 * 60 * 1000 # 30分钟
MAX_CACHE_SIZE = 100 # 最大缓存条目数

# 验证工具函数 - DRY处理，统一验证逻辑
###
验证必填字段
@param {Any} value - 要验证的值
@param {String} fieldName - 字段名称
@throws {Error} 当字段为空时抛出异常
###
validateRequired = (value, fieldName) ->
  throw new Error("#{fieldName}不能为空") unless value

###
验证ID格式
@param {String} id - 要验证的ID
@throws {Error} 当ID为空或格式无效时抛出异常
###
validateId = (id) ->
  throw new Error('ID不能为空') unless id
  throw new Error('ID格式无效') unless 'string' is typeof id

###
验证场景格式
@param {String} scenario - 要验证的场景
@throws {Error} 当场景为空或格式无效时抛出异常
###
validateScenario = (scenario) ->
  throw new Error('场景不能为空') unless scenario
  throw new Error('场景格式无效') unless 'string' is typeof scenario

###
验证状态格式
@param {String} status - 要验证的状态
@throws {Error} 当状态为空或格式无效时抛出异常
###
validateStatus = (status) ->
  throw new Error('状态不能为空') unless status
  throw new Error('状态格式无效') unless isAlphaNumeric(status)

###
验证创建数据的完整性
@param {Object} data - 要验证的数据对象
@throws {Error} 当数据无效时抛出异常
###
validateCreateData = (data) ->
  throw new Error(MSG_STRINGS.BAD_PARAMETER) unless data

  # 验证必填字段
  validateRequired(data._id, 'ID')
  validateRequired(data.nm, '名称')
  validateRequired(data.desc, '描述')
  throw new Error('版本号不能为空') unless data.ver?
  validateRequired(data.status, '状态')
  validateRequired(data.scenario, '场景')
  validateRequired(data.m_cfg, '模型配置')
  validateRequired(data.tpl, '模板内容')

  # 验证字段格式
  validateId(data._id)
  validateScenario(data.scenario)
  validateStatus(data.status)

# 定义数据库集合
PromptsCol = COLLECTION 'chome', 'prompts'

PromptsCol.createIndex {'scenario': 1},{background:true}
PromptsCol.createIndex {'status': 1},{background:true}

class Prompts
  ###
  创建新的提示词模板
  @param {Object} data - 模板数据
  @param {String} data._id - 模板ID (必填)
  @param {String} data.nm - 模板名称 (必填)
  @param {String} data.desc - 模板描述 (必填)
  @param {Number} data.ver - 版本号 (必填)
  @param {String} data.status - 状态 (必填)
  @param {String} data.scenario - 使用场景 (必填)
  @param {Array} data.tags - 标签数组 (可选)
  @param {Object} data.m_cfg - 模型配置 (必填)
  @param {Object} data.tpl - 模板内容 (必填)
  @param {Array} data.vars - 变量定义 (可选)
  @return {Object} 创建结果 {success, data, error}
  @throws {Error} 当数据验证失败时抛出异常
  ###
  @create: (data) ->
    # 数据验证 - 验证失败时抛出异常
    validateCreateData(data)

    # 构建插入数据
    insertData = {
      _id: data._id
      nm: data.nm
      desc: data.desc
      ver: data.ver
      status: data.status
      scenario: data.scenario
      tags: data.tags or []
      m_cfg: data.m_cfg
      tpl: data.tpl
      vars: data.vars or []
      ts: new Date()
      _mt: new Date()
    }

    return await PromptsCol.insertOne(insertData)

  ###
  根据ID查询提示词模板
  @param {String} id - 模板ID
  @return {Object} 查询结果 {success, data, error}
  @throws {Error} 当ID验证失败时抛出异常
  ###
  @findById: (id) ->
    # ID验证 - 验证失败时抛出异常
    validateId(id)

    return await PromptsCol.findOne({_id: id})

  ###
  根据场景查询提示词模板
  @param {String} scenario - 使用场景
  @param {Object} options - 查询选项 {limit, skip, sort}
  @return {Object} 查询结果 {success, data, error}
  @throws {Error} 当场景验证失败时抛出异常
  ###
  @findByScenario: (scenario, options = {}) ->
    # 场景验证 - 验证失败时抛出异常
    validateScenario(scenario)

    opt = {}
    opt.limit = options.limit if options.limit
    opt.skip = options.skip if options.skip
    opt.sort = options.sort or {_mt: -1}

    return await PromptsCol.findToArray({scenario}, opt)

  ###
  根据状态查询提示词模板
  @param {String} status - 状态
  @param {Object} options - 查询选项 {limit, skip, sort}
  @return {Object} 查询结果 {success, data, error}
  @throws {Error} 当状态验证失败时抛出异常
  ###
  @findByStatus: (status, options = {}) ->
    # 状态验证 - 验证失败时抛出异常
    validateStatus(status)

    opt = {}
    opt.limit = options.limit if options.limit
    opt.skip = options.skip if options.skip
    opt.sort = options.sort or {_mt: -1}

    return await PromptsCol.findToArray({status}, opt)

  ###
  查询所有提示词模板
  @param {Object} options - 查询选项 {limit, skip, sort, filter}
  @return {Object} 查询结果 {success, data, error}
  ###
  @findAll: (options = {}) ->
    # 应用查询选项
    opt = {}
    opt.limit = options.limit if options.limit
    opt.skip = options.skip if options.skip
    opt.sort = options.sort or {_mt: -1}
    query = options.filter or {}

    return await PromptsCol.findToArray(query, opt)

  ###
  更新提示词模板
  @param {String} id - 模板ID
  @param {Object} updateData - 更新数据
  @return {Object} 更新结果 {success, data, error}
  @throws {Error} 当参数验证失败时抛出异常
  ###
  @update: (id, updateData) ->
    # 参数验证 - 验证失败时抛出异常
    validateId(id)
    validateRequired(updateData, '更新数据')

    # 构建更新操作
    update = {
      $set: {
        ...updateData
        _mt: new Date()  # 自动更新修改时间
      }
    }

    return await PromptsCol.updateOne({_id: id}, update)

  ###
  删除提示词模板
  @param {String} id - 模板ID
  @return {Object} 删除结果 {success, data, error}
  @throws {Error} 当ID验证失败时抛出异常
  ###
  @delete: (id) ->
    # ID验证 - 验证失败时抛出异常
    validateId(id)

    return await PromptsCol.deleteOne({_id: id})

  ###
  Upsert操作 - 存在则更新，不存在则插入
  @param {Object} data - 模板数据
  @return {Object} 操作结果 {success, data, error}
  @throws {Error} 当参数验证失败时抛出异常
  ###
  @upsert: (data) ->
    # 参数验证 - 验证失败时抛出异常
    validateRequired(data, '数据')
    validateId(data._id)

    # 构建upsert数据
    upsertData = {
      $set: {
        ...data
        _mt: new Date()  # 更新修改时间
      }
      $setOnInsert: {
        ts: new Date()  # 仅在插入时设置创建时间
      }
    }

    return await PromptsCol.updateOne(
      {_id: data._id}
      upsertData
      {upsert: true}
    )

  ###
  检查缓存是否有效
  @param {String} cacheKey - 缓存键
  @return {Boolean} 是否有效
  ###
  @_isCacheValid = (cacheKey) ->
    cached = promptTemplatesCache[cacheKey]
    return false unless cached
    return (Date.now() - cached.timestamp) < cached.ttl

  ###
  更新缓存
  @param {String} cacheKey - 缓存键
  @param {Array} data - 数据
  ###
  @_updateCache = (cacheKey, data) ->
    # 检查缓存大小，必要时清理
    if Object.keys(promptTemplatesCache).length >= MAX_CACHE_SIZE
      Prompts._cleanupCache()

    promptTemplatesCache[cacheKey] = {
      data: data
      timestamp: Date.now()
      ttl: CACHE_TTL
    }
    return

  ###
  清理过期缓存
  ###
  @_cleanupCache = () ->
    now = Date.now()
    for key, cached of promptTemplatesCache
      if (now - cached.timestamp) >= cached.ttl
        delete promptTemplatesCache[key]
    return

  ###
  获取带缓存的提示词模版
  @param {String} scenario - 使用场景
  @param {String} status - 状态，默认为 'active'
  @return {Array} 提示词模版数组
  @throws {Error} 当参数验证失败或数据库操作失败时抛出异常
  ###
  @getCachedPromptTemplates = (scenario, status = 'active') ->
    # 参数验证 - 验证失败时抛出异常
    validateScenario(scenario)
    validateStatus(status)

    # 构建缓存键（简化格式）
    cacheKey = "#{scenario}_#{status}"

    # 检查缓存
    if Prompts._isCacheValid(cacheKey)
      return promptTemplatesCache[cacheKey].data

    # 缓存失效，查询数据库（不使用try-catch，让错误向上抛出）
    query = {scenario, status}
    options = {
      sort: {_mt: -1}
    }

    templates = await PromptsCol.findToArray(query, options)

    # 更新缓存
    Prompts._updateCache(cacheKey, templates)

    return templates

  ###
  清除指定场景的缓存
  @param {String} scenario - 使用场景
  ###
  @clearCache = (scenario) ->
    for key of promptTemplatesCache
      if key.startsWith("#{scenario}_")
        delete promptTemplatesCache[key]
    return

  ###
  清除所有缓存
  ###
  @clearAllCache = () ->
    promptTemplatesCache = {}
    return

# 注册模型
MODEL 'Prompts', Prompts
