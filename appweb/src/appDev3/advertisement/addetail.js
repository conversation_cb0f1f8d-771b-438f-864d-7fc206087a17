var adDetail = {
  computed:{
    calculatedWith:function(){
      return Math.max(400,this.currentAnalysis.length*20)
    },
    activeTime:function(){
      if(!(this.curBanner.st && this.curBanner.et)){
        return ''
      }
      var ts = new Date(this.curBanner.et).getTime() - new Date(this.curBanner.st).getTime()
      return ts/(1000 * 3600 * 24)
    },
    isAdActive:function(){
      if(!(this.curBanner.st && this.curBanner.et)){
        return false
      }
      var edTs = new Date(this.curBanner.et).getTime();
      var stTs = new Date(this.curBanner.st).getTime();
      return (edTs > this.todayts) && (this.todayts > stTs)
    }
  },
  data () {
    return {
      todayts:new Date().getTime(),
      curBanner:{},
      currentAnalysis:[],
      mode:'edit',
      provs:[],
      bannerTypes:[
        {k:'banner',v:'banner'},
        {k:'project',v:'project(index ad)'},
        // {k:'event',v:'event'},
        // {k:'popup',v:'popup'},
        {k:'realtor',v:'realtor'},
        {k:'mortgage',v:'mortgage'}
        // {k:'projectl',v:'project(list)'}
      ],
      dispVar:{
        edmAdmin:false,
        edmApprove:false,
        splashAdmin:false,
        languageList:[],
      },
      datas:[
        "edmAdmin",
        "edmApprove",
        'splashAdmin',
        'adFieldsRequired',
        'languageList',
      ],
      batch:['weekly','daily','saved'],
      batchRunTime:{
        weekly:'Sent at 1am on Saturday',
        daily:'Sent every afternoon at 5pm',
        saved:'Sent every afternoon at 6pm'},
      position:['top','middle','bottom'],
      baseTemp:{
        prov: ['CA'],
        pauseHist:[],
        zh:true,
        en:true,
      },
      adFieldsRequired:{},
      imageSize:{
        splash:{width:1080,height:2340},
        banner:{width:640,height:180},
        edm:'width:height=4:3',
      },
      area:['china','nochina'],
      language:[],
    };
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    this.$getTranslate(this);
    var bus = window.bus, self = this;
    this.getPageData(this.datas, {}, true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      self.adFieldsRequired = d.adFieldsRequired;
      
      if(d.edmAdmin || d.edmApprove){
        self.bannerTypes.push({k:'edm',v:'edm'})
      }
      if(d.splashAdmin){
        self.bannerTypes.push({k:'splash',v:'splash'})
      }
      self.language = d.languageList
    });
    bus.$on('image-selected',({images})=>{
      this.handleImageSelected(images, 'src')
    })
    bus.$on('image-selected-en',({images})=>{
      this.handleImageSelected(images, 'src_en')
    })
    bus.$on('set-city',(d)=>{
      var {o,p_ab} = d.city;
      if (self.curBanner.prov[0] !== p_ab) {
        RMSrv.dialogAlert(`The selected city ${o} is not in the current province`);
        return;
      }
      if (self.curBanner.cities.indexOf(o) == -1) {
        self.curBanner.cities.push(o);
      }
    });
    self.getProvs();
    if(ADDetail._id){
      this.curBanner = ADDetail;
      if(!this.curBanner.prov){
        this.curBanner.prov= ['CA'];
      }
    }else{
      this.curBanner = this.baseTemp
      this.curBanner.type = vars.type
    }
    if(vars.create){
      this.mode = 'create'
    }
  },
  methods: {
    deleteFieldsByType(){
      var deleteFields = this.adFieldsRequired[this.curBanner.type],self=this;
      deleteFields.forEach(f => {
        delete self.curBanner[f]
      });
      self.curBanner.prov = ['CA']
    },
    pause(status){
      var self = this;
      var params = {type:this.curBanner.type,id:this.curBanner._id,pause:status};
      fetchData('/1.5/ads/pause', {body:params},function (err,ret) {
        if(err || ret.e){
          window.bus.$emit('flash-message',err || ret.e)
        }else{
          window.bus.$emit('flash-message','OK')
        }
      });
    },
    checkInappUrl(e){
      if(e.target.checked === true && !/^\//.test(this.curBanner.tgt)){
        RMSrv.dialogAlert('url should start with /')
        setTimeout(() => {
          this.curBanner.inapp = false
        }, 1000);
      }
    },
    selectCity(){
      var cfg = {hide:false};
      var url = '/1.5/city/select?search=1';
      var curProv = this.curBanner.prov[0];
      if (curProv) {
        url += `&prov=${curProv}`;
      }
      RMSrv.getPageContent(url, '#callBackString', cfg, function(val){
        if (val == ':cancel') {
          return;
        }
        try {
          var city = JSON.parse(val);
          window.bus.$emit('set-city',city);
        } catch (e) {
          console.error(e);
        }
      });
    },
    selectProv(prov) {
      var idx = this.curBanner.prov.indexOf(prov);
      if (prov == 'CA') {
        this.curBanner.prov = [prov];
        return;
      } else {
        var idxCanada = this.curBanner.prov.indexOf('CA');
        if (idxCanada > -1) {
          this.curBanner.prov.splice(idxCanada,1);
        }
      }
      if (idx > -1) {
        this.curBanner.prov.splice(idx,1);
      } else {
        this.curBanner.prov.push(prov);
      }
    },
    isProvSelected(prov) {
      return this.curBanner.prov && (this.curBanner.prov.indexOf(prov)>-1);
    },
    getProvs () {
      var self = this;
      fetchData('/1.5/props/provs.json', {body:{}},function (err,ret) {
        if(err || ret.e){
          window.bus.$emit('flash-message',err || ret.e)
        }else{
          if (ret.ok) {
            self.provs = ret.p;
          }
        }
      });
    },
    save(){
      if (!Object.keys(this.curBanner)) {
        return;
      }
      if (this.mode !== 'create' && !this.curBanner._id){
        return;
      }
      var self = this;
      var params = Object.assign(self.curBanner,{mode:self.mode});
      // 必要字段检查
      let necessarySingleKey = ['st','et','tgt','type','grp'];
      necessarySingleKey.forEach(fld => {
        if(!params[fld] || params[fld].length == 0){
          return RMSrv.dialogAlert('广告需要'+fld);
        }
      });
      let necessaryMultipleKey = ['language','area'];
      necessaryMultipleKey.forEach(fld => {
        let hasFld = this[fld].some(value => params.hasOwnProperty(value) && (params[value] == true));
        if(!hasFld){
          return RMSrv.dialogAlert('广告需要'+fld);
        }
      });
      // edm需要检查posn和batch
      if (params.type == 'edm') {
        ['position','batch'].forEach(fld => {
          let hasFld = this[fld].some(value => params.hasOwnProperty(value) && (params[value] == true));
          if(!hasFld){
            return RMSrv.dialogAlert('edm广告需要'+fld);
          }
        });
      }
      // NOTE: dont show project type to agents
      if (params.type == 'project'){
        if (params.grp == 'all' || params.grp == 'realtor'){
          return RMSrv.dialogAlert('不要展示project类型的广告给agent')
        }
      }
      // 验证图片尺寸 - 异步处理
      const validatePromises = [];
      if(params.src) {
        validatePromises.push(this.validateImageSizeAsync(params.src, params.type, 'Image size is not qualified'));
      }
      if(params.src_en) {
        validatePromises.push(this.validateImageSizeAsync(params.src_en, params.type, 'English image size is not qualified'));
      }
      
      // 等待所有图片验证完成
      Promise.all(validatePromises).then((results) => {
        if(results.some(result => !result)) {
          return; // 验证失败，不继续保存
        }
        this.performSave(params);
      }).catch((error) => {
        console.error('图片验证失败:', error);
        window.bus.$emit('flash-message', '图片验证失败');
      });
      return; // 异步处理，先返回
    },
    /**
     * 执行保存操作
     * @param {object} params - 要保存的参数
     */
    performSave(params) {
      // 处理prov字段
      if (!params.prov || params.prov.length == 0 || params.prov.indexOf('CA') > -1) {
        delete params.prov;
      }
      var self = this;
      var url = '/1.5/ads/detail';
      fetchData(url,{body:params},function(err,ret) {
        if (err || ret.e) {
          return window.bus.$emit('flash-message',err || ret.e)
        }
        window.bus.$emit('flash-message','Saved')
        trackEventOnGoogle('new ad model','create or edit')
      })
    },
    openImageModel () {
      this.openImageSelector('image-selected')
    },
    openImageModelEn () {
      this.openImageSelector('image-selected-en')
    },
    /**
     * 通用图片选择处理函数
     * @param {object} images - 图片数据对象
     * @param {string} fieldName - 要设置的字段名
     */
    handleImageSelected(images, fieldName) {
      if(!(Array.isArray(images.picUrls))) return
      const fullUrl = images.picUrls[0]
      this.curBanner[fieldName] = fullUrl
    },
    /**
     * 通用图片选择器函数
     * @param {string} eventName - 要触发的事件名
     */
    openImageSelector(eventName) {
      RMSrv.getPageContent('/1.5/img/insert', '#callBackString',{}, (val)=>{
        try {
          const json = JSON.parse(val);
          if(!json.picUrls || !Array.isArray(json.picUrls)){
            return
          }
          window.bus.$emit(eventName,{images:json})
        } catch (e) {
          console.error(e);
        }
      })
    },
    /**
     * 异步图片尺寸验证函数
     * @param {string} imageSrc - 图片URL
     * @param {string} adType - 广告类型
     * @param {string} errorMessage - 错误提示信息
     * @returns {Promise<boolean>} 返回Promise，验证通过为true，失败为false
     */
    validateImageSizeAsync(imageSrc, adType, errorMessage) {
      return new Promise((resolve, reject) => {
        if(!imageSrc || !this.imageSize[adType]) {
          resolve(true);
          return;
        }
        
        let imageSize = this.imageSize[adType];
        let img = new Image();
        
        // 设置图片加载完成后的处理
        img.onload = () => {
          try {
            if(adType == 'edm'){
              var ratio = img.width / img.height;
              if(Math.abs(ratio - 4/3) > 0.01) { // 允许小的误差
                RMSrv.dialogAlert(errorMessage);
                resolve(false);
                return;
              }
            } else {
              if((img.width != imageSize.width) || (img.height != imageSize.height)){
                RMSrv.dialogAlert(errorMessage);
                resolve(false);
                return;
              }
            }
            resolve(true);
          } catch (error) {
            console.error('图片尺寸验证出错:', error);
            reject(error);
          }
        };
        
        // 设置图片加载失败的处理
        img.onerror = () => {
          console.error('图片加载失败:', imageSrc);
          RMSrv.dialogAlert('图片加载失败，无法验证尺寸');
          resolve(false);
        };
        
        // 设置超时处理
        setTimeout(() => {
          if(img.complete === false) {
            console.warn('图片加载超时:', imageSrc);
            RMSrv.dialogAlert('图片加载超时，无法验证尺寸');
            resolve(false);
          }
        }, 10000); // 10秒超时
        
        // 开始加载图片
        img.src = imageSrc;
      });
    }
  }
};
initUrlVars();
var app = Vue.createApp(adDetail);
app.component('flash-message', flashMessage);
trans.install(app,{ref:Vue.ref})
app.mixin(pageDataMixins);
app.mount('#adDetail');