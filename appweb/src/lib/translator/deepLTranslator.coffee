debugHelper = require '../debug'
debug = debugHelper.getDebugger()
Translator = require './translator'

class DeepLTranslator extends Translator
  constructor: (apiKey, endpoint,maxUsage) ->
    super(apiKey,endpoint,maxUsage)
    @trans_uri = "#{@endpoint}?auth_key=#{@apiKey}"

  translate: (message, fromLang='EN', toLang='ZH') ->
    data = "auth_key=#{@apiKey}&source_lang=#{fromLang}&target_lang=#{toLang}&text=#{message}"
    try
      @use()
      response = await fetch(@trans_uri,
        method: 'POST',
        body: data
      )
      ret = await response.json()
      if response.ok
        translatedContent = ret.translations[0].text
        translatedContentDeleteN = translatedContent.replace(/\n+$/, "")
        return translatedContentDeleteN
      else
        throw new Error response.data
    catch error
      debug.error "DeepL Translation API Error:", error.message
      throw error
    finally
      @release()
    # request.post TRANS_URI, {form:data},(err, response, ret)->
      # 400	Bad request. Please check error message and your parameters.
      # 403	Authorization failed. Please supply a valid auth_key parameter.
      # 404	The requested resource could not be found.
      # 413	The request size exceeds the limit.
      # 414	The request URL is too long. You can avoid this error by using a POST request instead of a GET request.
      # 429	Too many requests. Please wait and resend your request.
      # 456	Quota exceeded. The character limit has been reached.
      # 503	Resource currently unavailable. Try again later.
      # 5**	Internal error

  ###*
   * 使用自定义提示词进行翻译
   * @param {String} customPrompt - 自定义提示词内容
   * @param {String} systemPrompt - 系统提示词（可选，DeepL不支持系统提示词，会被忽略）
   * @param {String} fromLang - 源语言（可选，用于兼容性）
   * @param {String} toLang - 目标语言（可选，用于兼容性）
   * @returns {Promise<String>} 翻译结果
  ###
  translateWithCustomPrompt: (customPrompt, systemPrompt = null) ->
    # DeepL不支持自定义提示词，这里返回错误信息
    # 实际使用中应该使用其他支持LLM的翻译器
    throw new Error('DeepL does not support custom prompts. Please use AI-based translators like OpenAI, Claude, or Gemini.')

module.exports = DeepLTranslator