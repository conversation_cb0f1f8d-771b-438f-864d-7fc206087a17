TextTranslationClient = require('@azure-rest/ai-translation-text').default
debugHelper = require '../debug'
debug = debugHelper.getDebugger()
Translator = require './translator'

class AzureTranslator extends Translator
  constructor: (apiKey, endpoint, region, maxUsage) ->
    super(apiKey, endpoint, maxUsage)
    @translationClient = new TextTranslationClient(@endpoint, {key: @apiKey,region:region})

  translate: (message, fromLang='en', toLang='zh-Hans') ->
    requestBody = [
      {
        'text': message
      }
    ]

    try
      @use()
      translateResponse = await @translationClient.path('/translate').post(
        body: requestBody,
        queryParameters: {
          to: toLang,
          from: fromLang,
        }
      )
      if Array.isArray(translateResponse.body) and translateResponse.body[0]?
        translations = translateResponse.body[0]
      else
        translations = translateResponse.body
      return translations.translations?[0]?.text
    catch err
      debug.error 'msTranslate error:', err.toString(), '\ntoTranslate:', message
      throw new Error 'Translate failed'
      # NOTE: various 400 errors related to request body, Error response: 400 Bad Request { code: 400000, message: 'One of the request inputs is not valid.' }
    finally
      @release()

  ###*
   * 使用自定义提示词进行翻译
   * @param {String} customPrompt - 自定义提示词内容
   * @param {String} systemPrompt - 系统提示词（可选，Azure不支持系统提示词，会被忽略）
   * @param {String} fromLang - 源语言（可选，用于兼容性）
   * @param {String} toLang - 目标语言（可选，用于兼容性）
   * @returns {Promise<String>} 翻译结果
  ###
  translateWithCustomPrompt: (customPrompt, systemPrompt = null) ->
    # Azure Translator不支持自定义提示词，这里返回错误信息
    # 实际使用中应该使用其他支持LLM的翻译器
    throw new Error('Azure Translator does not support custom prompts. Please use AI-based translators like OpenAI, Claude, or Gemini.')

module.exports = AzureTranslator