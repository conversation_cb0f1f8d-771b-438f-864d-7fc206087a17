debugHelper = require '../debug'
debug = debugHelper.getDebugger()
AITranslator = require './AITranslator'


class OpenAITranslator extends AITranslator
  constructor: (apiKey, endpoint, model, prompt, orgID, projectID,maxUsage) ->
    super(apiKey,endpoint,model,prompt,maxUsage)
    @orgID=orgID
    @projectID=projectID
    
  translate: (message, fromLang="English", toLang="Chinese") ->
    data = {
      model: @model
      messages: [
        { role: "user", content: "#{@prompt} #{fromLang} to #{toLang}: #{message}" }
      ]
      stream: false
    }
    try
      @use()
      response = await fetch(@endpoint,
        method: 'POST',
        headers: {
          "Content-Type": "application/json"
          "Authorization": "Bearer #{@apiKey}"
          "OpenAI-Organization": @orgID
          "OpenAI-Project": @projectID
        },
        body: JSON.stringify(data)
      )
      ret = await response.json()
      debug.debug ret
      if response.ok
        translatedContent = ret.choices[0].message.content
        translatedContentDeleteN = translatedContent.replace(/\n+$/, "")
        return translatedContentDeleteN
      else
        throw new Error(ret.message or ret)
    catch error
      debug.error "OpenAI Translation API Error:", {
        error: {
          name: error.name
          message: error.message
          stack: error.stack
          details: error  # 完整错误对象
        }
        response: ret  # API 返回的原始响应
      }
      throw error
    finally
      @release()

  ###*
   * 使用自定义提示词进行翻译
   * @param {String} customPrompt - 自定义提示词内容
   * @param {String} systemPrompt - 系统提示词（可选）
   * @param {String} fromLang - 源语言（可选，用于兼容性）
   * @param {String} toLang - 目标语言（可选，用于兼容性）
   * @returns {Promise<String>} 翻译结果
  ###
  translateWithCustomPrompt: (customPrompt, systemPrompt = null) ->
    messages = []

    # 添加系统提示词（如果提供）
    if systemPrompt
      messages.push { role: "system", content: systemPrompt }

    # 添加用户提示词
    messages.push { role: "user", content: customPrompt }

    data = {
      model: @model
      messages: messages
      stream: false
    }

    try
      @use()
      response = await fetch(@endpoint,
        method: 'POST',
        headers: {
          "Content-Type": "application/json"
          "Authorization": "Bearer #{@apiKey}"
          "OpenAI-Organization": @orgID
          "OpenAI-Project": @projectID
        },
        body: JSON.stringify(data)
      )
      ret = await response.json()
      debug.debug ret
      if response.ok
        translatedContent = ret.choices[0].message.content
        translatedContentDeleteN = translatedContent.replace(/\n+$/, "")
        return translatedContentDeleteN
      else
        throw new Error(ret.message or ret)
    catch error
      debug.error "OpenAI Custom Prompt Translation API Error:", {
        error: {
          name: error.name
          message: error.message
          stack: error.stack
          details: error
        }
        response: ret
      }
      throw error
    finally
      @release()

module.exports = OpenAITranslator