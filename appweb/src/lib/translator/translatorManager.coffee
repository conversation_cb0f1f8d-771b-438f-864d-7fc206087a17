debugHelper = require '../debug'
debug = debugHelper.getDebugger()
AzureTranslator = require './azureTranslator'
DeepLTranslator = require './deepLTranslator'
DeepSeekTranslator = require './deepseekTranslator'
OpenAITranslator = require './openAITranslator'
GeminiTranslator = require './geminiTranslator'
ClaudeTranslator = require './claudeTranslator'
GrokTranslator = require './grokTranslator'
RMTranslator = require './rmTranslator'
llmHelper = require '../../libapp/llmTranslationHelper'

PROMPT="You are a real estate expert. The following content is about Canada real estate listing. Please translate all of the following content, but do not translate place names. Only return the translated text, do not include any other text. Translate the following text from English to Chinese."
MODEL_DEEPSEEK="deepseek-chat"
MODEL_OPENAI="gpt-4o-mini"
MODEL_GEMINI="gemini-2.0-flash-lite" #"gemini-1.5-flash"
MODEL_CLAUDE="claude-3-haiku-20240307"
MODEL_GROK="grok-2-latest"
MODEL_RM="gemma3:12b"
TRANSLATORLIST = ['gemini', 'openAI', 'claude', 'azure'] # grok can not be used in app now because of the request limit

LANGUAGE_OBJ = [
  {k: 'en', v:'English', AIValue: 'English', azureValue: 'en', deepLValue:'EN'},
  {k: 'zh-cn', v:'简体中文', AIValue: 'Chinese', azureValue: 'zh-Hans', deepLValue:'ZH'},
  {k: 'zh', v:'繁体中文', AIValue: 'Traditional Chinese', azureValue: 'zh-Hant', deepLValue:'ZH-HANT'},
  {k: 'kr', v:'한국어', AIValue: 'Korean', azureValue: 'ko', deepLValue:'KO'},
]

findLanguageValue = (k, service) ->
  result = LANGUAGE_OBJ.find (language) -> language.k is k
  if result?
    switch service
      when 'azure' then result.azureValue
      when 'deepl' then result.deepLValue
      when 'deepseek', 'openAI', 'gemini', 'claude', 'grok', 'rm' then result.AIValue
      else throw new Error "Service '#{service}' not supported"
  else
    throw new Error "Key '#{k}' not found in LANGUAGE_OBJ"

class TranslatorManager
  constructor: (config) ->
    debug.debug 'TranslatorManager config:', config
    @translators = {}
    @waitingQueue = []
    
    # Initialize translators with their max usage limits from config
    if config?.azure?.subscriptionKey
      @translators['azure'] = new AzureTranslator(config.azure.subscriptionKey, config.azure.endpoint, config.azure.region, config.azure.maxUsage)
      
    if config?.deepL?.key
      @translators['deepl'] = new DeepLTranslator(config.deepL.key, config.deepL.endpoint, config.deepL.maxUsage)
      
    if config?.deepseek?.key
      @translators['deepseek'] = new DeepSeekTranslator(config.deepseek.key, config.deepseek.endpoint, MODEL_DEEPSEEK, PROMPT, config.deepseek.maxUsage)
      
    if config?.openAI?.key
      @translators['openAI'] = new OpenAITranslator(config.openAI.key, config.openAI.endpoint, MODEL_OPENAI, PROMPT, config.openAI.orgID, config.openAI.projectID, config.openAI.maxUsage)
      
    if config?.gemini?.key
      @translators['gemini'] = new GeminiTranslator(config.gemini.key, MODEL_GEMINI, PROMPT, config.gemini.maxUsage)
      
    if config?.claude?.key
      @translators['claude'] = new ClaudeTranslator(config.claude.key, config.claude.endpoint, MODEL_CLAUDE, PROMPT, config.claude.maxUsage)
      
    if config?.grok?.key
      debug.debug 'Initializing Grok translator with key:', config.grok.key
      @translators['grok'] = new GrokTranslator(config.grok.key, config.grok.endpoint, MODEL_GROK, PROMPT, config.grok.maxUsage)
      
    if config?.rm?.endpoint
      @translators['rm'] = new RMTranslator(config.rm.endpoint, MODEL_RM, PROMPT, config.rm.maxUsage)

  getAvailableTranslator: (translatorList) ->
    debug.debug '###getAvailableTranslator',translatorList
    # Check translators in the order of translatorList
    for service in translatorList
      translator = @translators[service]
      if translator?
        debug.debug '###service',service,translator.usageCount, translator.maxUsage, translator.isAvailable()
        if translator.isAvailable()
          return service
      else
        debug.debug "###service #{service} not found"
    return null

  processWaitingQueue: ->
    debug.debug '###processWaitingQueue',@waitingQueue.length
    if @waitingQueue.length > 0
      # Get the first waiting resolve function and execute it
      resolve = @waitingQueue.shift()
      resolve()

  translate: (message, translatorList=TRANSLATORLIST, fromLangKey='en', toLangKey='zh-cn') ->
    attemptTranslation = (index) =>
      if index >= translatorList.length
        throw new Error "Translation failed with all services"

      service = translatorList[index]
      translator = @translators[service]

      if translator?
        try
          fromLang = findLanguageValue(fromLangKey, service)
          toLang = findLanguageValue(toLangKey, service)
          
          # Check if translator is available
          if not translator.isAvailable()
            debug.debug "Translator #{service} is at max usage, trying next service"
            return attemptTranslation(index + 1)
            
          debug.debug "Using translator #{service}"
          
          result = await translator.translate(message, fromLang, toLang)
          
          # Process waiting queue after translation
          @processWaitingQueue()
          
          if result and result isnt ''
            return [result, service]
            
          debug.warn "Empty translation result from #{service}, trying next service"
          return attemptTranslation(index + 1)
        catch error
          # Process waiting queue on error
          @processWaitingQueue()
          
          if error.name is 'authentication_error'
            debug.error "#{service} Translation API Authentication Error"
            throw error
          else
            debug.error "Error using #{service} translator:", {
              errorName: error.name
              errorMessage: error.message
              errorDetails: error
              service: service
            }
            return attemptTranslation(index + 1)
      else
        debug.error "Translator service not found: #{service}"
        return attemptTranslation(index + 1)

    # Try to get an available translator
    service = @getAvailableTranslator(translatorList)
    if not service?
      # If no translator is available, add to waiting queue
      debug.debug "No translator available, adding to waiting queue"
      await new Promise (resolve) =>
        @waitingQueue.push resolve
      # After being woken up, try again
      service = @getAvailableTranslator(translatorList)
      if not service?
        throw new Error "No translator available after waiting"

    # Start translation with the available translator
    return await attemptTranslation(translatorList.indexOf(service))

  ###*
   * 使用自定义提示词进行翻译
   * @param {Array} translatorPrompts - 包含translator-prompt映射的数组
   *   格式: [{service: 'openAI', prompt: '...', systemPrompt: '...', templateId: '...'}]
   * @returns {Promise<Array>} [翻译结果, 使用的服务名称, 使用的模板ID]
  ###
  translateWithCustomPrompt: (translatorPrompts) ->
    unless Array.isArray(translatorPrompts) and translatorPrompts.length > 0
      throw new Error('translatorPrompts must be a non-empty array')

    # 从数组中提取translatorList用于可用性检查
    translatorList = translatorPrompts.map (item) -> item.service

    attemptTranslation = (index) =>
      if index >= translatorPrompts.length
        throw new Error('Translation failed with all services')

      promptInfo = translatorPrompts[index]
      service = promptInfo.service
      translator = @translators[service]

      if translator?
        try
          # 检查翻译器是否可用
          if not translator.isAvailable()
            debug.debug "Translator #{service} is at max usage, trying next service"
            return attemptTranslation(index + 1)

          debug.debug "Using translator #{service} with custom prompt"

          # 使用该translator对应的prompt调用翻译方法
          result = await translator.translateWithCustomPrompt(promptInfo.prompt, promptInfo.systemPrompt)

          # 处理等待队列
          @processWaitingQueue()

          if result and result isnt ''
            return [result, service]

          debug.warn "Empty translation result from #{service}, trying next service"
          return attemptTranslation(index + 1)
        catch error
          # 处理等待队列
          @processWaitingQueue()

          if error.name is 'authentication_error'
            debug.error "#{service} Custom Prompt Translation API Authentication Error"
            throw error
          else
            debug.error "Error using #{service} translator with custom prompt:", {
              errorName: error.name
              errorMessage: error.message
              errorDetails: error
              service: service
            }
            return attemptTranslation(index + 1)
      else
        debug.error "Translator service not found: #{service}"
        return attemptTranslation(index + 1)

    # 尝试获取可用的翻译器
    service = @getAvailableTranslator(translatorList)
    if not service?
      # If no translator is available, add to waiting queue
      debug.debug 'No translator available for custom prompt, adding to waiting queue'
      await new Promise (resolve) =>
        @waitingQueue.push resolve
      # After being woken up, try again
      service = @getAvailableTranslator(translatorList)
      if not service?
        throw new Error('No translator available after waiting')

    # 找到可用服务在translatorPrompts中的索引
    serviceIndex = translatorPrompts.findIndex (item) -> item.service is service
    if serviceIndex is -1
      serviceIndex = 0  # 如果没找到，从第一个开始

    # 开始使用可用的翻译器进行翻译
    return await attemptTranslation(serviceIndex)

  ###*
   * 使用LLM模板进行内容翻译
   * @param {String} content - 需要翻译的内容
   * @param {String} targetLanguage - 目标语言代码
   * @param {Array} promptTemplates - 提示词模板列表
   * @param {Object} options - 可选参数 {sourceLanguage, context, scenario}
   * @returns {Promise<Object>} 翻译结果 {success: boolean, translatedText: string, usedService: string, error: string}
  ###
  translateContent: (content, targetLanguage, promptTemplates, options = {}) ->
    # 参数验证
    return {success: false, error: '内容不能为空'} unless content
    return {success: false, error: '目标语言不能为空'} unless targetLanguage
    return {success: false, error: '提示词模板列表不能为空'} unless promptTemplates and Array.isArray(promptTemplates) and promptTemplates.length > 0

    # 设置默认参数
    sourceLanguage = options.sourceLanguage or 'en'
    context = options.context or ''
    scenario = options.scenario or 'universal_translation'

    # 验证语言代码
    unless llmHelper.isValidLanguageCode(sourceLanguage)
      return {success: false, error: "不支持的源语言: #{sourceLanguage}"}
    unless llmHelper.isValidLanguageCode(targetLanguage)
      return {success: false, error: "不支持的目标语言: #{targetLanguage}"}

    # 获取排序后的提示词模板列表（支持容错机制）
    sortedTemplates = llmHelper.getSortedPromptTemplates(content, scenario, promptTemplates)
    unless sortedTemplates.length > 0
      return {success: false, error: "未找到合适的#{scenario}翻译模板"}

    # 构建提示词变量
    variables = {
      text: content
      source_language: llmHelper.getLanguageDisplayName(sourceLanguage)
      target_language: llmHelper.getLanguageDisplayName(targetLanguage)
      context: context
    }

    # 构建translator-prompt映射数组
    translatorPrompts = []
    attemptErrors = []

    for template in sortedTemplates
      # 构建完整提示词
      promptResult = llmHelper.buildTranslationPrompt(template, variables)
      unless promptResult.success
        attemptErrors.push "模板#{template._id}: 构建提示词失败 - #{promptResult.error}"
        continue

      # 获取模型名称
      modelName = template.m_cfg?.m_nm
      if service = llmHelper.getModelName(modelName)
        # 添加到translator-prompt映射数组
        translatorPrompts.push {
          service: service
          prompt: promptResult.prompt
          systemPrompt: promptResult.systemPrompt
          templateId: template._id
        }

    # 如果没有有效的模板，返回错误
    if translatorPrompts.length is 0
      return {
        success: false
        error: "没有有效的翻译模板: #{attemptErrors.join('; ')}"
      }

    try
      # 调用自定义提示词翻译服务，传入translator-prompt映射数组
      [translatedText, usedService] = await @translateWithCustomPrompt(translatorPrompts)
    catch error
      debug.error 'LLM翻译失败:', error
      return {
        success: false
        error: error.message
      }
    # 翻译成功，返回结果
    return {
      success: true
      translatedText: translatedText
      usedService: usedService
    }

  ###*
   * 使用LLM模板进行内容过滤
   * @param {String|Object} input - 待过滤的内容（文本或图片数据）
   * @param {Array} promptTemplates - 提示词模板列表
   * @param {Object} options - 可选参数 {context, filterType}
   * @returns {Promise<Object>} 过滤结果 {success: boolean, passed: boolean, reason: string, usedService: string, error: string}
  ###
  filterContent: (input, promptTemplates, options = {}) ->
    # 参数验证
    return {success: false, error: '输入内容不能为空'} unless input
    return {success: false, error: '提示词模板列表不能为空'} unless promptTemplates and Array.isArray(promptTemplates) and promptTemplates.length > 0

    # 设置默认参数
    context = options.context or ''
    filterType = options.filterType or 'comment_filter'

    # 获取排序后的过滤模板列表（支持容错机制）
    sortedTemplates = llmHelper.getSortedFilterTemplates(filterType, promptTemplates)
    unless sortedTemplates.length > 0
      return {success: false, error: "未找到合适的#{filterType}过滤模板"}

    # 构建提示词变量
    variables = {
      context: context
    }

    # 根据输入类型设置相应变量
    if typeof input is 'string'
      variables.text = input
    else if typeof input is 'object' and input.type is 'image'
      variables.image = input
    else
      return {success: false, error: '不支持的输入类型'}

    # 构建translator-prompt映射数组
    translatorPrompts = []
    attemptErrors = []

    for template in sortedTemplates
      # 构建完整提示词
      promptResult = llmHelper.buildFilterPrompt(template, variables)
      unless promptResult.success
        attemptErrors.push "模板#{template._id}: 构建过滤提示词失败 - #{promptResult.error}"
        continue
      # 获取模型名称
      modelName = template.m_cfg?.m_nm
      if service = llmHelper.getModelName(modelName)
        # 添加到translator-prompt映射数组
        translatorPrompts.push {
          service: service
          prompt: promptResult.prompt
          systemPrompt: promptResult.systemPrompt
          templateId: template._id
        }

    # 如果没有有效的模板，返回错误
    if translatorPrompts.length is 0
      return {
        success: false
        passed: false
        reason: '过滤服务异常，默认拒绝'
        error: "没有有效的过滤模板: #{attemptErrors.join('; ')}"
      }

    try
      # 调用AI模型进行过滤，传入translator-prompt映射数组
      [filterResult, usedService] = await @translateWithCustomPrompt(translatorPrompts)
    catch error
      debug.error 'LLM过滤失败:', error
      # 过滤失败时默认不通过审核
      return {
        success: false
        passed: false
        reason: '过滤服务异常，默认拒绝'
        error: error.message
      }

    # 解析过滤结果
    parseResult = llmHelper.parseFilterResult(filterResult)

    return {
      success: true
      passed: parseResult.passed
      reason: parseResult.reason
      usedService: usedService
    }

exports.createTranslatorManager = (config)-> new TranslatorManager(config)
exports.findLanguageValue = findLanguageValue
