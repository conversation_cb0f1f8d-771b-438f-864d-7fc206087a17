libproperties = INCLUDE 'libapp.properties'
listingPicUrls = libproperties.listingPicUrls
getProjectLists = DEF 'getProjectLists'
# searchSchools = DEF 'searchSchools'
getRecommendListings = DEF 'getRecommendListings'
getBanners = DEF 'getBanners'
filterBanners = DEF 'filterBanners'
helpers = INCLUDE 'lib.helpers'
ProvAndCity = MODEL 'ProvAndCity'
provAbbrName = ProvAndCity.getProvAbbrName
config = CONFIG(['share'])
debug = DEBUG()

ADDSTDFN 'searchProjects',{needReq:true},(opt,cb)->
  req = opt.req
  projOpt =
    mode:'new',
    nm:opt.s,
    city:'',
    prov:'',
    tp1:'All',
    limit:10,
    req:req
  getProjectLists projOpt, (err, projs)->
    return cb err,null if err
    for p in projs
      # console.log '++++++',p.city, req.locale(), req.l10n(p.city)
      p.prov = req.l10n p.prov
      p.city = req.l10n p.city
    cb null,{ok:1,projs}

# ADDSTDFN 'searchSchools',(opt,cb)->
#   req = {body:{'tp':'all','city':'','nm':opt.s,'sort':{'tp':'nm'},'page':0,'limit':10}}
#   searchSchools req,(err,schs)->
#     return cb err,null if err
#     cb null,{ok:1,schs}

ADDSTDFN 'getAds',{needReq:true,userObject:true},(opt,cb)->
  req = opt.req
  tp = opt.tp
  # flag from new native, if no wechat dont show avt
  hasWechat = if opt.hasWechat? then opt.hasWechat else true
  # console.log '+++++hasWechat',hasWechat
  user = opt.user
  if user?.hasWechat?
    hasWechat = hasWechat and user.hasWechat
  switch (tp)
    when 'tl'
      # NOTE: DDF prop.city not translated, because DDF not allowed to translate prop fields
      getRecommendListings req,null,(ret)->
        result = ret.l
        for tl in result
          propImg = libproperties.thumbUrlReplace(tl, req.isChinaIP(), config.share?.hostNameCn)
          if propImg?.img
            tl.img = propImg.img # native使用的是img字段
          tl.prov = provAbbrName(tl.prov_en) if tl.prov_en
          if price = (tl.lp or tl.lpr)
            tl.priceValStrRed = libproperties.currencyFormat price,'$',0
          else
            debug.error {msg:"getAds: No rental price or selling price",_id:tl._id}
            tl.priceValStrRed = ''
          if /^RM1/.test tl.id
            scope = {ml_num:tl.sid}
            tl.img = libproperties.convert_rm_imgs(scope,tl.pic,'reset')[0]
          if tl.adrltr and not hasWechat
            delete tl.adrltr
        return cb null,{ok:1,result}
    when 'proj'
      lang = req.locale()
      result = helpers.shuffle(filterBanners(req, user, getBanners(lang,'project',true), true))
      # NOTE: must copy otherwise we change obj in banner cache
      result = helpers.deepCopyObject result,2,true
      # TODO: modified orig object
      # result = result.slice(0)
      # console.log result
      for p in result
        delete p.stat
        delete p.analysis
        delete p.spuids
        # NOTE: already toString in banner.coffee init reloadAds
        # p._id = p._id.toString() if p._id
        p.p = 'project'
        p.city = req.l10n(p.proj_city) if p.proj_city
        p.prov = provAbbrName(p.proj_prov) if p.proj_prov
        if not hasWechat
          p.avt = '/img/small-transparent-image.png'
          # delete p.avt
        if lang not in ['zh-cn', 'zh']
          for k in ['src', 'desc', 'name']
            p[k] = p[k + '_en'] or ''
            delete p[k + '_en']

      return cb null,{ok:1,result}
    else
      result = []
      return cb null,{ok:1,result}

# NOTE: in new native will jump to new native page
APP '1.5',true
# GET 'autocomplete',(req,res)->
#   res.send {ok:1}

# POST 'searchSchools',(req,resp)->
#   body = req.body
#   req.callStdFn 'findSchools',body,(err,retObj)->
#     return resp.send {ok:0,e:err} if err
#     resp.send retObj


GET 'autoCompleteSearch',(req,resp)->
  lang = req.locale()
  searchPlaceHolder = req.l10n('Enter address, MLS') + '&#174;'
  resp.ckup 'autoCompleteSearch', {lang,searchPlaceHolder}, '_', {noAngular:1, noAppcss:1, noRatchetcss:1, bootstrap:1}

VIEW 'autoCompleteSearch',->
  # css '/css/font-awesome.min.css'
  div id:'autocompleteSearchBar',->
    text """<Search></Search>"""
  coffeejs {vars:{lang:@lang,searchPlaceHolder:@searchPlaceHolder}}, ->
    null
  js '/js/entry/commons.js'
  js '/web/packs/autoCompleteSearch.js'