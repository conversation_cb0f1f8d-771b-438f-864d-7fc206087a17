#promote
config = CONFIG(['share','s3config'])
debug = DEBUG()
helpers = INCLUDE 'lib.helpers'
libUser = INCLUDE 'libapp.user'
objectCache = INCLUDE 'libapp.objectCache'
{respError} = INCLUDE 'libapp.responseHelper'

gShareHost = config.share.host or 'https://realmaster.com'
BlockPhoneModel = MODEL 'BlockPhone'
# User = COLLECTION 'chome', 'user'

# UserListings = COLLECTION 'chome', 'user_listing'

Properties = MODEL 'Properties'
getConfig = DEF 'getConfig'


promoTo3pty = SERVICE 'promoTo3pty'

s3config = config.s3config

# isAllowed = DEF '_access_allowed'
# prepSocket = DEF 'prepSocket'
# getWXConfig = DEF 'getWXConfig'
UserModel = MODEL 'User'
createUniqueListingID = DEF 'createUniqueListingID'
mapServer = INCLUDE 'lib.mapServer'
getGoogleAPILink = mapServer.getGoogleAPILink
TRUSTED_ASSIGN_CITY_LIST = 'trustedAssignCityList'
TRUSTED_ASSIGN_STYLE_LIST = 'trustedAssignStyleList'
TRUSTEDASSIGM = 'trustedAssigm'

ltpMap = (req)->
  return {
    exlisting: req.l10n 'Exclusive',"listingType"
    assignment: req.l10n 'Assignment',"listingType"
    rent: req.l10n 'Rental',"listingType"
    mlslisting: req.l10n 'MLS Listing',"listingType"
    mlsrent: req.l10n 'MLS Rental',"listingType"
  }
DEF 'ltpMap', ltpMap


APP '1.5'
# APP 'promote', true

#/1.5/promote/api
POST 'promote/api', (req, resp)->
  req.setupL10n()
  UserModel.appAuth {req,resp},(user)->
    error = (err, url)->
      resp.send {ok:0, err:err, url:url}

    return error('Please Login', '/1.5/user/login') unless user
    #allow all user to edit rental
    # return error('Not Realtor', '/1.5/verify') unless (isAllowed('wecardRltrMnu',user) or user?.flwng)
    # cmd:'string', data:{}
    return error('No command') unless cmd = req.param 'cmd'
    data = req.param 'data'
    #log major events, data is ',' separated string
    ###
    type,             valueFrom, valueTo,  time,  field5
    1(status update),  active,    expire,  Date(),
    2(price update),   100,        1000,
    3(publish),        null,       58,     Date()
    ###
    logEventListings  = (data)->
      #write to file
      console.log data

    # use req._ab translate template, return new {}
    # translate prop template field key and value for display in edit
    #TODO: make these global fn s
    translate_flds = (req, orig_tpl)->
      # return tpl
      realUser = (req.session.get('realUser') or user)
      tpl = {}
      for k, v of orig_tpl
        # @k: field name
        # @v: array of {tp:'', fld:'', v:[], dbv:[]}
        # will tran tp later
        if k is 'tp'
          continue
        if (k is 'vid') and not (req.isAllowed('propAdmin',realUser))
          continue
        tpl[k] = []
        for i in v
          # i is {tp:'', fld:'', v:[], dbv:[]}
          ic = {}
          ic = helpers.shallowExtendObject ic, i
          # fld is for formData, fldV for display
          ctx = i.ctx or 'propertyListing'
          ic.fldV = req._ab i.fld, ctx
          # ph = place holder
          ic.ph = req._ i.ph if i.ph
          # ic.fldV = "X" + i.fld
          tpl[k].push ic
          # console.log 'i===', i
          if i.v
            ic.v = []
            ic.dbv = []
            # v is [string] or [[]]
            for translateK in i.v
              # ic.v.push "XXX" + i
              # handle when v is [[]], ptp only
              if Array.isArray translateK
                ret = []
                dbret = []
                for subKey in translateK
                  if subKey
                    ret.push req._ subKey, 'type_own1_out'
                  else
                    ret.push subKey
                  # ret.push   'XX' + subKey
                  dbret.push subKey
                ic.v.push   ret
                ic.dbv.push dbret
              else
                #i.fld as ctx
                # TODO: use req._ab dbv -> value
                if i.fld is 'sqft'
                  ic.v.push translateK
                else if /(^\d+\+)|(^\d+\-\d+)/.test translateK
                  ic.v.push translateK
                else
                  ic.v.push req._ translateK, i.fld
                ic.dbv.push translateK
          # manually add yes/no
          if i.tp is 'bool'
            ic.v = []
            ic.v.push req._ 'Yes',i.fld
            ic.v.push req._ 'No', i.fld
          # if i already has dbv, dont overwrite
          if i.dbv
            ic.dbv = []
            for dbv in i.dbv
              ic.dbv.push dbv
      tpl.tp = req._(orig_tpl.tp)

      return tpl

    updateUserPromoteProfile = (req, user, data, cb)->
      return cb "Error: no user", 0 unless user
      return cb null, 'no update' unless data.user

      uv = data.user
      if data.to in ['58', 'market']
        to = data.to
      else
        return cb "Error: unknown destination", 0
      UserModel.promote2Market {req,uv,user,to},(err)->
        return cb err.toString() if err
        cb null, 'updated user profile'


      # update = {$unset:{}}
      # update.$unset[data.to+''] = 1
      # UserListings.findOneAndUpdate {id: data.id}, update ,{upsert:false,returnDocument:'after'}, (err,ret)->
      #   return cb err.toString() if err
      #   return cb "Error: no listing found" unless ret.value
      #   cb null, 'market_revoked' + data.to

    promoteListing58 = (req, data, cb) ->
      return cb "Error: no ID", 0 unless data.id
      #deal with 58
      {to,tl,addr58,id,user} = data
      opt = {req, to, tl, addr58, id,user}
      Properties.promoteListing58 opt,(err,ret)->
        return cb err if err
        #INCALL 'promo23rdParty',{id: data.id},(err,ret)->
        promoTo3pty.promo58 (err)->
          # notify promotion service
          if err then console.error err
          console.log "58 Promoted"
        cb null, ret

    revokeListing58 = (req, data, cb) ->
      return cb 'Error: no ID', 0 unless data.id
      #deal with 58
      #check status
      Properties.revokeListing58 {id: data.id, uid:req.user._id, to:data.to}, (err,ret)->
        #INCALL 'promo23rdParty',{id: data.id},(err,ret)->
        return cb err if err
        promoTo3pty.promo58 (err)->
          if err then console.error err
          # notify promotion service
          console.log "Revoked"
          cb null, ret
        # UserListings.findOne {id: data.id, uid:req.user._id } , (err,ret)->

    # promoteListingMarket = (req, data, cb) ->
    #   Properties.promoteListingMarket data,cb


    # revokeListingMarket = (req, data, cb) ->
    #   Properties.revokeListingMarket data,cb

    promoteMyListing = (req, data, cb)->
      if data.to is '58'
        promoteListing58 req, data, (err, ret)->
          return cb err if err
          cb null, ret
      else if data.to is 'market'
        opt = {
          to:data.to,
          cmstn:data.cmstn,
          adok:data.adok,
          marketPromoTs:data.marketPromoTs,
          id:data.id}
        opt.agrmntImg = data.agrmntImg if data.agrmntImg
        opt.isV = data.isV if data.isV
        if req.isAllowed('assignAdmin') and data.rmProp
          opt.isV = true
          opt.rmProp = data.rmProp
          opt.sortOrder = data.sortOrder if data.sortOrder
          opt.nm = data.nm if data.nm
          opt.mbl = data.mbl if data.mbl
        Properties.promoteListingMarket opt, cb
      else
        cb "Error: unknown destination"

    revokeMyListing = (req, data, cb)->
      if data.to is '58'
        revokeListing58 req, data, (err, ret)->
          return cb err if err
          cb null, ret
      else if data.to is 'market'
        Properties.revokeListingMarket {to:data.to,cmstn:data.cmstn,id:data.id}, cb
      else
        cb "Error: unknown destination"

    # Do not update geocache without using the lib. And Remove site may not have write access
    #updateGeoCache = (data, cb)->
    #  sql_base = "INSERT INTO geocache (country, municipality, addr, st, st_num, zip, lat, lng) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?);"
    #  values = []
    #  for i in ['country', 'municipality', 'addr', 'st', 'st_num', 'zip', 'lat', 'lng']
    #    values.push data[i]
    #  mysql_client.query sql_base, values,(err,ret)->
    #    cb err.toString() if err
    #    cb null

    # saveListing = (data, user, cb)->
    #   Properties.saveRMListing {data,user},cb

    get_listings = (user, data, cb)->
      skip = data.skip or 0
      ret = {
        ok:1
        type:data.type
        skip:skip
      }
      type = data.type or "mylisting"
      # HERE!!!
      if type isnt 'mylisting'
        ret.l  = []
        ret.ul = []
        return cb ret
      #find all listing with id user.id limit 20
      return Properties.findUserAllListings {locale:req.locale(),user,skip}, (err,list)->
        if err
          ret.err = err.toString()
        ret.l = list
        cb ret
    if cmd is 'pmt-p-lst'
      get_listings(user, data, (ret)->
        return resp.send ret
      )
    else if cmd is 'pmt-p-tpl'
      obj =
        ok:1
        type:data
        tpl: translate_flds(req, Properties.getRMListingTemplete(data))
      return resp.send obj
    else if cmd is 'pmt-p-pub'
      # publish to db
      findErr = (err,errType)->
        ret.err = err
        ret[errType] = 1
        return resp.send ret
      ret =
        ok:0
        to:data.to
      # data.user = null if no update
      try
        verify = await UserModel.checkVerify user._id
        isPhoneNumberBlocked = await BlockPhoneModel.isPhoneNumberBlocked verify.mbl if verify.mbl
      catch err
        debug.error err
        return findErr(MSG_STRINGS.DB_ERROR)
      # Please verify your {{email/phone number/email and phone number}} before publishing!
      # NOTE: 暂时没有普通用户verify phone number的机制，另外promote页面需要修改可以跳转普通用户verify手机号的页面，暂时先这样
      if not (verify?.emlV and verify?.mblV)
        if not verify.emlV
          toVerify = 'email'
        else if not verify.mblV
          toVerify = 'phone number'
        else
          toVerify = ''
        errMsg = "Please verify your #{toVerify} before publishing!"
        return findErr(req.l10n(errMsg),'unverify')
      if verify.mbl and isPhoneNumberBlocked
        return findErr(req.l10n(MSG_STRINGS.PHONE_BLOCKED))
      updateUserPromoteProfile req, user, data, (err, result)->
        return findErr(err) if err
        ret.msg = result
        data.user = user
        promoteMyListing req, data, (err,result2)->
          return findErr(err) if err
          ret.ok = 1
          ret.msg += result2.msg
          ret.id = data.id
          ret.val = result2.val
          # assignAdmin角色的人发布真楼花的概率较大，删除trustedAssigm的缓存；没有用rmProp是因为如果将self改为none，判断rmProp就是false
          if req.isAllowed('assignAdmin')
            objectCache.clearObjectCacheList TRUSTEDASSIGM
            objectCache.clearObjectCacheList TRUSTED_ASSIGN_CITY_LIST
            objectCache.clearObjectCacheList TRUSTED_ASSIGN_STYLE_LIST
          return resp.send ret
    else if cmd is 'pmt-p-revoke'
      findErr = (err)->
        ret.err = err
        return resp.send ret
      ret =
        ok:0
        to:data.to
      revokeMyListing req, data, (err,result2)->
        return findErr(err) if err
        ret.ok = 1
        ret.id = data.id
        ret.msg = (if ret.msg then ret.msg else '') + result2.msg
        ret.val = result2.val
        if req.isAllowed('assignAdmin')
          objectCache.clearObjectCacheList TRUSTEDASSIGM
          objectCache.clearObjectCacheList TRUSTED_ASSIGN_CITY_LIST
          objectCache.clearObjectCacheList TRUSTED_ASSIGN_STYLE_LIST
        return resp.send ret
    else if cmd is 'pmt-p-delete'
      findErr = (err)->
        ret.err = err
        return resp.send ret
      ret =
        ok:0
        to:data.to
      Properties.removeUserListing {id:data.id,uid:user._id,_id:data._id},(err,ret)->
        # UserListings.deleteOne {id:data.id, uid:user._id},(err,ret)->
        return findErr(err) if err
        ret.ok = 1
        ret.id = data.id
        if req.isAllowed('assignAdmin')
          objectCache.clearObjectCacheList TRUSTEDASSIGM
          objectCache.clearObjectCacheList TRUSTED_ASSIGN_CITY_LIST
          objectCache.clearObjectCacheList TRUSTED_ASSIGN_STYLE_LIST
        return resp.send ret
    else if cmd is 'pmt-p-rcmd'
      return unless data.id
      rcmd = if data.rcmd then 1 else 0
      findErr = (err)->
        ret.err = err
        return resp.send ret
      ret =
        ok:0
      Properties.setRMPropRecommand {id:data.id,rcmd},(err,ret)->
        return findErr(err) if err
        ret.ok = 1
        ret.id = data.id
        return resp.send ret
    else if cmd is 'pmt-p-status'
      return unless data.id
      findErr = (err)->
        ret.err = err
        return resp.send ret
      ret =
        ok:0
        to:data.to
      try
        prop = await Properties.findRMOneByID data.id
      catch err
        debug.error err
        return findErr(MSG_STRINGS.DB_ERROR)
      return findErr('Prop not found') unless prop
      ret.ok = 1
      ret.id = data.id
      if prop[data.to]?.st
        prop[data.to].stT = req._ prop[data.to].st
      ret.val = prop[data.to]
      return resp.send ret
    else if cmd is 'pmt-p-save'
      findErr = (err)->
        ret.err = err
        return resp.send ret
      ret =
        ok:0
      if data.prop.id
        Properties.saveRMListing {prop:data.prop, user}, (err, result)->
          return findErr(err) if err
          ret.ok = 1
          ret.msg = result
          return resp.send ret
      else
        createUniqueListingID (err, rmid)->
          return findErr(err) if err
          ret.id = rmid
          data.prop.id = rmid
          # create timestamp
          data.prop.ts = new Date()
          #save to db
          Properties.saveRMListing {prop:data.prop, user}, (err, result)->
            return findErr(err) if err
            ret.ok = 1
            ret.msg = result
            return resp.send ret

# get user profile nm and eml
# DEF 'fullNameOrNickname',fullNameOrNickname
POST 'promote/userInfo.json',(req,resp)->
  # TODO: fix url appauth in post req
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    to = req.param 'to'
    ret = {ok:0, user:{}}
    # search in user profile
    # UserModel.findProfileById user.id,{}, (err,profile)->
    #   if err
    #     ret.err = err
    #     return resp.send ret
    #   # unless profile
    #   #   ret.err = "User profile not found!"
    #   #   return resp.send ret
    #   if profile and profile[to] and profile[to].eml
    #     ret.user = profile[to]
    #   else
    #     # generate at front end, and save to db
    if Array.isArray user.eml
      ret.user.eml = user.eml.slice()
    else
      ret.user.eml = user.eml
    flds = ['fn','ln','cpny','mbl','itr','qq','wx','web']
    for i in flds
      ret.user[i] = user[i] or ''
    ret.ok = 1
    ret.user.cpny = libUser.userCpny(req.locale(),user)
    ret.user.fnm = libUser.fullNameOrNickname(req.locale(),user)
    ret.user.avt = user.avt
    resp.send ret
    # ret = helpers.shallowExtendObject ret,user

# GET 'promote/inputAddr', (req,resp)->
#   UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
#     resp.ckup 'input-address-page'

# /1.5/promote/mylisting page
GET 'promote/mylisting', (req,resp)->
  # return if req.redirectHTTPWhenCnDitu()
  if req.redirectHTTP()
    return
  d = req.param 'd'
  id = req.param 'id'
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->
    # allow all loggedin use to post
    # unless req.hasRole 'realtor'
    #   return resp.ckup "generalError", {err:"This feature is available to Realtors only"}
    type = 'mylisting' #(req.param "type") or "mylisting"
    ml_num = req.param 'ml_num'
    promoteTo =  req.param 'to' if ml_num #only works if has ml_num
    action = req.param 'action'
    isApp = req.getDevType() is 'app'

    unless promoteTo in ['58','market']
      promoteTo = null
    verified = {emlV:false,mblV:false}
    try
      verified = await UserModel.checkVerify user._id
    catch err
      debug.error err
    cfg =
      id: id
      d: d
      to : promoteTo
      user    :user
      type    :type
      # s3bucket:s3config.params.Bucket
      ml_num  :ml_num
      action  : action
      ltpMap : ltpMap(req)
      isApp: isApp
      isVipUser: req.isAllowed 'vipUser'
      maxImageSize : getConfig('maxImageSize')
      # gmap_static: getGoogleAPILink({tp:'staticMap',cn:req.isChinaIP(), src:'mlstg'})
      # gmap_geocode: getGoogleAPILink({tp:'geocode',cn:req.isChinaIP(), src:'mlstg'})
      # gmap_apijs: getGoogleAPILink({tp:'googleapi',cn:req.isChinaIP(),
      #lib:'places',cb:'initAutocomplete',lang:'en',src:'mlstg'})
      gShareHost:gShareHost
      verified:verified
    # req.isChinaIP = ()->
    #   true
    resp.ckup 'mylisting-page', cfg, '_', {noasync:1}


POST 'promote/search', (req, resp)->
  req.setupL10n()
  UserModel.appAuth {req,resp},(user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user

    unless search = req.body?.search
      return respError {category:MSG_STRINGS.BAD_PARAMETER,resp}
    isAssignAdmin = req.isAllowed('assignAdmin')

    skip = req.body?.skip or 0
    Properties.findUserAllListings {locale:req.locale(),user,skip,search,isAssignAdmin}, (err,list)->
      if err
        return resp.send {ok:0,err:err.toString()}
      uid = user._id.toString()
      for i in list
        if i.uid.toString() isnt uid
          i.others = true
      resp.send {ok:1,list}


# promote page, includes leads, mylisting, listing market
# use lazy initiallzation
# need to modify leads
VIEW 'mylisting-page',->
  css '/css/angular-datepicker.min.css'
  css '/css/mylisting.min.css'
  css '/css/sprite.min.css'
  js '/js/ratchet/segmented-controllers.js'
  js '/js/sortable.min.js'
  div id:'realtorUtilPage',
  ngApp:'listingApp',
  scroll:'',
  ngController:'listingCtrl',
  class:'',-> #ngApp
    text ckup 'edit-listing-modal'
    # text ckup 'input-address-modal'

    # text ckup 'city-select-modal'
    text ckup 'shareDialog2', {page:'mylisting', req:@req}
    text ckup 'promote-modal',{gShareHost:@gShareHost}
    text ckup 'new-listing-modal'
    text ckup 'date-picker-modal'
    # text ckup 'img-select-modal', {angular:1}
    text ckup 'img-preview-modal'
    # text ckup 'save-promote-modal'
    text ckup 'save-tip-modal'
    text ckup 'remarks-modal'
    text ckup 'remarkszh-modal'
    text ckup 'select-contact-modal'
    text ckup 'busy-icon', {}

    text ckup 'flashMessage',{id:'vip-only',msg:_('VIP Only')}
    text ckup 'flashMessage',{id:'server-error',msg:_('Server Error')}
    text ckup 'flashMessage',{id:'img-inserted',msg:_('Image Inserted')}
    text ckup 'flashMessage',{id:'promoted',msg:_('Promoted')}
    text ckup 'flashMessage',{id:'revoked',msg:_('Revoked!')}
    text ckup 'flashMessage',{id:'deleted',msg:_('Deleted')}
    text ckup 'flashMessage',{id:'saved',msg:_('Saved')}
    text ckup 'flashMessage',{id:'tip_save_err',msg:_('Please Fill All Required Fields')}
    text ckup 'flashMessage',{id:'cmstn-req',msg:_('Commission is Required')}
    text ckup 'flashMessage',{id:'revoke_fail',msg:_('Revoke Failed! Never Promoted.')}
    text ckup 'flashMessage',{id:'status-not-active',msg:_('Cannot be published, not active.')}
    text ckup 'flashMessage',{id:'integers-only',msg:_('Need integer.')}
    text ckup 'flashMessage',{id:'integers-only2',msg:_('Need integer.')}
    text ckup 'flashMessage',{id:'publish_draft_err',msg:_('Please fill in the required fields before publishing!')}
    text ckup 'flashMessage',{id:'share_save_err',msg:_('Save before sharing')}
    text ckup 'flashMessage',{id:'share_draft_err',msg:_("Can't share a draft listing")}
    text ckup 'flashMessage',{id:'no-longer-supported',msg:_('Cannot be published, mls not supported.')}
    text ckup 'flashMessage',{id:'allow-advertise',msg:_('Cannot be published, mls not supported.')}
    text ckup 'flashMessage',{id:'revoke-not-change',msg:_("Revoked by admin but didn't change anything")}
    text ckup 'flashMessage',{id:'conten_empty',msg:_("Please fill in the content!")}

    # ckup views here

    div class:'backdrop ng-cloak', ngHide:'hideBackdrop'#, ngClick:'hideBackdrop = true'

    header class: 'bar bar-nav', ->
      # text ckup 'headerbarUserModalBtn'
      h1 class: 'title',-> text _ 'MyListing'
      if @req.getDevType() is 'app'
        backUrl = @d or '/1.5/settings'
        a href:backUrl, class:'icon fa fa-back pull-left'#, ngClick:"goBack()"
        span class:'icon fa fa-rmsearch pull-right',ngClick:"showSearchBar = true"
    div class:'search-bar', ngShow:'showSearchBar', ->
      input type:'text', class:'search-input', placeholder:_('Search listing ID'), ngModel:'searchId'
      a class:'icon fa fa-rmclose pull-right', ngClick:"resetSearch()"

    # nav class:'bar bar-standard bar-header-secondary',->
    #   div class:'segmented-control scrollable ',->
    #     if @req.hasRole 'realtor'
    #       a class:'control-item ', href:'/1.5/crm/leads', -> text _ "Leads"
    #     a class:'control-item active', href:'#', ->#ngClick:"loadListings('mylisting', 0)", ngClass:"{'active': type == 'mylisting'}",->
    #       text _ "MyListing"
    #     a class:'control-item ', href:'/1.5/promote/market', ->#ngClick:"loadListings('market', 0)", ngClass:"{'active': type == 'market'}",->
    #       text _ "Listing Market"
    div id:'signupModal', class:'modal ', style:'z-index:20;', ->
      header class:'bar bar-nav', ->
        a class: 'icon icon-close pull-right', href: '', ngClick:"toggleModal('signupModal')"
        h1 class: 'title',-> _ 'Contact Us'
      div class:'content', ->
        div style:'color: #777;
          font-size: 13px;
          padding: 19px 10px 8px 15px;
          text-align: left;',->
          text _ 'Please contact us to upload your Exclusive or Assignment Listings. As a landlord, you can list your home for rentals without any charges and number limitations.'
        text ckup 'signUpForm',{title:''} unless @req.hasRole 'realtor'

    div class: 'bar bar-standard bar-footer', ->
      aParam = class:'create fa fa-plus', href:'javascript: void 0', ngClick:"toggleModal('newListingModal');hideBackdrop=false"
      # unless @req.hasRole 'realtor'
      #   aParam.ngClick = "createListing('rent')"
      a aParam, ->
        div class:'desc', ->
          text _ 'Create'

    div class:'WSBridge',style:'display:none',->

      text ckup 'listing-share-desc'
      user = @req.session.get('user')
      span id:'share-url',->
        # link_url = "#{@req.shareHost()}/html/propDetailPage.html?id={{selectedProp.length? '' : prop.id}}?lang=#{@req.locale()}"
        link_url = "#{@req.shareHost()}/1.5/prop/detail?id={{selectedProp.length? '' : prop.id}}?lang=#{@req.locale()}"
        if user?.id and @req.isAllowed 'shareSign'
          link_url += "{{wDl ? '&wDl=1':''}}{{wSign ? '&aid=#{user._id or user.id}':''}}"
        else
          link_url += '&wDl=1'
          if @owner or user
            link_url += "&aid=#{@req.getShareUID(@owner or user)}"
        text link_url
      span id:'share-data',->
        data_text = "id={{prop.id}}&tp=mylisting&lang=#{@req.locale()}"
        if user?.id and @req.isAllowed 'shareSign'
          data_text += "{{wSign ? '&aid=#{@req.getShareUID(user)}':''}}{{wDl ? '&wDl=1':''}}"
        else
          data_text += '&wDl=1'
          if @owner or user
            data_text += "&uid=#{@req.getShareUID(@owner or user)}"
        text data_text

      span id:'m-share-url',->
        link_url = "#{@req.shareHost()}/1.5/search/prop?id={{selectedProp.join(',')}}&lang=#{@req.locale()}&share=1"
        if @req.isAllowed 'shareSign'
          link_url += "{{wSign ? '&aid=#{@req.getShareUID(user)}':''}}{{wDl ? '&wDl=1':''}}"
        else
          link_url += "&aid=#{@req.getShareUID(user)}&wDl=1"
        text link_url

      span id:'share-image',->
        text "{{picUrls.length > 0 ? picUrls[0] : '#{@req.shareHost()}/img/create_exlisting.png'}}"
      if @singleView #have this in list result
        span id:'wx-url',->
          text @req.fullUrl()


    div class: 'content', ->
      div class:'content-list ng-cloak', ->
        div class:'listing-wrapper',
        ngIf:"type === 'mylisting'",
        ngRepeat:'prop in mylistings',->
          div class:'card my-listing-card',->
            div class:'card-header',->
              span ngIf:"prop.ltp == 'exlisting'", -> _ 'Exclusive','listingType'
              span ngIf:"prop.ltp == 'assignment'", -> _ 'Assignment','listingType'
              span ngIf:"prop.ltp == 'rent'", -> _ 'Rental','listingType'
              span ngIf:"prop.ltp == 'mlslisting'", -> _ 'MLS Listing','listingType'
              span ngIf:"prop.ltp == 'mlsrent'", -> _ 'MLS Rental','listingType'
              span class:'pull-right', ngClick:'deleteProp(prop)', style:'font-size:14px;',->
                text "{{prop.mt | date: 'yyyy.MM.dd'}}"
                span class:'sprite16-14 sprite16-1-4',
                style:'color:#e03131; margin: 5px 5px 5px 8px;vertical-align: bottom;'
            div class:'card-content prop-content',->
              # div class:"tri-wrapper",->
              #   div class:"triangle", ngClass:"{'active':prop.status=='Active' || prop.status=='Inactive'}", ->
              #     div class:"status", ->
              #       text "{{prop.status}}"
              div class:'status-wrapper',
              ngClass:"{'active':prop.status=='Active' || prop.status=='Inactive'}",
              style:'font-size: 14px; padding-top: 10px;',
              ngIf:'prop.isValid == true || prop.isValid == undefined',->
                text '{{prop.status}}'
              div class:'status-wrapper draft',
              style:'font-size: 14px; padding-top: 10px;',
              ngIf:'prop.isValid == false',->
                text 'Draft'

              div class:'img-wrapper', ngClick:'editMyListing(prop.id)',->
                img ngSrc:"{{prop.img || '/img/noPic.png'}} "

              div class:'detail-wrapper',
              ngClick:'editMyListing(prop.id)', ->
                div -> #TODO: height: 21px; overflow: hidden;
                  span class:'addr',->
                    text "{{prop.unt}} {{prop.addr?prop.addr+',':''}} {{prop.city?prop.city+',':''}} {{prop.prov}} "
                div ->
                  span class:'', style:'font-size: 17px;',->
                    #TODO: fix accourding to rm2propdetail :143
                    text "{{prop.lp | currency:\'$\':0}} "
                    text _ 'for', 'saleOrRent'
                    text ' {{prop.stp}}'
                div ->
                  text "{{prop.ptp}} {{prop.pstyl}}, #{_('Bedrm')} {{prop.rmbdrm || prop.tbdrms || prop.bdrms}} #{_('Washrm')} {{prop.rmbthrm || prop.tbthrms || prop.bthrms}}"

            div class:'promote-wrapper', -> # Rain: ngSrc to change image, no need 2 spans
              #TODO: use ngSrc
              span class:'pull-left', ngShow:'prop.vc',->
                p ->
                  text _ 'Viewed'
                  text ':{{prop.vcd || 0}}/{{prop.vc || 0}}'

              span class:'pull-left', ngShow:'prop.shr',->
                p ->
                  text _ 'Shared'
                  text ':{{prop.shrd || 0}}/{{prop.shr || 0}}'

              span class:'pull-left', ngShow:'prop.others',ngClick:'openOwnerInfo(prop.uid)',->
                p style:'color:#337ab7;',->
                  text _ 'Publisher'

              span class:'pull-right',
              ngClick:"promote('market', prop);",
              ngShow:"highLightPromoteIcon(prop, 'market', true)",->
                i class:'sprite50-35 sprite50-4-2'
              span class:'pull-right',
              ngClick:"promote('market', prop);",
              ngShow:"highLightPromoteIcon(prop, 'market', false)",->
                i class:'sprite50-35 sprite50-6-5'

              # if @req.hasRole 'realtor' # allow all user to upload to 58
              # 隐藏58 20220520
              # span class:'pull-right',
              # ngClick:"promote('58', prop);",
              # ngShow:"highLightPromoteIcon(prop, '58', true)",->
              #   i class:'sprite50-35 sprite50-4-4'
              # span class:'pull-right',
              # ngClick:"promote('58', prop);",
              # ngShow:"highLightPromoteIcon(prop, '58', false)",->
              #   i class:'sprite50-35 sprite50-6-4'
              span class:'pull-right',
              ngClick:"promote('wecard', prop);",
              ngShow:"highLightPromoteIcon(prop, 'wecard', true)",->
                i class:'sprite50-35 sprite50-8-5'
              span class:'pull-right',
              ngClick:"promote('wecard', prop);",
              ngShow:"highLightPromoteIcon(prop, 'wecard', false)",->
                i class:'sprite50-35 sprite50-7-5'

              span class:'pull-right', ->
                p ->
                  text _ 'Promote'

        div class:'spinner-wrapper',
        style:' height: 40px; padding: 10px;',
        ngShow:'loading',->
          div class:'pull-spinner ',
          ngShow:'loading',
          style:'display:block'
        # button class:"btn btn-block btn-long btn-positive", ngClick:"loadMore()", style:"margin-top:10px;", ngShow:"hasMore",-> _ "Load More"

  ###
  js '/js/angular-touch.min.js'
  js '/js/moment-timezone-with-data.min.js'
  ###
  js '/js/moment.min.js'
  js '/js/angular-datepicker.min.js'
  # js '/socket/socket.io.js'
  emurl = @req.exMapURL()
  coffeejs {vars:{
              id:@id
              d:@d
              RealMaster:_('RealMaster')
              ltpMap:@ltpMap
              maxImageSize:@maxImageSize
              str_bdrms:_('Bedroom')
              str_bthrms:_('Bathroom')
              vipTipStr: @req.l10n("Available only for Premium VIP user! Upgrade and get more advanced features.")
              vipLaterStr: @req.l10n('Later')
              str_yes: @req.l10n('Yes')
              str_cancel: @req.l10n('Cancel')
              str_verify: @req.l10n('Verify')
              str_confirm: @req.l10n('Confirm delete')
              vipSeeStr: @req.l10n('See More')
              lang: @req.locale(),
              s3protocol: @req.getProtocol(),
              action: @action
              emurl:emurl
              type:@type
              s3bucket:@s3bucket
              ml_num:@ml_num
              isApp:@isApp
              to:@to
              userInfonm: @user?.nm
              userInfombl: @user?.mbl
              userInfoeml: @user?.eml?.toString()
              isVipUser:@isVipUser
              isRealtor:@req.isAllowed('brokerage')
              isAssignAdmin:@req.isAllowed('assignAdmin')
              verified:@verified
              # gmap_geocode:@gmap_geocode
              # gmap_static:@gmap_static
              # gmap_apijs:@gmap_apijs
              domain:@req.host}
            }, ->
    document.getElementById('busy-icon').style.display = 'block'
    # necessary to do this?
    # window.name = "NG_DEFER_BOOTSTRAP!"
    RMSrv?.onReady ->
      RMSrv.clearCache()
      RMSrv.enableBackButton(false) if RMSrv.enableBackButton
    null
  js '/js/mylisting.min.js'
  # script src:'/js/mylisting.min.js', async:"", defer:""
  js '/js/proplib.min.js'
  # script src:'/js/proplib.min.js', async:"", defer:""
  # script src:@gmap_apijs, async:"", defer:""

# $scope.changeDate = (modelName, newDate)->
#   console.log(modelName + ' has had a date change. New value is ' + newDate.format());
# might need to search in future
# $scope.searchCpny = ()->
#   unless rltr = $scope.cpny
#     return console.log  "No current brokerage"
#   unless rltr.length > 3
#     return console.log  "Too Short"
#   rltr = rltr.replace(/\,?\s*BROKERAGE\s*$/i,'').toUpperCase().trim()
#   $http.post('/1.5/brkg/phones?tp=name',{nm:rltr}).success (ret)->
#     if ret.ok
#       $scope.brkgs = ret.brkgs
#       $scope.$apply()
#     else
#       $scope.err = ret.e

# $scope.selectCompany = (name, addr) ->
#   $scope.userData['cpny'] = name
#   $scope.userData['cpny_addr'] = addr
#   toggleModal('companySelectModal')

# background: url(http://www.stackoverflow.com/favicon.ico) 96% / 15% no-repeat #eee;

# promote page, has header and agent info
VIEW "promote-modal",->
  div  class:'modal ', id:'promoteModal',-> #ngApp:'appUtilLinks',ngController:'ctrlIndex',
    header class:'bar bar-nav',->
    #  a href:"#promoteModal", class:'pull-right icon icon-close'
      a href:"javascript: void 0", ngClick:"toggleModal('promoteModal')", class:'pull-right icon icon-close'
      h1 class:'title',-> _ "Listing Promote"
      # a href:'#', class:'pull-left icon icon-left-nav'

    div class:'bar bar-standard bar-footer', style:"padding:0", ->
      button class:'btn btn-positive btn-half btn-sharp btn-fill', ngClick:"publish()",->
        text _ "Publish"
      button class:'btn btn-default btn-half btn-sharp btn-fill', ngClick:"revoke()",->
        text _ "Revoke"

    div class:'content', ->
      div class:'dest-wrapper',->
        div class:'icon', ->
          i class:'sprite50-80 sprite50-4-4',ngShow:"promoteTo === '58'"
          i class:'sprite50-80 sprite50-4-2',ngShow:"promoteTo === 'market'"

        div class:'detail',->
          div class:'dest-name',->
            span ngShow:"promoteTo === '58'", -> _ "58.com" #"58同城"
            span ngShow:"promoteTo === 'market'", -> _ "Listing Market"
            i class:'fa fa-globe', onclick:"RMSrv.showInBrowser('http://58.com')", ngShow:"promoteTo === '58'"
            i class:'fa fa-globe', onclick:"RMSrv.showInBrowser('#{@gShareHost}')", ngShow:"promoteTo === 'market'"


          div class:'dest-desc',->
            span ngShow:"promoteTo === '58'", -> _ "The Largest Online Classified Advertising Portal in China." #中国最大的网上分类资讯平台。"
            span ngShow:"promoteTo === 'market'", -> _ "Listing Exchange Market Place in RealMaster Platform."

      div class:'', ->
        div class:'h4',->
          text _ "Listing: "
          # span class:'pull-right', style:"", ngShow:"curProp[promoteTo].url", ngClick:"showInBrowser(curProp[promoteTo].url)",->
            # p ->
            #   br()
            #   span ->
            #     text "ID: {{curProp.id}}"
            #   span ngShow:"curProp.ml_num",->
            #     text " ({{curProp.ml_num}})"
            #   br()
            #   span ->
            #     text _ "Status: "
            #     text "{{curProp.status}}"
            # a href:"#", style:"font-size:14px; font-weight:normal",->
            #   text "View"

        div class:'prop-wrapper',->
          div class:"promo-status", ngShow:"curProp[promoteTo].st",->
            div style:"padding-top: 10px; font-weight: bold; font-size: 16px;",->
              text "{{curProp[promoteTo].stT || curProp[promoteTo].stT}}"
            div style:"padding-top: 15px; font-size: 11px;", ->
              text "{{curProp[promoteTo].ts | date:'yyyy-MM-dd'}}"

          div style:"padding: 15px 115px 0 10px",->
            span class:'',->
              text "{{curProp.lp | currency:\'$\':0}}"
            span class:'addr',->
              text "{{ curProp.unt}}  {{ curProp.addr}} {{ curProp.city ? curProp.city + ',' : '' }} {{ curProp.prov}}"

              # br()
          # div class:"", style:"border-top: 7px solid #E6E6E6"

        # NOTE: 2020-08 /promote/market deprecated  ( promoteTo == 'market' && curProp[promoteTo].st == 'Promoted' )
        div ngShow:"( curProp[promoteTo].url )",  style:"background-color: white; width:100%; color: #666; font-size: 14px; padding: 11px 10px 11px 10px;    border-top: 1px solid #f1f1f1; ",->
          a href:"javascript: void 0", style:"font-size:14px; font-weight:normal", ngClick:"checkPromotResult(promoteTo, curProp)", ->
            text _ "Check promoted result"

        div class:'extra', ngShow:"promoteTo === '58'", ->
          div class:'input', ->
            div class:"label", ->
              text _ "Title"
            # modify lock if modifyed tl
            input type:'text', placeholder:_("Title"), ngModel:"curProp.tl58", class:''

          div class:'input', style:"border-top: 1px solid #f1f1f1;",->
            div class:"label", ->
              text _ "Address"
            #no change do nothing
            input type:'text', placeholder:_("Address"), ngModel:"curProp.addr58", class:'addr'
        if @req.hasRole 'realtor'
          span style:"display:none", ngInit:"commi = 1"
        div class:'h4', ngShow:"promoteTo === 'market' && commi",->
          text _ "Terms & Conditions: "
        div class:'extra commi', ngShow:"promoteTo === 'market' && commi", ->
          div class:'input', ->
            div class:"label",ngClass:"{'error':showError && (!curProp['market'].cmstn)}",->
              span ngClass:'', ->
                text _ 'Co-op Commission'
              i class:'fa fa-star-of-life'
            input type:'text', placeholder:"Like % or $ or Months", ngModel:"curProp['market'].cmstn", class:'commi'

          div class:'agreeCheck',->
            i class:"icon fa ", style:"float: right;color:#5cb85c;margin-top: 3px;vertical-align: middle;padding-left: 10px;",ngClass:"{'fa-check-square-o':curProp['market'].adok, 'fa-square-o':!curProp['market'].adok}", ngClick:"curProp['market'].adok = !curProp['market'].adok"
            span style:'vertical-align: middle;', ->
              span ngClass:'', ->
                text _ 'Allow other Realtors to advertise'
              # i class:'fa fa-star-of-life'

          div class:'listingAgreement',ngIf:'curProp.ltp == "assignment" || curProp.ltp == "exlisting"',->
            span style:'float: right;padding: 10px 0 10px 15px;color:#5cb85c;font-size:15px;', ngIf:'!curProp["market"].agrmntImg',ngClick:'uploadAgrmntImg()', ->
              text _ 'Upload'
            span class:'pull-right',style:'position: relative;margin-right: 10px;' , ngIf:'curProp["market"].agrmntImg.length>0', ->
              img ngSrc:'{{curProp["market"].agrmntImg}}', ngClick:'previewPic(curProp["market"].agrmntImg,"agrmntImg")',style:'width:30px;height:40px;'
              i class:'fa fa-rmclose',style:'color:rgba(0,0,0,0.3);position: absolute;background: white;right: -16px;top: -16px;padding: 10px 10px 0 0;border-radius: 8px;',ngClick:'deleteAgrmntImg()'
            span ->
              span style:'display: block;', ->
                text _ 'First page of listing agreement'
              span class:'describe', ->
                text _ 'Only used for internal authentication purpose'
          
          div class:'assignRmProp',ngIf:'isAssignAdmin',->
            div class:"label", ngStyle:"{'flex': curProp['market'].rmProp.length > 5? 5 : 9}", ->
              span ngClass:'', ->
                text _ 'RM Property'
            select name:"rmProp",class:'rmPropSel', ngModel:"curProp['market'].rmProp", ->
              option value:'', ->
                text 'none'
              option value:"self", ->
                text 'self'
              option value:"outside", ->
                text 'outside'

          div class:'agreeCheck',ngIf:'isAssignAdmin',->
            div class:"label", ->
              span ngClass:'', ->
                text _ 'RM Property Sort'
            input style:'font-weight:normal;',type:"number", name:"sort", ngModel:"curProp['market'].sortOrder", pattern:"[0-9]*"
          
          div class:'assignRmProp',ngIf:"isAssignAdmin && curProp['market'].rmProp && curProp['market'].rmProp !== ''",->
            span ->
              text _ 'Contact'
            div style:'display: flex;align-items: center;',->
              span class:'fa fa-times-circle',style:'color:#e03131;padding:10px;',ngClick:'deleteSel()',ngIf:"curProp['market'].mbl"
              div style:'font:14px normal;',->
                p ->
                  text "{{curProp['market'].nm}}"
                p ->
                  text "{{curProp['market'].mbl}}"
              span style:'color:#5cb85c;font-size:15px;margin-left:15px;', ngClick:'getContactList()',->
                text _ 'Select'
          div class:'policyStatement',->
            i class:'fa fa-exclamation-circle',style:"color:#e03131;font-size:16px"
            # span ngClass:"", -> # {'error':showError && !agreeCheck}
            #   text _ "Warning"
          # i class:"icon fa ", style:"float: right;  margin-top: 3px;",ngClass:"{'fa-check-square-o':curProp['market'].adok, 'fa-square-o':!curProp['market'].adok}", ngClick:"curProp['market'].adok = !curProp['market'].adok"
            span class:"pull-right",style:'width: calc(100% - 20px);', -> # {'error':showError && !agreeCheck}
              text _ "We assume that all listings posted by you as our member are properly authorized. However, in case we receive any complaint pertaining to any listing, we may require you to provide an authorization document from the owner. If it turns out the listing is not authorized by the owner, the listing will be taken off, and your VIP membership may be suspended. We do not accept any responsibility and will not be liable for any loss or damage suffered by you whatsoever arising out of or in connection with such unauthorized listing."



        div class:'h4', ->
          text _ "Personal Info: "
          # i class:"fa fa-edit", href:"/1.5/settings/editProfile"

        div class:'info-wraper',->
          div ->
            label ->
              img src:'/img/logo.png', ngSrc:"{{userData['avt']}}"
            # span ->
              # a href:'/1.5/settings/editUserProfileImg', class:'btn btn-positive btn-mid', ->
              #   "Change Photo"
              # a href:'/1.5/settings/editProfile', class:'btn btn-positive btn-mid', ->
              #   "Edit Profile"
          if @req.hasRole 'realtor'
            div style:"display:none", ngInit:"isRealtor = 1"
          div ngIf:"promoteTo == '58' && isRealtor", ->
            label class:'',->
              text _ "Title"
            input type:'text', placeholder:_('Title on 58.com for Realtor'), ngModel:"userData['tl']"

          div ->
            label class:'',->
              text _ "Name"
            input type:'text', placeholder:_('Name'), ngModel:"userData['fnm']", ngReadonly:"promoteTo == 'market'"

          # div ->
          #   label class:'',->
          #     text _ "Last Name"
          #   input type:'text', placeholder:_('Last Name'), ngModel:"userData['ln']", ngReadonly:"promoteTo == 'market'"

          div ->
            label class:'',->
              text _ "Cell"
            div class:'input',->
              i ngIf:"promoteTo == 'market'",class:'fa',ngClick:'openVerifyPopup("p","mblV")',ngClass:"{'fa-check-circle':verified.mblV == true,'fa-exclamation-circle':verified.mblV == false}"
              input class:'hasFa',type:'number',  ngModel:"userData['mbl']", ngIf:"promoteTo != 'market'"
              p ngIf:"promoteTo == 'market'",ngClick:'openVerifyPopup("p","mblV")',-> text "{{userData['mbl']}}"
              b ngShow:"verified.mblV == false",ngClick:'openVerifyPopup("p","mblV")',class:'icon icon-right-nav'
            div style:"color: #666;font-size: 11px;", ngShow:"promoteTo != 'market'",->
              text _ "This number may be dialled directly from China"
          div ->
            label class:'',->
              text _ "Email"
            div class:'input',->
              i ngIf:"promoteTo == 'market'",class:'fa',ngClick:'openVerifyPopup("v","emlV")',ngClass:"{'fa-check-circle':verified.emlV == true,'fa-exclamation-circle':verified.emlV == false}"
              input class:'hasFa',type:'number',  ngModel:"userData['mbl']", ngIf:"promoteTo != 'market'"
              p ngIf:"promoteTo == 'market'",ngClick:'openVerifyPopup("v","emlV")',-> text "{{userData['eml']}}"
              b ngShow:"verified.emlV == false",ngClick:'openVerifyPopup("v","emlV")',class:'icon icon-right-nav'
          div ->
            label class:'',->
              text _ "QQ"
            input type:'number',  ngModel:"userData['qq']", ngReadonly:"promoteTo == 'market'"

          div ->
            label class:'',->
              text _ "WeChat"
            input type:'text',  ngModel:"userData['wx']", ngReadonly:"promoteTo == 'market'"

          div ngIf:"isRealtor",->
            label  style:"margin-bottom: 0;", ->
              text _ "Company"
            input type:'text',  ngModel:"userData['cpny']", ngReadonly:"promoteTo == 'market'" #readonly:'', ngClick:"toggleModal('companySelectModal')"

          div ngIf:"isRealtor",->
            label  style:"margin-bottom: 0;", ->
              text _ "Website"
            input type:'text',  ngModel:"userData['web']", ngReadonly:"promoteTo == 'market'" #readonly:'', ngClick:"toggleModal('companySelectModal')"

          div ngHide:"promoteTo == 'market'", ngIf:"isRealtor",->
            label class:'',->
              text _ "Introduction"
          div ngHide:"promoteTo == 'market'", style:"border-top: 1px none;", ngIf:"isRealtor",->
            textarea rows:"3" ,class:'',  ngModel:"userData['itr']", style:"", ngReadonly:"promoteTo == 'market'"

          # div ngHide:"promoteTo == 'market'", ngClick:"showCityList()", ngIf:"isRealtor",->
          #   div ->
          #     label -> _ "Service City"
          #     # span ->
          #     #   i ngClick:"showCityList()", class:'fa fa-edit pull-right'
          #   div style:" padding-bottom: 10px;",->
          #     p class:'city-wrapper',->
          #       span ngRepeat:"cn in userData.cn",->
          #         text "{{cn}}"

# listing edit modal
# use ngRepeat and ngSwitch
VIEW "edit-listing-modal",->
  div id:'editListingModal', class:'modal ',->
    header class:'bar bar-nav',->
      a href:"javascript: void 0", class:'pull-right icon icon-close', ngClick:"promptSaveModal()"
      h1 class:'title',-> "{{template['tp']}}"

    div class: 'bar bar-standard bar-footer footer-tab', style:'display:flex; padding:0px;', ->
      a class:'btn-segment no-border', style:'width:40%;background-color: #E03131;color:white; float:left; line-height: 50px;', ngClick:"submitForm(propForm.$valid);", ngClass:"{'disabled': !((propStatus === 'Active') || (propStatus === 'Inactive')) }", ngIf:"formData['isValid'] == false", ->
        text _ "Save draft"
      a class:'btn-segment no-border', style:'width:40%;background-color: #E03131;color:white; float:left; line-height: 50px;', ngClick:"submitForm(propForm.$valid);", ngClass:"{'disabled': !((propStatus === 'Active') || (propStatus === 'Inactive')) }", ngIf:"!(formData['isValid'] == false)", ->
        text _ "Save"
      a class:'btn-segment no-border', style:'width:40%;background-color: #5CB85C;color:white; float:left; line-height: 50px;', ngClick:"saveAndPromote(propForm.$valid)", ngClass:"{'disabled': !((propStatus === 'Active') || (propStatus === 'Inactive')) }", ->
        text _ "Save & Promote"
        # a class:'inline-splitter',ngShow:"prop.bcf !== 'b' && showPromoteModal", ->
        #   text "&nbsp;"
        # a class:'btn btn-segment no-border ', ngClick:"showPromote(propForm.$valid)", ngClass:"{'disabled':(!formData.id) ||  !((propStatus === 'Active') || (propStatus === 'Inactive')) || (propDataHasChanged && !changeSaved && propForm.$dirty) }", style:"min-width:74px;", ->
        #   text _ "Promote"
      span class: 'pull-right',->
        if @req.getDevType() is 'app'
          a href:"javascript: void 0",class: 'pull-right',style:'padding-right:15px;', ngClick:"showSMB()",  ngClass:"{'disabled': (!formData.id) || (formData['status'] !== 'Active' && formData['status'] !== 'Sold')}", ->
            span class:"icon sprite16-21 sprite16-1-1", style:"width: 21px;height: 21px;"
            span class:"tab-label", ->
              text _ "Share"
        a href:"javascript: void 0", class: 'pull-right',style:'padding-right:15px;', ngClick:"deleteProp(formData)",  ngClass:"{'disabled': (!formData.id) || (formData['status'] !== 'Active')}", ->
          span class:"icon sprite16-21 sprite16-1-4", style:"width: 21px;height: 21px;"
          span class:"tab-label", ->
            text _ "Delete"

    # div class: 'bar bar-standard bar-footer edit-footer', -> #ngClass:"{'disabled': (!formData.id) || (formData['status'] !== 'Active')}"
    #   span class:'pull-left', style:"height: 100%; padding-top: 3px;", ->
    #     a class:'btn btn-segment no-border', ngClick:"submitForm(propForm.$valid);", ngClass:"{'disabled': !((propStatus === 'Active') || (propStatus === 'Inactive')) }", style:"min-width:74px;",->
    #       text _ "Save"
    #     a class:'inline-splitter',ngShow:"prop.bcf !== 'b'",->
    #       text "&nbsp;"
    #     a class:'btn btn-segment no-border',ngClick:"showPromote(propForm.$valid)", ngClass:"{'disabled': !((propStatus === 'Active') || (propStatus === 'Inactive')) || (!formData.id) || (propDataHasChanged && !changeSaved && propForm.$dirty) }", style:"min-width:74px;",->
    #       text _ "Promote"
    #   a class: 'icon pull-right icon-share  pull-right', ngClick:"showSMB()",  ngClass:"{'disabled': (!formData.id) || (formData['status'] !== 'Active' && formData['status'] !== 'Sold') || (propDataHasChanged && !changeSaved && propForm.$dirty)}"



    div class: 'bar bar-standard bar-header-secondary status-tip', ngIf:"! (formData['status'] == 'Active')",->
      div style:"display:flex;  display:-webkit-flex; ",->
        div style:"  vertical-align: top;  padding-top: 10px;",->
          i class:"fa fa-exclamation-circle", style:"color: white; font-size: 22px;"
        div style:"position: absolute;
          padding-left: 22px;
          top: 50%;
          transform: translateY(-50%);",->
          text _("Status",'Listing') + ": {{template['psf'][1]['v'][template['psf'][1]['dbv'].indexOf(formData['status'])] }}. "
          span ngShow:"formData['status'] == 'Inactive'", ->
            text _ "Listing can’t be  presented in public any more after saving."
          span ngShow:"formData['status'] !== 'Inactive'", ->
            text _ "Listing can't be promoted and/or edited any more after saving."

    div class:'content', ->
      form name:'propForm', novalidate:'', ngSubmit:"submitForm(propForm.$valid)", ngClass:"{'disabled': !((propStatus === 'Active') || (propStatus === 'Inactive')) }", ->
        div class:'id-header',->
          div ->
            text _ "ID: "
            span ->
              text "{{formData.id}}"
            span ngShow:"formData.ml_num || formData.sid",->
              text " ({{formData.ml_num || formData.sid}})"
          # div ->
          #   text _ "MLS ID:"
          #   span ->
          #     text "{{formData.ml_num}}"
          # pre ->
          #   text "{{formData}}"
        div class:'id-header',ngShow:'formData.rvkRsn',style:'color:#e03131',->
          div ->
            text _ 'Revoke reason: '
            span ->
              text '{{formData.rvkRsn}}'
        div class:'listing-fields', ->
          for n in [["Price & Status",'psf'], ["Project","pjt"], ["Location","loc"], ["Property Description","pd"], ["Rent Policy", "rp"], ["Amenities","amt"], ["Photo","Drag to rearrange images"], ["Video",'vid'], ["Remarks En","rmrk"], ["Remarks Zh","rmrk_zh"]]
            div class:'field-wrapper', ->
              divParam = class:'fields'
              if n[0] is "Amenities"
                divParam = class:'fields', ngShow:'showAmt'
                div class:'field-header' , style:'color:#2063FD', ngClick:"showAmt = !showAmt",->
                  text _ n[0]
                  i class:'fa',  ngClass:"{'fa-caret-down' : !showAmt, 'fa-caret-up':showAmt}", style:' color:black; vertical-align: middle; margin-left: 7px;'
              else if n[0] is "Photo"
                div class:'field-header' , ->
                  text _ n[0]
                  span class:"pull-right" ,style:"font-size:14px;font-weight: 400;", ->
                    text _ n[1]
              else if n[0] is "Remarks En"
                div class:'field-header' , ngClass:"{'error':(propForm['m'].$invalid && !propForm['m'].$pristine) || (showError && propForm['m'].$invalid ) || (showError && !propForm['m'])}", ->
                  text _ n[0]
              else
                div class:'field-header' , ngShow:"template.#{n[1]}", ->
                  text _ n[0]
              div divParam, ->
                if n[0] is "Photo"
                  div id:'picUrlsList',style:"display: inline;",->
                    span class:'image' , ngRepeat:"img in picUrls track by $index", ->
                      img ngSrc:"{{img}}", ngClick:"previewPic(img)"

                  span class:'image new-img', ngClick:"showImgSelect();",->
                    div ->
                      i class:'icon icon-plus'
                else
                  div class:'row', ngRepeat:"i in template.#{n[1]}", ngShow:"template.#{n[1]}", ngSwitch:"i.tp", ngClass:"{'full-height' : i.fld === 'm' || i.fld === 'm_zh'}", ngHide:"i.fld == 'rgdr' && formData['rtp'] == 'epl'",->

                    div class:'field-name', ngShow:"i.fld !== 'm' && i.fld !== 'm_zh'", ngClass:"{'optional' : i.opt === 1 || i.fld === 'tl'}",->
                      div class:"label", fieldName:"{{i.fld}}", ngClass:"{'error':(propForm[i.fld].$invalid && !propForm[i.fld].$pristine) || (showError && propForm[i.fld].$invalid ) || (showError && !formData['ptp'] && i.fld === 'ptp')}",->
                        text "{{i.fldV}}"
                      span class:'optional', ngIf:'i.opt',->
                        text _ "Opt"
                      #add lock
                      span class:'optional', ngIf:"i.fld == 'tl'",->
                        span class:"fa fa-lock", ngClick:"formData.tllck = !formData.tllck", ngClass:"{'active': formData.tllck}", style:"",
                      # p ngIf:"i.fld === 'rtp'",->
                      #   text _ "Room/Place"
                    div class:'field-input', ngClass:"{'full-width' : i.fld === 'm' || i.fld === 'm_zh','invaError':(propForm[i.fld].$invalid && !propForm[i.fld].$pristine) || (showError && propForm[i.fld].$invalid ) || (showError && !formData['ptp'] && i.fld === 'ptp')}",->

                      div ngSwitchWhen:"drpd2", style:"padding-top: 4px;",->
                        # span style:"display:none", ngInit:"ptpList=i.v" ngInit:"ptpList = i",
                        # opt[3] = v[0] + ' ' + v[1]
                        select ngOptions:"opt[3] for (idx,opt) in ptpList.dbv", style:"",  ngModel:"formData[i.fld]", ngRequired:"i.opt !== 1"
                        #([i.dbv[idx][0], i.dbv[idx][1]]) as as ptpList.v[idx][2]

                      div ngSwitchWhen:"drpd", style:"padding-top: 4px;",->
                        select name:"{{i.fld}}", style:"",  ngModel:"formData[i.fld]", ngRequired:"i.opt !== 1", ->
                          option value:'', ngIf:"i.opt == 1", ->
                            text ""
                          option ngRepeat:"f in i.v track by $index", value:"{{i.dbv[$index]}}", ->
                            text "{{f}}"

                      input style:"", name:"{{i.fld}}", type:"number", ngSwitchWhen:"number", ngModel:"formData[i.fld]", placeholder:"{{i.ph}}", ngRequired:"i.opt !== 1", ngDisabled:"!((propStatus === 'Active') || (propStatus === 'Inactive'))", inputmode:"numeric"
                      input style:"", name:"{{i.fld}}", type:"number", ngSwitchWhen:"numberInt", ngModel:"formData[i.fld]", placeholder:"{{i.ph}}", ngRequired:"i.opt !== 1", ngDisabled:"!((propStatus === 'Active') || (propStatus === 'Inactive'))", inputmode:"numeric", pattern:"[0-9]*"

                      # input name:"{{i.fld}}", type:"number", ngSwitchWhen:"currency", ngModel:"formData[i.fld]", ngRequired:"i.opt !== 1"

                      # input type:"date", ngSwitchWhen:"date" #date-picker="start" min-view="date"
                      span class: 'date-content', ngSwitchWhen:"date", ngClick:"pickDate(i.fld)", style:'min-height: 20px;padding-top: 6px;',->
                        # text "{{i.fld}}"
                        span ngShow:"showDate('tba',i.fld)",->
                          text "{{i.v[0]}}"
                        span ngShow:"showDate('immed',i.fld)",->
                          text "{{i.v[1]}}"

                        span ngShow:"showDate('date',i.fld)",->
                          text "{{formData[i.fld]  | date:'yyyy/MM/dd'}}"
                        span ngShow:"showDate('range',i.fld)", style:" width: 100%;",->
                          text "{{formData[i.fld+'f'] | date:'yyyy/MM/dd' }}"
                          text "-"
                          text "{{formData[i.fld+'t'] | date:'yyyy/MM/dd' }}"
                        input name:"{{i.fld}}", type:"text", ngIf:"i.fld === 'exp'", ngShow:"false", ngModel:"formData[i.fld]"
                        input name:"{{i.fld}}", type:"text", ngIf:"i.fld === 'psn'", ngShow:"false", ngModel:"formData['psn']", ngRequired:"!(formData['psn'] || formData['psnf'])"


                      input type:"text", name:"{{i.fld}}", ngSwitchWhen:"text", ngModel:"formData[i.fld]", ngIf:"(i.fld !== 'addr' && i.fld !== 'sqft')", placeholder:"{{i.ph}}", ngRequired:"(i.fld !== 'addr' && i.fld !== 'sqft') && i.opt !== 1 ", ngDisabled:"!((propStatus === 'Active') || (propStatus === 'Inactive'))"
                      input type:"text", name:"{{i.fld}}", ngSwitchWhen:"title", ngModel:"formData[i.fld]",  placeholder:"{{i.ph}}",   ngFocus:"titleFocus(true)" , ngBlur:"titleFocus(false)"

                      input type:"text", name:"{{i.fld}}", ngSwitchWhen:"text", ngIf:"i.fld === 'sqft'", ngModel:"formData[i.fld]", ngRequired:"i.opt !== 1", pattern:"^[0-9]+(-[0-9]+)?$"
                      input id:'inputAddrRet',type:"text", name:"{{i.fld}}", ngSwitchWhen:"text", ngIf:"i.fld === 'addr'", readonly:'', ngModel:"inputAddrRet", ngClick:"popAddrModal()", ngRequired:"i.opt !== 1"
                      # input type:"text", name:"{{i.fld}}", ngSwitchWhen:"text", ngModel:"formData[i.fld]", ngIf:"i.fld === 'titl'", ngRequired:"i.opt !== 1 ", value:"RealMaster - {{ prop.lp_dol | currency}}, {{prop.addr}} {{prop.apt_num}}, {{ prop.municipality_district}} {{prop.county}} "


                      div ngSwitchWhen:"cnum", class:'cnum', style:"display: -webkit-flex; display: flex; padding-top: 4px;", ->
                        a class:'btn btn-default', ngClick:"setCnum(i.fld, -1)", style:"float:left",-> text "-"
                        input type:"number", name:"{{i.fld}}", ngModel:"formData[i.fld]", ngRequired:"i.opt !== 1", pattern:"[0-9]*"
                        a class:'btn btn-default', ngClick:"setCnum(i.fld,  1)", style:"float:right",-> text "+"

                      span ngSwitchWhen:"bool", ->
                        a class:'btn btn-default', href:"javascript: void 0" ,ngClick:"setBool(i.fld, 1)",
                          ngClass:"{'active' : formData[i.fld] === 1 }",-> "{{i.v[0]}}"#_ "Yes"
                        a class:'btn btn-default', href:"javascript: void 0" ,ngClick:"setBool(i.fld, 0)",
                          ngClass:"{'active' : formData[i.fld] === 0 }",-> "{{i.v[1]}}"#_ "No"
                        #amenities all optional
                        if n[1] isnt 'amt'
                          input name:"{{i.fld}}", type:"text",  ngShow:"false", ngModel:"formData[i.fld]", ngRequired:"i.opt !== 1 "

                      span ngSwitchWhen:"cbool2", ->
                        a class:'btn btn-default', href:"javascript: void 0" , ngClass:"{'active' : formData[i.fld] === i.dbv[0] }",
                          ngClick:"setBool(i.fld, i.dbv[0])",-> "{{i.v[0]}}"
                        a class:'btn btn-default', href:"javascript: void 0" , ngClass:"{'active' : formData[i.fld] === i.dbv[1] }",
                          ngClick:"setBool(i.fld, i.dbv[1])",-> "{{i.v[1]}}"
                        if n[1] isnt 'amt'
                          input name:"{{i.fld}}", type:"text",  ngShow:"false", ngModel:"formData[i.fld]", ngRequired:"i.opt !== 1 "

                      span ngSwitchWhen:"cbool3", style:"display: flex;", class:"cbool3",->
                        a class:'btn btn-default', href:"javascript: void 0" , ngClass:"{'active' : formData[i.fld] === i.dbv[0] }", ngClick:"setBool(i.fld, i.dbv[0])",
                          style:'width:33%; white-space: normal;', -> "{{i.v[0]}}"
                        a class:'btn btn-default', href:"javascript: void 0" , ngClass:"{'active' : formData[i.fld] === i.dbv[1] }", ngClick:"setBool(i.fld, i.dbv[1])",
                          style:'width:33%; white-space: normal;', -> "{{i.v[1]}}"
                        a class:'btn btn-default', href:"javascript: void 0" , ngClass:"{'active' : formData[i.fld] === i.dbv[2] }", ngClick:"setBool(i.fld, i.dbv[2])",
                          style:'width:33%; white-space: normal;', -> "{{i.v[2]}}"
                        if n[1] isnt 'amt'
                          input name:"{{i.fld}}", type:"text",  ngShow:"false", ngModel:"formData[i.fld]", ngRequired:"i.opt !== 1 "

                      span ngSwitchWhen:"cbool3gdr", style:"display: flex;", class:"cbool3", ->
                        a class:'btn btn-default', href:"javascript: void 0" , ngClass:"{'active' : formData[i.fld] === i.dbv[0] }", ngClick:"setBool(i.fld, i.dbv[0])",
                          style:'width:33%; white-space: normal;', -> "{{i.v[0]}}"
                        a class:'btn btn-default', href:"javascript: void 0" , ngClass:"{'active' : formData[i.fld] === i.dbv[1] }", ngClick:"setBool(i.fld, i.dbv[1])",
                          style:'width:33%; white-space: normal;', -> "{{i.v[1]}}"
                        a class:'btn btn-default', href:"javascript: void 0" , ngClass:"{'active' : formData[i.fld] === i.dbv[2] }", ngClick:"setBool(i.fld, i.dbv[2])",
                          style:'width:33%; white-space: normal;', -> "{{i.v[2]}}"
                        # input name:"{{i.fld}}", type:"text",  ngShow:"false", ngModel:"formData[i.fld]", ngRequired:"i.opt !== 1 "


                      div ngSwitchWhen:"textbox",->
                        textarea rows:'4', name:"m", ngModel:"formData[i.fld]", placeholder:_("Remarks"),readonly:'', style:"resize: none;", ngClick:"toggleModal('remarksModal'); ", ngRequired:"true"

                      div ngSwitchWhen:"textbox1",->
                        textarea rows:'4', name:"m_zh", ngModel:"formData[i.fld]", placeholder:_("Remarks"),readonly:'', style:"resize: none;", ngClick:"toggleModal('remarkszhModal'); ", ngRequired:"false"

# modal for create new listing select
VIEW "new-listing-modal",->
  div id:'newListingModal', class:'modal modal-60pc ', style:'z-index:20;',->
    # header class:'bar bar-nav', ->
    #   a class: 'icon icon-close pull-right', href: '#', dataIgnore: 'push', ngClick:"toggleModal('newWecardModal')"
    #   h1 class: 'title',-> _ 'Choose Wecard Type'

    div class:'bar bar-standard bar-footer' , ->#style:'border-top: 0; ',
      # div style:'width:90%; margin-left:5%; border-top: 1px solid #eee; ', ->
      button class:'btn btn-block  btn-long', style:'border:1px none;',ngClick:"toggleModal('newListingModal');hideBackdrop=true", ->
        text _ 'Cancel'

    # header class:'bar  bar-nav', id:'wecardSearchBar', -> #bar-header-secondary bar-standard
      # a class: 'icon fa fa-check pull-right', ngClick:"generate()"
      # input class:'',placeholder:_('Enter '),ngModel:"url",style:'color:black; background-color:#eee'

    div class:'content', style:'background-color:white;',->
      div class:'content-padded' , style:'padding-top: 10px; padding-left: 3px;',->
        div class:'row', ->
          # if @req.hasRole 'realtor'
          div class:'type-wrapper',->
            a href:'javascript: void 0', ngClick:"createListing('exlisting')",->
              if @req.getDevType() is 'app'
                i class:'type-img sprite50-7-1 sprite50-75'
              else
                i class:'type-img sprite50-7-1 sprite50-100'
            p ->
              text _ 'Exclusive Listing'
          div class:'type-wrapper',->
            a href:'javascript: void 0', ngClick:"createListing('assignment')",->
              if @req.getDevType() is 'app'
                i class:'type-img sprite50-7-3 sprite50-75'
              else
                i class:'type-img sprite50-7-3 sprite50-100'
            p ->
              text _ 'Assignment'
          # div class:'row', ->
          div class:'type-wrapper',->
            a href:'javascript: void 0', ngClick:"createListing('rent')",->
              if @req.getDevType() is 'app'
                i class:'type-img sprite50-7-4 sprite50-75'
              else
                i class:'type-img sprite50-7-4 sprite50-100'
            p ->
              text _ 'Rental'

# modal for datepicker
VIEW "date-picker-modal",->
  div id:'datePickerModal', class:'modal modal-40pc ', style:'z-index:20;', ->
    # header class:'bar bar-nav', ->
    #   a class: 'icon icon-close pull-right', href: '#', dataIgnore: 'push', ngClick:"toggleModal('newWecardModal')"
    #   h1 class: 'title',-> _ 'Choose Wecard Type'

    div class:'bar bar-standard bar-footer' , ->#style:'border-top: 0; ',
      # div style:'width:90%; margin-left:5%; border-top: 1px solid #eee; ', ->
      button class:'btn btn-half btn-fill btn-sharp', style:'border:1px none; font-size: 15px;',ngClick:"dateConfirm()", ->
        text _ 'Ok'
      button class:'btn btn-half btn-fill btn-sharp', style:'border:1px none; border-left: 1px solid #ddd; font-size: 15px;',ngClick:"toggleModal('datePickerModal'); hideBackdrop=true", ->
        text _ 'Cancel'

    # header class:'bar  bar-nav', id:'wecardSearchBar', -> #bar-header-secondary bar-standard
      # a class: 'icon fa fa-check pull-right', ngClick:"generate()"
      # input class:'',placeholder:_('Enter '),ngModel:"url",style:'color:black; background-color:#eee'

    div class:'content', style:'background-color:white;',->
      # input type:"datetime",minView:"date", dateTime:'', ngModel:"start", format:"short", onSetDate:"setDate"
      # today = new Date(new Date() - (3600 * 1000 * 25))
      div class:'table-wrapper', datePicker:"date" , view:"date", minView:"date",  ngClass:"{'disabled' : (!dateMode && !rangeMode)}", minDate:"minDate", watchDirectChanges:"true" #,ngModel:'date'
      # input type:"text", dateTime:'', ngModel:"date",  view:"date", dateChange:"changeDate"
      # div datePicker:"date", view:"date", dateChange:"changeDate", timezone:"EST", watchDirectChanges:"true"

      div style:'padding-top: 5px; height: 35px', ngClass:"{'disabled' : !dateMode}", ngShow:"!rangeMode", ->
        # div class:'input-wrapper', style:'width: 90%', ->
        #   input type:'text', placeholder:'Date', class:'', value:"{{date  | date:'yyyy/MM/dd' }}",readonly:'', ngClass:"{'active' : range==='from' }"

      div style:'padding-top: 5px; height: 35px', ngClass:"{'disabled' : !rangeMode}", ngShow:"rangeMode", ->
        div class:'input-wrapper', ->
          input type:'text', placeholder:'From', class:'', value:"{{dateFrom  | date:'yyyy/MM/dd' }}",readonly:'', ngClick:"range = 'from'", ngClass:"{'active' : range==='from', 'disabled' : dateMode}"
        div class:'input-wrapper', ->
          input type:'text', placeholder:'To',   class:'', value:"{{dateTo | date:'yyyy/MM/dd' }}", readonly:'', ngClick:"range = 'to'", ngClass:"{'active' : range==='to', 'disabled' : dateMode}"
      # text "{{date}}"
      div class:'btn-wrapper', ngHide:"dateOnly",->
        div class:"btn-div", ->
          button class:'btn btn-outlined btn-primary', ngClick:"setDate('tba');", ngClass:"{'active' : formData['psn'] === 'tba' && !dateMode && !rangeMode}",->
            text _ "TBA"
        div class:"btn-div", ->
          button class:'btn btn-outlined btn-primary', ngClick:"setDate('immed');", ngClass:"{'active' : formData['psn'] === 'immed' && !dateMode && !rangeMode}",->
            text _ "IMMED"
        div class:"btn-div", ->
          button class:'btn btn-outlined btn-primary', ngClick:"setDate('date')", ngClass:"{'active':  dateMode}",->
            text _ "Date"
        div class:"btn-div", ->
          button class:'btn btn-outlined btn-primary', ngClick:"setDate('range')", ngClass:"{'active':  rangeMode}",->
            text _ "Range"

# modal for ask for save if close without saving
VIEW "save-tip-modal",->
  div id:'saveTipModal', class:'modal modal-60pc ', style:'z-index:20;',->
    # header class:'bar bar-nav', ->
    #   a class: 'icon icon-close pull-right', href: '#', dataIgnore: 'push', ngClick:"toggleModal('newWecardModal')"
    #   h1 class: 'title',-> _ 'Choose Wecard Type'

    div class:'bar bar-standard bar-footer' , ->#style:'border-top: 0; ',
      # div style:'width:90%; margin-left:5%; border-top: 1px solid #eee; ', ->
      button class:'btn btn-half btn-sharp btn-fill', style:'border:1px none;',ngClick:"cancelSaveListing()", ->
        text _ 'Cancel'
      button class:'btn btn-half btn-sharp btn-fill',  style:'border:1px none;border-left: 1px solid #ddd;',ngClick:"submitForm(propForm.$valid); toggleModal('saveTipModal')", ->
        text _ 'OK' #ngDisabled:'',

    # header class:'bar  bar-nav', id:'wecardSearchBar', -> #bar-header-secondary bar-standard
      # a class: 'icon fa fa-check pull-right', ngClick:"generate()"
      # input class:'',placeholder:_('Enter '),ngModel:"url",style:'color:black; background-color:#eee'

    div class:'content', style:'background-color:white;  padding: 10px;',->
      div class:'content-padded' , style:'padding: 10px;',->
        # span ngShow:"tip_save_err", -> "{{saveTip}}"
        # span ngShow:"tip_save_change", ->
        # span ngShow:"tip_save_ready", -> _ "Listing is ready to be promoted, shared and searched in Realmaster Listing Market."
        # span ngShow:"tip_save_inactive", -> _ "Listing status is inactive, can't be promoted, shared and searched in RealMaster Listing Market after saving."
        # span ngShow:"tip_save_other", -> _ "Listing can't be presented in public and edited any longer after saving"
      div style:"display: inline-block; width: 10%",->
        i class:"fa fa-exclamation-circle", style:"font-size: 22px; color: #E03131"
      div style:"display: inline-block; width: 90%",->
        text _ "Listing has been changed, save it?"

# modal for promote within edit page
VIEW "save-promote-modal",->
  div id:'savePromoteModal', class:'modal modal-30pc ', style:'z-index:20;',->
    # header class:'bar bar-nav', ->
    #   a class: 'icon icon-close pull-right', href: '#', dataIgnore: 'push', ngClick:"toggleModal('newWecardModal')"
    #   h1 class: 'title',-> _ 'Choose Wecard Type'

    div class:'bar bar-standard bar-footer' , ->#style:'border-top: 0; ',
      # div style:'width:90%; margin-left:5%; border-top: 1px solid #eee; ', ->
      button class:'btn btn-block  btn-long', style:'border:1px none;', ngClick:"cancelPromoteModal()",->
        text _ 'Cancel'

    # header class:'bar  bar-nav', id:'wecardSearchBar', -> #bar-header-secondary bar-standard
      # a class: 'icon fa fa-check pull-right', ngClick:"generate()"
      # input class:'',placeholder:_('Enter '),ngModel:"url",style:'color:black; background-color:#eee'

    div class:'content', style:'background-color:white;',->
      div ->
        div class:'' , style:'padding: 12px 15px 0 15px;',->
          h4 ->
            text _ "WeMarketing"
          p ->
            text _ "Start to promote your listing and experience RealMaster Marketing Service."

        ul class:"table-view cust", ->
          #if @req.hasRole 'realtor'
          li class:"table-view-cell media", ->
            a class:"navigate-right", ngClick:"promote('58', formData); toggleModal('savePromoteModal')",->
              i class:"media-object pull-left sprite50-45 sprite50-4-4"
              div class:"media-body", ->
                text "58.com"
                p ->
                  text _ "Connect to China market"
          li class:"table-view-cell media", ->
            a class:"navigate-right", ngClick:"promote('market', formData); toggleModal('savePromoteModal')",->
              i class:"media-object pull-left sprite50-45 sprite50-4-2"
              div class:"media-body", ->
                text _ "Listing Market"
                p ->
                  text _ "RealMaster Listing Market"

###
# modal for create new wepage
VIEW "create-wepage-modal",->
  div id:'createWepageModal', class:'modal modal-40pc ', style:'z-index:20;',->
    # header class:'bar bar-nav', ->
    #   a class: 'icon icon-close pull-right', href: '#', dataIgnore: 'push', ngClick:"toggleModal('newWecardModal')"
    #   h1 class: 'title',-> _ 'Choose Wecard Type'

    div class:'bar bar-standard bar-footer' , ->#style:'border-top: 0; ',
      button class:'btn btn-block  btn-long', style:'border:1px none;', ngClick:"toggleModal('createWepageModal',null,1)",->
        text _ 'Cancel'

    div class:'content', style:'background-color:white;',->
      div ->
        div class:'' , style:'padding: 12px 15px 0 15px;',->
          h4 ->
            text _ "Clone to WePage or save as My Listing"
          p ->
            text _ "Select clone destination"

        ul class:"table-view cust", ->
          li class:"table-view-cell media", ->
            a class:"navigate-right", href:'javascript: void 0', ngHref:"/1.5/wecard/edit/{{prop.ml_num || prop.sid}}?shSty=vt", ->
              img class:"media-object pull-left", src:"/img/create_virtualtour.png"
              div class:"media-body", ->
                text _("Virtual Tour") + "(#{_('WePage')})"
                p ->
                  text _ "Flyer style of listing"
          li class:"table-view-cell media", ->
            a class:"navigate-right", href:'javascript: void 0', ngHref:"/1.5/wecard/edit/{{prop.ml_num || prop.sid}}?shSty=blog", ->
              img class:"media-object pull-left", src:"/img/create_topic.png"
              div class:"media-body", ->
                text _("Blog Style") + "(#{_('WePage')})"
                p ->
                  text _ "Blog style of listing"
          li class:"table-view-cell media", ngHide:"prop.bcf == 'b' || prop.pcls == 'b'", ->
            a class:"navigate-right", href:'javascript: void 0', ngHref:"/1.5/promote/mylisting?ml_num={{prop.sid || prop.id}}", ->
              img class:"media-object pull-left", src:"/img/create_exlisting.png"
              div class:"media-body", ->
                text _ "Clone to My Listing"
                p ->
                  text _ "Can edit the listing after clone"
###

# modal for img preview
VIEW "img-preview-modal",->
  div id:'imgPreviewModal', class:'modal modal-fade ', style:'z-index:20;', ->
    # header class:'bar bar-nav', ->
    #   a class: 'icon icon-close pull-right', href: '#', dataIgnore: 'push', ngClick:"toggleModal('newWecardModal')"
    #   h1 class: 'title',-> _ 'Choose Wecard Type'

    # div class:'bar bar-standard bar-footer' , ->#style:'border-top: 0; ',
    #   button class:'btn btn-block  btn-long', style:'border:1px none;',ngClick:"toggleModal('savePromoteModal')", ->
    #     text _ 'Cancel'

    # header class:'bar  bar-nav', id:'wecardSearchBar', -> #bar-header-secondary bar-standard
      # a class: 'icon fa fa-check pull-right', ngClick:"generate()"
      # input class:'',placeholder:_('Enter '),ngModel:"url",style:'color:black; background-color:#eee'
    button class:'btn btn-round fa fa-trash', ngHide:"picRmConfirm", ngClick:"toggleRemovePic()"
    button class:"btn btn-yes  btn-confirm", ngShow:"picRmConfirm", ngClick:"removePic(currentPic)",-> _ "Yes"
    button class:"btn btn-no btn-confirm", ngShow:"picRmConfirm", ngClick:"toggleRemovePic()",-> _ "Cancel"

    div class:'content', ngClick:"toggleModal('imgPreviewModal'); hideBackdrop=true",->
      div class:'content-padded' , style:'padding-left: 0px; text-align: center; padding-top: 20%',->
        img ngSrc:"{{currentPic}}", src:'/img/ajax-loader.gif'

# VIEW "input-address-page", ->
#   css '/css/mylisting.css'
#   div id:'inputAddressModal', ->
#     text ckup 'input-address-pane'
#   coffeejs {vars:{}}, ->
#     console.log 'here'
#     null

VIEW "input-address-pane",->
  header class:'bar bar-nav', ->
    a class: 'icon icon-close pull-right', href: '', ngClick:"toggleModal('inputAddressModal')", dataIgnore: 'push'
    h1 class: 'title',-> _ 'Set Address'

  div class:'', style:"padding:0;position: relative;top: 45px; height: 220px;",->
    form id:"addr-search", class:'',->
      input type:'text', placeholder:_("Input Address"), ngModel:"inputAddr", style:"", id:"inputAddr", onclick:"this.setSelectionRange(0, this.value.length)"
      if @req.isChinaIP()
        i class:'btn btn-default btn-nooutline fa fa-search', ngClick:'searchAddr();'
      else
        i class:'btn btn-default btn-nooutline fa fa-times-circle', ngClick:"inputAddr = ''"
    div ->
      input type:'text', placeholder:_("Postcode"), ngModel:"inputZipRet", placeholder:"Zip Code", maxlength:"6", ngDisabled:"!tmpData.city"
      i class:"tip fa ",style:"top: 50px", ngClass:"{'fa-check-circle' : fullAddr, 'fa-exclamation-circle':!fullAddr }"
    button class:"btn btn-fill  btn-sharp btn-positive btn-split", style:"height:30px; width: 90%; left:5%; margin-top: 15px!important;", ngClick:"closeAddrModal()", -> #ngDisabled:"!fullAddr",
      text _ "Ok"
    div style:"text-align: center; padding-top: 10px; color:#a9a9a9; font-size: 14px;",->
      text _ "Drag & Drop the Map Marker to Pick Address"

  div class:'', style:"position: relative;", ->
    div class:'content-padded' ,  style:'margin:0px 0px 0px 0px;  display:block;',-> #height:100%;
      div id:'id_d_map', style:'',-> #height:100%
        img ngSrc:"{{mapImg}}"

# modal for input address in google
VIEW "input-address-modal",->
  div id:'inputAddressModal', class:'modal ', style:'z-index:20;', ->
    text ckup 'input-address-pane'

# ul for service city select
VIEW "city-select-modal",->
  div id:'citySelectModal', class:'modal ', style:'z-index:20;', ->
    header class: 'bar bar-nav', ->
      a class: 'icon icon-close pull-right nobusy', onclick:"toggleModal('citySelectModal')", href: 'javascript:void 0'
      h1 class: 'title',-> _ 'Select City'
    div class: 'content', ->
      ul class:'table-view', style:"margin-top: 0px;",->
        li class:'table-view-divider', ->
          text _ "Popular Cities"
        li class:'table-view-cell match', ngRepeat:"city in favCities", ->
          # {{city.k}}&cn={{city.n}}
          a ngHref:"#", ngClick:"addServiceCity(city.k, city.o);", ->
            text  "{{city.o}} {{city.n}}"
          # input type:"checkbox" ,ngChecked:"userData.ck.indexOf(city.k) > -1" , ngClick:"addServiceCity(city.k, city.o);"
          i class:"checkbox pull-right fa ", ngClick:"addServiceCity(city.k, city.o);", style:"", ngClass:"{'fa-square-o' : !(userData.ck.indexOf(city.k) > -1), 'fa-check-square-o active': userData.ck.indexOf(city.k) > -1 }"

      ul class:'table-view', ->
        li class:'table-view-divider', ->
          text _ "Cities"
        li class:'table-view-cell match', ngRepeat:"city in extCities", ->
          a ngHref:"#", ngClick:"addServiceCity(city.k, city.o);", ->
            text "{{city.o}} {{city.n}}"
          # input type:"checkbox" ,ngChecked:"userData.ck.indexOf(city.k) > -1" , ngClick:"addServiceCity(city.k, city.o);"
          i class:"checkbox pull-right fa ", ngClick:"addServiceCity(city.k, city.o);", style:"", ngClass:"{'fa-square-o' : !(userData.ck.indexOf(city.k) > -1), 'fa-check-square-o active': userData.ck.indexOf(city.k) > -1 }"

# remarks editing modals
VIEW "remarks-modal", ->
  div id:'remarksModal', class:'modal ', style:'z-index:20;', ->
    header class: 'bar bar-nav', ->
      a class: 'icon icon-close pull-right nobusy', onclick:"toggleModal('remarksModal');", href: 'javascript:void 0'
      h1 class: 'title',-> _ 'Remarks'
    div class: 'content', ->
      textarea row:'10', name:"ad_text", placeholder:"Remarks", style:"resize: none; height:100%; margin-bottom:0; padding: 10px 5px;", ngModel:"formData['m']"

VIEW "remarkszh-modal", ->
  div id:'remarkszhModal', class:'modal ', style:'z-index:20;', ->
    header class: 'bar bar-nav', ->
      a class: 'icon icon-close pull-right nobusy', onclick:"toggleModal('remarkszhModal');", href: 'javascript:void 0'
      h1 class: 'title',-> _ 'Remarks'
    div class: 'content', ->
      textarea row:'10', name:"ad_text", placeholder:"Remarks", style:"resize: none; height:100%; margin-bottom:0; padding: 10px 5px;", ngModel:"formData['m_zh']"

VIEW "select-contact-modal", ->
  div id:'selectContactModal', class:'modal ', style:'z-index:20;background: #eee;', ->
    header class: 'bar bar-nav', ->
      a class: 'icon icon-close pull-right nobusy', ngClick:"toggleModal('selectContactModal');", href: 'javascript:void 0'
      h1 class: 'title',-> _ 'Select Contact'
    div  class:'selContactContent', ->
      div class:'creatBtnCard', ->
        span ->
          text _ 'New Contact'
        span class:'sprite16-18 sprite16-1-9 pull-right',style:'color: #e03131;font-size: 20px;',ngClick:"isAddContact = true"
      div style:'padding-top:65px;height:100%;overflow:scroll;', ->
        div class:'contactCard',ngRepeat:'contact in contactList track by $index',->
          div ngClick:'selectContact(contact.nm,contact.mbl)',->
            p style:'color: #333;',->
              span ->
                text _"Name"
              span style:'overflow:hidden;text-overflow:ellipsis;',->
                text ":  {{contact.nm}}"
            p style:'color: #333;margin-bottom:0px;',->
              span ->
                text _"Phone"
              span ->
                text ":  {{contact.mbl}}"
          div ->
            span ngIf:'!contact.isShowDelete',->
              span class:'sprite16-18 sprite16-5-3',style:'margin-right:15px;',ngClick:"editContact(contact._id,contact.nm,contact.mbl)"
              span class:'sprite16-18 sprite16-4-8',ngClick:"confirmDeletion($index)"
            span class:'btn btn-nooutline pull-right',style:'padding:0px;margin-top:-3px;',ngIf:'contact.isShowDelete',->
              span class:'pull-right btn btn-nooutline',style:'border:1px solid #fff;',ngClick:'cancelDelete($index)',->
                text _"Cancel"
              span class:'pull-right btn btn-negative',style:'border:1px solid #fff;',ngClick:'deleteContact(contact._id,$index)',->
                text _"Delete"
    div ngIf:'isAddContact',->
      div class:'backdrop'
      div class:'inputContact',->
        div class:'inputCell',->
          input type:'text', placeholder:_("Name"), ngModel:"contactInfo.nm"
        div class:'inputCell',->
          input type:'text', placeholder:_("Phone"), ngModel:"contactInfo.mbl"
        div class:'contactSaveBtn',->
          button class:'btn btn-positive btn-half btn-sharp btn-fill',style:"background-color:#e30303;font-size:17px;", ngClick:"saveContact()",->
            text _ "Add"
          button class:'btn btn-positive btn-half btn-sharp btn-fill',style:"background-color:#fff;color:#333;font-size: 17px;", ngClick:"cancelSave()",->
            text _ "Cancel"