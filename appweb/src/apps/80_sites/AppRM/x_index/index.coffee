helpers = INCLUDE 'lib.helpers'
helpersHttp = INCLUDE 'lib.helpers_http'
{formatCmntTs} = INCLUDE 'lib.helpers_date'
cfg = CONFIG(['share','serverBase'])
debug = DEBUG()
gShareHostNameCn = cfg.share.hostNameCn or 'realmaster.cn'
gShareHostNameCnRegex = helpers.str2reg gShareHostNameCn
ADDPLUGIN 'shareHostNameCn',()->
  gShareHostNameCn

# propSearch = INCLUDE 'libapp.propSearch'
libproperties = INCLUDE 'libapp.properties'
libUser = INCLUDE 'libapp.user'
mapServer = INCLUDE 'lib.mapServer'
libAppVer = INCLUDE 'libapp.appVer'
ObjectId = INCLUDE('lib.mongo4').ObjectId
debug = DEBUG 'index'

ShowingModel = MODEL 'Showing'

Properties = MODEL 'Properties'
UserModel = MODEL 'User'
ForumModel = MODEL 'Forum'
TrebKeyModel = MODEL 'TrebKey'
# UserProfile = COLLECTION 'chome','user_profile'

# accessAllowed = DEF '_access_allowed'
getHasNewMsgs = DEF 'getHasNewMsgs'
getNewFormInput = DEF 'getNewFormInput'
getBanners = DEF 'getBanners'
listingPicUrls = libproperties.listingPicUrls
wordpressHelper = INCLUDE 'libapp.wordpressHelper'
ALL_FIELDS =  libproperties.ALL_FIELDS
getNextTopListings = DEF 'getNextTopListings'
getHomeServices = DEF 'getHomeServices'
getHeadLinesHome = DEF 'getHeadLinesHome'
# puts = console.log
# verGTE = DEF 'verGTE'
ProvAndCity = MODEL 'ProvAndCity'
propTranslate = INCLUDE 'libapp.propertiesTranslate'
objectCache = INCLUDE 'libapp.objectCache'
{deepCopyObject} = INCLUDE 'lib.helpers_object'

# TODO: @refactor once locale set(or req.l10n), cannot set again, wont work
# FILTER 'setLocale',(req)->
#     @

# cityListObject = getCityListObject()
# IndexAds = COLLECTION 'index_ads'
# ChatDb = COLLECTION 'chat'
# Wecard = COLLECTION 'wecard'
# isAllowed = DEF '_access_allowed'
# getCityList = DEF 'getCityList'
# getCityFavList = DEF 'getCityFavList'
# getCityExtraList = DEF 'getCityExtraList'
# getCityListObject = DEF 'getCityListObject'

filterBanners = DEF 'filterBanners'
TRUSTEDASSIGM = 'trustedAssigm'

ADDPLUGIN 'getCordovaJs',({pubOnly=false,wxcfg,vctx})->
  req = @
  jsArray = []
  jsScript = []
  # app and test env
  if (req.getDevType() is 'app') and (not pubOnly)
    appVer = req.getAppVer()
    # test/dev env
    if 'appDebug' is appVer
      jsArray.push '/js/rmsrv5web.min.js'
    # react versions of app
    else if req.verGTE('5.7.0')
      jsScript.push "var gShareHostNameCn = '#{req.shareHostNameCn()}';"
      jsArray.push '/js/rmsrv5wk.min.js?v=2'
    # TODO: should be deprecated, this is used for cordova app
    else
      jsArray.push '/js/rmsrv.min.js'
  # inside wehcat opened browser
  else if req.isWeChat()
    jsArray.push '/js/wx/jweixin-1.6.0.js'
    if wxcfg or vctx?.wxcfg
      jsScript.push "var wxConfig = #{JSON.stringify wxcfg};"
    jsArray.push '/js/wechatsrv.min.js'
  # normal browser
  else
    jsArray.push '/js/srv.min.js'
  [jsArray,jsScript]

VIEW 'cordovaJs', ->
  [jsArray,jsScript] = @req.getCordovaJs({pubOnly:@pubOnly,wxcfg:@wxcfg,vctx:@vctx})
  noasync = @noasync
  for i in jsScript
    if i
      script -> i
  for i in jsArray
    js i
  # injectScript = (src)->
  #   if noasync
  #     js src
  #   else
  #     script src:src, async:"", defer:""
  ###
  if (@req.getDevType() is 'app') and (not @pubOnly)
    appVer = @req.getAppVer()
    # appVerNum = @req.getAppVerNumber()
    # TODO: this exists multiple places, DRY
    # NOTE: remove < v5.6 support
    # if ('app' isnt appVer) and ('appDebug' isnt appVer) and (not @req.verGTE('5.0.0')) # simulate app from browser, use this key
    #   if @req.isIOS()
    #     if @req.verGTE('3.2.0')
    #       js '/js/ios32.min.js' #'/js/ios32/cordova.js'
    #     else
    #       js '/js/ios.min.js' # '/js/ios/cordova.js'
    #   else if @req.isAndroid()
    #     js '/js/android.min.js' # '/js/android/cordova.js'
    if 'appDebug' is appVer
      js '/js/rmsrv5web.js'
      # text "<script src='/js/rmsrv5web.js' async></script>"
      # injectScript('/js/rmsrv5web.js')
    else if @req.verGTE('5.7.0')
      # text "<script type='text/javascript'>var gShareHostNameCn = '#{@req.shareHostNameCn()}';</script>"
      # TODO: page constant datas
      # TODO: theme can ckup cordovaJs, noneed to write own
      script -> "var gShareHostNameCn = '#{@req.shareHostNameCn()}';"
      # if /realexpert/.test @req.shareHostNameCn()#@req.hostname
      #   js '/js/rmsrv5wkcn.min.js'
      # else
      js '/js/rmsrv5wk.min.js?v=1'
      # text "<script src='/js/rmsrv5wk.min.js' async></script>"
      # injectScript('/js/rmsrv5wk.min.js')
      # else if @req.verGTE('5.0.0')
      # alertError?
      # js '/js/rmsrv5.min.js'
      # text "<script src='/js/rmsrv5.min.js' async></script>"
      # injectScript('/js/rmsrv5.min.js')
    else
      js '/js/rmsrv.min.js'
      # text "<script src='/js/rmsrv.min.js' async></script>"
      # injectScript('/js/rmsrv.min.js')
  else if @req.isWeChat()
    js '/js/wx/jweixin-1.6.0.js'
    if wxcfg = @wxcfg or @vctx?.wxcfg
      script -> "var wxConfig = #{JSON.stringify wxcfg};"
    # if gShareHostNameCnRegex.test @req.hostname
    #   js '/js/wechatsrvcn.min.js'
    # else
    js '/js/wechatsrv.min.js'

    # NOTE: wx.ready wxOnShare, page may not ready
    # text "<script src='/js/wechat.min.js' async defer></script>"
  else
    js '/js/srv.min.js'
    # text "<script src='/js/srv.min.js' async></script>"
  ###
# general error page
# @title : error title
# @err   : error description
# @solu  : error tips/solutions
VIEW 'generalError', ->
  if @req.getDevType() is 'app'
    header class: 'bar bar-nav', ->
      #a class: 'icon icon-left pull-left', href: '/1.5/index', style:'color:white;',dataTransition: 'slide-out'
      h1 class: 'title', -> @title or _ 'Error'
    div class: 'content', ->#style:'margin:10px;',->
      div class: '', style: 'margin:10px;', ->
        p style:'text-align:center;color:red;',->
          text if @err then _ @err else if @err_tran then @err_tran else  _ 'Page Not Found'
        unless @noReturn
          a class: 'btn btn-positive btn-block btn-long',
          href: 'javascript:void(0);',
          onclick: 'javascript:window.history.back();', -> _ 'Go Back'
          a class: 'btn btn-positive btn-block btn-long',
          href: '/home/<USER>', -> _ 'Go To Index'
  else
    div id: "errorPage", style:"display:flex;align-items:center;height:calc(100vh - 71px);", ->
      div class: "container", ->
        img src:'/web/imgs/oops.jpg'
        h3 ->
          span ->
            text if @err then _ @err else if @err_tran then @err_tran else  _ 'Page Not Found'
          if @showlogin
            span style: 'font-size: 12x;', ->
              a style: '', href: '/www/login', -> _ 'Login'
        unless @noReturn
          div style:'font-size: 16px;', ->
            a style: 'display:inline-block;background-color:#dd1700;color:#fff;padding:10px;border-radius:5px;', href: '/', -> _ 'Go To Homepage'
    css '/web/css/common.css'
    css '/web/css/bootstrap.min.css'
    js '/js/jquery-2.1.1.min.js'
    js 'https://www.google.com/recaptcha/api.js'
    js 'https://static.geetest.com/static/tools/gt.js'
    js '/web/packs/commons.js'
    js '/web/packs/errorPage.js'
    js '/web/packs/bootstrap.min.js'

VIEW 'E404', ->
  text ckup 'generalError', {err_tran:__("'%s' is not found on the server %s", @url, @host)}
  #,'_',{noUserModal:true,noCordova:true}

VIEW 'clickDirect',->
  coffeejs {}, ->
    clickDirect = (isapp, url,isNative)->
      # console.log isNative
      if (isapp)
        callBackStr = ":ctx:cmd-redirect:#{url}";
        # // console.log('window.rmCall: '+callBackStr);
        if isNative
          # console.log url.split('?')?[1]
          callBackStr = ':ctx:'+url.split('?')?[1]
        if window.rmCall
          window.rmCall(callBackStr)
      else
        window.location = url
    null
# general Message page
# @title : title
# @msg   : message
# @link  : {nm: button name, url: jump url}

VIEW 'generalMessage', ->
  unless @nobar
    header class: 'bar bar-nav', ->
      a class: 'icon fa fa-back pull-left',
      href: 'javascript:void(0);',
      onclick: 'javascript:window.history.back();'
      h1 class: 'title', -> @title or _ 'Message'
  div class: 'content', ->#style:'margin:10px;',->
    div class: '', style: 'margin:10px;', ->
      p style:'text-align:center;',->
        text @msg if @msg
      if @link
        if @link.onclick
          a class: 'btn btn-positive btn-block btn-long',
          onclick: @link.onclick,
          href: (@link.url or 'javascript:void(0);'), ->
            text @link.nm
        else
          a class: 'btn btn-positive btn-block btn-long', href: @link.url, -> text @link.nm
  text ckup 'clickDirect'

# share tips Message page
# @title : title
# @msg   : message，翻译过的消息
VIEW 'shareMessage', ->
  header class: 'bar bar-nav', ->
    h1 class: 'title', -> @title or _ 'Share'
  div class: 'content',style:'background-color: #F3F3F3;', ->
    div style: 'position: absolute;top: 20%;margin:10px;width:95%;', ->
      p style:'text-align:center;margin-bottom:50px;',->
        span class:'fa fa-exclamation-circle',style:'color:#F6CC31;font-size:100px;'
      p style:'text-align:center;color:#000;font-size: 15px;',->
        text @msg if @msg

# Server maintenance tips
VIEW 'E503', ->
  header class: 'bar bar-nav', ->
    h1 class: 'title', -> _ 'RealMaster'
  div class: 'content', ->
    div style: 'position: absolute;top: 30%;margin:10px;width:95%;', ->
      p style:'text-align:center;margin-bottom:45px;',->
        img src:'/img/server_maintenance.png'
      p style:'text-align:center;color:#9a9a9a;font-size: 17px;',->
        text _'Server maintenance in progress…'

topPropertiesByProvCache = DEF 'topPropertiesByProvCache'
newPropsByCityCache = DEF 'newPropsByCityCache'

#TODO: get prov return first matched city
getProvFromCity = (city)->
  return 'ON'


defaultCityToronto = DEF 'defaultCityToronto'
getCityBriefInfo = DEF 'getCityBriefInfo'
getUserCity = DEF 'getUserCity'

# getCurCityStat = (req, resp, cb)->
#   UserModel.appAuth {req,resp}, (user) ->
#     # curCity = user?.city or req.session.get('city') or defaultCityToronto
#     idx = parseInt(req.param('idx')) or 0
#     cities = user?.cities or req.session.get('cities') or []
#     nCities = cities.slice()
#     # TODO: @SAM DRY
#     if curCity = user?.city or curCity = req.session.get('city') or curCity = defaultCityToronto
#       nCities.unshift(curCity)
#     idx = idx%(nCities.length)
#     userCity = getUserCity(req, user, nCities, idx)
#     cityName = userCity?.city #cityListObject[cityKey].n
#     provName = userCity?.prov
#     opt =
#       city:   cityName
#       prov:   provName
#       itvl:   'W'
#       now:    new Date()
#       saletp: 'Sale'
#     return cb('no city and prov') unless opt.city and opt.prov
#     getCityBriefInfo req, opt, (err, cityinfo)->
#       return cb err,cityinfo

#    var async = require('async'); async.each()
#    require("promise").Promise;
# @return: {ok:1, l:[{prop}]}

isRencentCache = (ts)->
  ((new Date() - new Date(ts)) < 3600*1000)

# RCMD_PROP_FIELDS = {
#   sid:1
#   trbtp:1
#   lp:1
#   lpr:1
#   lpunt:1
#   saletp:1
#   addr:1
#   city:1
#   prov:1
#   bdrms:1
#   tbdrms:1
#   bthrms:1
#   gr:1
#   tgr:1
#   daddr:1
#   phosrc:1
#   picUrl:1
#   pho:1
#   oh:1
#   oh0:1
#   oh1:1
#   ddfID:1
#   topup_pts:1
#   topTs:1
#   priceChange:1
# }
getRecommendListings = (req, resp, cb) ->
  error = (err) ->
    return cb {err:err.toString(), ok:0}
  rcmdTypeMap = {
    new:req.l10n('New Listing'),
    rcmd:req.l10n('Recommend'),
    ad:req.l10n('TOP','advertise'),
    pc:req.l10n('New Price')
  }
  setOhDate = (p)->
    ohdate = libproperties.nearestOhDate p
    if ohdate
      p.ohdate = ohdate.f.split(' ')[0]
    # if (ohdate = if p.ohz?.length then p.ohz[0].f else null)
    #   p.ohdate = ohdate.split(' ')[0]
  req.setupL10n() unless req._ab and req._
  UserModel.appAuth {req,resp}, (user) ->
    # cityKey = user?.ck or req.session.get("ck") or "01._DT"
    idx = parseInt(req.param('idx')) or 0
    # if req.body?.src !='wordpress'
    cities = user?.cities or req.session.get('cities') or []
    nCities = cities.slice()
    if curCity = (user?.city or req.session.get('city') or defaultCityToronto)
      nCities.unshift(curCity)
    idx = idx%(nCities.length)
    userCity = getUserCity(req, user, nCities, idx)
    cityName = userCity?.city #cityListObject[cityKey].n
    provName = userCity?.prov
    retCity = {o:userCity?.city, n:req.l10n(userCity?.city), p:userCity?.prov, lat:userCity?.lat, lng:userCity?.lng}
    result = []
    provName = getProvFromCity(cityName) unless provName
    provName = ProvAndCity.getProvAbbrName(provName) if not /[A-Z]{2}/.test provName
    pc = provName+':'+cityName
    # console.log('+++++',provName,pc)
    top = getNextTopListings({prov:provName,ptype:'Residential'}) or []
    cachedTopProp = {l:top}#topPropertiesByProvCache[provName]
    cachedNewProp = newPropsByCityCache[pc]
    if req.cookies[libUser.APPMODE] is 'rm'
      cachedTopProp.l = cachedTopProp.l.slice(0,5)
      result = propTranslate.translate_prop_list req, cachedTopProp.l, rcmdTypeMap
      return cb {ok:1, l:result, city:retCity}
    if (cachedTopProp.l?.length > 4)
      cachedTopProp.l = cachedTopProp.l.slice(0,5)
      for p in cachedTopProp.l
        setOhDate(p)
      result = propTranslate.translate_prop_list req, cachedTopProp.l, rcmdTypeMap
      return cb {ok:1, l:result, city:retCity}
    if (cachedTopProp and cachedNewProp)
      if isRencentCache(cachedNewProp.ts)#isRencentCache(cachedTopProp.ts) and
        cachedTopProp.l ?= []
        cachedNewProp.l ?= []
        tmp = cachedTopProp.l.concat(cachedNewProp.l)
        tmp = tmp.slice(0,5)
        for p in top
          setOhDate(p)
        ret = propTranslate.translate_prop_list(req, tmp, rcmdTypeMap)
        return cb {ok:1, l:ret, city:retCity}
    opt = {
      projection:ALL_FIELDS
      city:cityName
      prov:provName
    }
    #ts = Date.now()
    # Toplisting no city, just prov | city:city,
    Properties.getRecentPropsByProvAndCity opt, (err, ret)->
      return cb err if err
      #helpers.showTs ts,"RecentPropsByProvAndCity"
      # topPropertiesByProvCache[provName] = {l:top, ts:new Date()} if provName
      newPropsByCityCache[pc] = {l:ret, ts:new Date()} if pc
      tmp = top.concat(ret)
      tmp = tmp.slice(0,5)
      for p in tmp
        delete p.favUsr
        delete p.cnt
        # delete p.favU
        setOhDate(p)
      result = (propTranslate.translate_prop_list req, tmp, rcmdTypeMap) or []
      return cb {ok:1, l:result, city:retCity}
    # getPopularListing cityName, prov, (err, ret)->
    #   return error err if err
    #   if ret?.length < 3
    #     getRecentPropsByProvAndCity cityName, prov, (err, ret)->
    #       return cb err if err
    #       result = propTranslate.translate_prop_list req, ret, req.l10n 'New Listing'
    #       popular_cities_list_detail_cache[pc] = {l:result, type:req.l10n 'New Listing'}
    #       return cb {ok:1, l:result}
    #   else
    #     l = []
    #     for i in ret
    #       for m in i.ml_num #src = 'TREB' AND sid = ?
    #         if /[A-Z]\d{6,}/.test(m) or /^TRB[A-Z]\d{6,}/.test(m) or /^DDF[a-zA-Z0-9]{6,}/.test(m)
    #           l.push(m)
    #     getListingDetail l, (err, ret)->
    #       return error err if err
    #       ret = ret.slice(0,5)
    #       popular_cities_list_detail_cache[pc] = {l:ret, type:req.l10n 'Hot'}
    #       result =  propTranslate.translate_prop_list req, ret, req.l10n 'Hot'
    #       cb {ok:1, l:result}

DEF 'getRecommendListings',getRecommendListings

# before /1.5/
# used in propDetail.coffee / dlpage.coffee
# parameter:
VIEW 'userBriefInfoNew', ->
  div class:"", id:"userBriefInfo", ngShow:"user", ngClass:"{'vip':user.vip}",->
    div class:'b-info', ->
      div class:"info-wrapper" , ->
        div class:"avt", ->
          img src:"/img/logo.png", ngSrc:"{{user.avt}}", dataRole:'ctct-avt', referrerpolicy:"same-origin"
        div class:"fn", ->
          div style:"font-weight:bold; font-size: 19px;", ->
            span dataRole:'ctct-nm', ->
              text "{{user.fnm ? user.fnm : (user.fn + ' ' + user.ln)}}" # TODO: set user.fullNameOrNickname from @req.fullNameOrNickname
            img dataRole:'ctct-realtor', src:"/img/realtor.png", ngShow:"user.realtor"
            span class:"verified", ngShow:"user.vip", dataRole:"ctct-vip", ->
              img src:"/img/icon_V.png"
              text _ "Verified", "realtorVerify" #实名认证

          div style:"margin-top:3px", ->
            p style:"color:#777",->
              span dataRole:'ctct-cpny_pstn', ->
                text "{{user.cpny_pstn}}"
              br()
              span dataRole:'ctct-cpny', ->
                text "{{user.cpny}}"
              br()
      div dataRole:'ctct-itr', class:"ctct-itr", ->
        text "{{user.itr}}"
      div class: "hr", ->
      div class:"action", ->
        div class:"info", ->
          div ->
            i class:"fa fa-phone-square", style:"font-size: 18px;"
            span dataRole:'ctct-mbl', ->
              text "{{user.mbl}}"
          div ->
            i class:"fa fa-envelope"
            a href:"mailto:" , ngHref:"mailto:{{user.eml}}", dataRole:'ctct-eml', ->
              text "{{user.eml}}"
          div ->
            i class:"fa fa-globe", style:"font-size: 18px;"
            a href:"", ngHref:"{{user.web}}", dataRole:'ctct-web', ->
              text "{{user.web}}"

        div class:"call", ->
          a href:"tel:", ngHref:"tel:{{user.mbl}}", dataRole:'ctct-mbl-call', ->
            div ->
              img src:"/img/icon_call.png"
            div ->
              text _ "Call Me"

    div class:"wx-info", ->
      div class:"info", ->
        div ->
          i class:"fa fa-wechat"
          text _ "WeChat"
          text " ID:"
        div ngClick:"qrcd=true;", ->
          i class:"fa fa-none"
          a href:"#", class:"qrcd-show", ->
            span dataRole:'ctct-wx', ->
              text "{{user.wx}}"

        div style:"margin-top:10px;",  ngShow:"user.wxgrp", class:"ctct-wxgrp", ->
          i class:"fa fa-none"
          text _ "Wechat Group"
          text " ID:"
        div ngClick:"qrcd=true;", ngShow:"user.wxgrp", class:"ctct-wxgrp", ->
          i class:"fa fa-none"
          a href:"#", class:"qrcd-show", ->
            span dataRole:'ctct-wxgrp', ->
              text "{{user.wxgrp}}"

      div class:"call", ngShow:"user.qrcd || user.grpqrcd", ->
        a href:"javascript:void 0", ngClick:"qrcd=true;", class:"qrcd-show", ->
          div ->
            img src:"/img/icon_WeChat_Fingerprint_Cir.png"
          div ->
            text _ "Follow Me"

    div class:"other-info", ngShow:"user.wurl || user.fburl || user.twturl", ->
      a href:"#", ngHref:"{{user.wurl}}",   ngShow:"user.wurl",   style:"color:#E6162D", class:"fa fa-weibo",    dataRole:"ctct-wurl"
      a href:"#", ngHref:"{{user.fburl}}",  ngShow:"user.fburl",  style:"color:#3b5998", class:"fa fa-facebook", dataRole:"ctct-fburl"
      a href:"#", ngHref:"{{user.twturl}}", ngShow:"user.twturl", style:"color:#55acee", class:"fa fa-twitter",  dataRole:"ctct-twturl"

#this modal must be in body for auto scan barcode
VIEW 'wechat-qr-modal', ->
  # div id:'wechatQrModal', class:'modal ', style:'z-index:20;', ->
  #   header class: 'bar bar-nav', ->
  #     a class: 'icon icon-close pull-right nobusy', ngClick:"qrcd=true;", href: 'javascript:void 0'
  #     h4 class: '',-> _ 'WeChat Follow'
  #     div -> _ "Long press or Scan QR-Code"
  #   div class: 'content', ->
  #     div ->
  #       div ->
  #         img src:"/img/p1.png", ngSrc:"{{user.qrcd}}"
  #       div ->
  #         text _ "WeChat"
  #         text _ "QR-Code"

  #     div style:"margin-top: 15px;",->
  #       div ->
  #         img src:"/img/p1.png", ngSrc:"{{user.qrcd}}"
  #       div ->
  #         text _ "Wechat"
  #         text _ "Group"
  #         text _ "QR-Code"
  div id:'wechatQrModal', ngClass:"{'active':qrcd}", ngShow:'qrcd', style:"", ->
    div class: 'bcontent', ->
      a class: 'icon icon-close pull-right nobusy qrcd-hide', ngClick:"qrcd=false;", href: 'javascript:void 0'
      header class: '', ->
        h4 class: '', -> _ 'WeChat Follow'
        div -> _ "Long press or Scan QR-Code"
      div ngShow:"user.qrcd", id:"qr_code_img", ->
        div ->
          img  src:"/img/p1.png", ngSrc:"{{user.qrcd}}", referrerpolicy:"same-origin"
        div ->
          text _ "WeChat QR-Code"

      div ngShow:"user.grpqrcd", id:"grp_qr_code_img", ->
        div ->
          img  src:"/img/logo.png", ngSrc:"{{user.grpqrcd}}", referrerpolicy:"same-origin"
        div ->
          text _ "Wechat Group QR-Code"

VIEW 'userModal', ->
  text ''

VIEW 'getAppBar', ->
  divParam = style:"background: rgba(102, 102, 102,0.6); border-top:1px none;"
  if @hide
    divParam = style:"background: rgba(102, 102, 102,0.6); border-top:1px none; display:none;"
  div id:'getAppBar', class:"bar bar-standard bar-footer", divParam, ->
    adtext = _ ('Detail In App')
    if @owner
      aParam = href:"/getapp?uid=" + @req.getShareUID(@owner) + "&openapp=1&" + @params #"/app-download?uid=" + @req.getShareUID @owner
    else if @dlApp
      aParam = href:'/getapp' + (if @params then '?' + @params else '')
      adtext = _ ('Get App')
    else
      aParam = href:"/getapp?openapp=1&" + @params #"app-download"
    a class:"adLinkHref", aParam, ->
      img src:"/img/logo.png" , class:"pull-left", height:"30px", width:"30px", style:'margin-top:8px'

    div style:"display:inline-block;  line-height: 16px;", ->
      div style:"color:white; font-size:14px; margin-left: 10px; padding-top: 8px;", ->
        text _ "RealMaster"
        span style:'font-size: 12px; margin-left:10px;', ->
          text _ "Canada"
      # br()
      div style:"color:white; margin-left: 10px; font-size:10px", ->
        text _ "Your Dream House Starts Here", "getAppBar"
        # text _ "二手 | 出租 | 学区, 找房就用房大师"
    # if true#@isWeChat
    #   aParam = href:'javascript:void 0', onclick:"document.querySelector('#content-cover').style.display = 'block'"
    a class:'pull-right btn adLinkHref', id:'adLinkHref', style:"opacity:1; background-color:red; color:white;", aParam, ->
      text adtext

#used in prop detail under /mp
# VIEW 'disclaimer', ->
#   div  style:'border-top: 1px solid #ccc; margin-top:20px;padding:10px 8px 2px 8px;line-height: 1em; ', ->
#     span  style:'font-size:10px;color:#ADACAC', ->
#       text _ "RealMaster does not guarentee the accuracy of information available for listings and schools."
#       text " "
#       text _ "Double-checking with Real Estate Board and District School Board is strongly recommended."
#   div style:'height:40px;color:black;text-align:center; border-radius:0px; margin-top:0px;padding-top:0px;font-size:10px; margin-bottom:10px;', ->
#     text _ 'Powered By '
#     a href:'http://www.realmaster.com', dataIgnore:'push', -> 'www.realmaster.com'
#   unless @noBotBar
#     div style:'height:44px;'

VIEW 'signUpForm', ->
  text ckup 'flashMessage', {id:'sendSuccess', msg:@req.l10n('Send')}
  text """
  <style>
  #signUpForm > div:not(:first-child){padding-top: 10px}
  #signUpForm {
    display:none;
    background-color:#F7F7F7; border-top:1px solid #DDDDDD; border-bottom:1px solid #DDDDDD;
  }
  #signUpForm.show{display:block;}
  #signUpForm {padding: 10px;  margin-top: 15px;}
  #signUpForm > div > * {margin-bottom: 2px;}
  #signUpForm .btn {background-color:#e03131; color:white}
  #signUpForm label span.tp{color:#666; font-weight: normal;}
  #signUpForm label span.tp:after{content:":"}
  #signUpForm label .ast {color:#e03131;   padding-left: 10px;}
  #signUpForm .tl{text-align:center;font-size: 16px;}
  #signUpForm .btn-short{
    width: 50%;
    margin-left: 25%;
    padding: 10px 0;
  }
  #signUpSuccess {
    display:none;
    height: 90px;
    text-align: center;
    background: #D4FAAA;
    border: 1px solid #DDDDDD;
    margin-top: 10px;
    padding-top: 32px;
    font-size: 15px;
  }
  #signUpSuccess i.fa{
    color:#80D820;
    padding-right: 7px;
  }
  #signUpForm input.error{
    border: 1px solid#e03131;
  }
  #signUpForm .btn-signup{
    height: 38px;
    padding: 10px;
  }
  </style>
  """
  div id:"signUpSuccess", ->
    i class:'fa fa-check-circle'
    text _ "Your feedback has been submitted."
  form id:"signUpForm", ngClass:"{'show':user.vip}",->
    div class:'tl',->
      if @title?
        text @title
      else
        text _ "Contact Me"
    div ->
      label ->
        span class:"tp",->
          text _ "Name", 'signUpForm'
        span class:'ast',-> "*"
      input type:"text", placeholder:"", ngModel:"userForm.nm", class:"nm", ngClass:"{'error':nmErr}"
    div ->
      label ->
        span class:"tp",->
          text _ "Email", 'signUpForm'
        span class:'ast',-> '*'
      input type:"email", ngModel:"userForm.eml", class:"eml", ngClass:"{'error':emlErr}"
    div ->
      label class:'mblTitle',->
        span class:"tp",->
          text _ "Mobile", 'signUpForm'
        span class:'ast',-> '*'
      input type:"number", ngModel:"userForm.mbl", class:'mbl'
    if @req.isWeChat()
      div ->
        label ->
          span class:"tp",->
            text _ "WeChat ID", 'signUpForm'
        input type:"text", ngModel:"userForm.wxid", class:'wxid'
    div ->
      label ->
        span class:"tp",->
          text _ "Message", 'signUpForm'
      textarea rows:"3", ngModel:"userForm.m", class:'m'
    div ->
      button class:"btn btn-block btn-signup", type:'button', ngClick:"signUp()", ->
        text _ "Submit", 'signUpForm'


VIEW 'flashMessage', ->
  div class:'flash-message-box', id:"fm-#{@id or (if @req._flashMessageID then ++@req._flashMessageID else @req._flashMessageID = 1)}", ->
    div class:'flash-message-inner', ->
      text @msg

LAYOUT ->
  doctype '5'
  html ->
    head ->
      meta charset: 'utf-8'
      meta name:"viewport", content:"width=device-width,height=device-height,initial-scale=1.0,maximum-scale=1.0,user-scalable=0"
      #meta name:'viewport',content:'initial-scale=1,minimum-scale=1.0,maximum-scale=1,user-scalable=no'
      meta name:"apple-mobile-web-app-capable", content:"yes"
      meta name:"apple-mobile-web-app-status-bar-style", content:"black-translucent"
      meta name:"format-detection", content:"telephone=no"
      meta name:"referrer", content:"no-referrer" if @noref
      meta name:'googlebot',content:'noindex,nofollow' if @noIndex
      # meta name:"format-detection", content="telephone=yes"
      meta name:"Access-Control-Allow-Origin", value:"*" if @cors
      g4tags js:new Date()
      headTitle = @vctx.title or @req.setting?.meta?.name or _('RealMaster')
      title -> headTitle
      meta property:"og:title", content: headTitle
      meta property:"og:image", content: @shareImage or  "/img/logo_s.png"
      meta name:"description", content:@desc if @desc
      css '/css/page.min.css' if @hasPageCss
      css '/css/ratchet.min.css' unless @noRatchet
      css '/css/font-awesome.min.css' unless @noFontAwesome
      css '/css/smb.min.css' unless @nosmbcss
      # text """
      #   <link rel="preload" href="styles.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
      #   <noscript><link rel="stylesheet" href="styles.css"></noscript>
      # """
      block 'customHeader', ->
        css '/css/app.min.css' unless @noAppCss
        js '/js/angular.min.js' unless @noAngular
        js '/js/ratchet.min.js' unless @noRatchetJs
        if @req.isChinaIP()
          js 'https://s.realmaster.cn/logger.lib.min.js'
        else
          js 'https://s.realmaster.com/logger.lib.min.js'
        #js '/js/app.js?v=1.8'
        text ckup 'cordovaJs', {wxcfg:@wxcfg, pubOnly:@pubOnly, noasync:@noasync} unless @noCordova
      if @noasync
        js '/js/rmapp.min.js' unless @noRMApp
      else
        script src:'/js/rmapp.min.js' unless @noRMApp #async:"", defer:""
        # text "<script src='/js/rmapp.min.js' async></script>" unless @noRMApp
      if @mapboxkey
        text """<script> key= {mapboxKey: '#{@mapboxkey}'};</script>"""
    block 'body', ->
      bodyStyle = ''
      if @bodyStyle
        bodyStyle = @bodyStyle
      body style:bodyStyle,->
        g4body()
        text @content()
        # text ckup 'userModal' unless @noUserModal or (@req.getDevType() isnt 'app')
        # text if @req.user then (@req.user.roles?.join() or 'User') else 'UnLoginUser'
        # text """'}); </script>"""

VIEW 'mapboxJs',->
  css 'https://api.tiles.mapbox.com/mapbox-gl-js/v3.5.1/mapbox-gl.css'
  js 'https://api.tiles.mapbox.com/mapbox-gl-js/v3.5.1/mapbox-gl.js'

# '/1.5/index'
APP '1.5'

###
#listing need login, used in mapSearch and search.coffee
VIEW 'prpNeedLogin', ->
  div id:'prpNeedLogin', class:'modal',->
    header class: 'bar bar-nav', ->
      a class: 'icon icon-close pull-right ', ngClick:"toggleModal('needLogin')"
      h1 class: 'title',-> _ 'RealMaster'
    div class: 'content content-padded', style:'',->
      div style:'text-align:center; padding:15px; padding-top:30px;',->
        text "{{message}}"
      a class: 'btn btn-positive btn-block btn-mar-top btn-long',dataIgnore:'push',href:"/1.5/user/login", ->
        text _ 'Login For Details'
###
VIEW "busy-icon", ->
  divParam = ""
  if @angular
    divParam = ngShow:"loading"
  div class:"overlay loader-wrapper", id:'busy-icon', divParam,  ->
    div class:'loader'
    # text """<svg viewBox="0 0 64 64"><g stroke-width="4" stroke-linecap="round"><line y1="12" y2="20" transform="translate(32,32) rotate(180)"><animate attributeName="stroke-opacity" dur="750ms" values="1;.85;.7;.65;.55;.45;.35;.25;.15;.1;0;1" repeatCount="indefinite"></animate></line><line y1="12" y2="20" transform="translate(32,32) rotate(210)"><animate attributeName="stroke-opacity" dur="750ms" values="0;1;.85;.7;.65;.55;.45;.35;.25;.15;.1;0" repeatCount="indefinite"></animate></line><line y1="12" y2="20" transform="translate(32,32) rotate(240)"><animate attributeName="stroke-opacity" dur="750ms" values=".1;0;1;.85;.7;.65;.55;.45;.35;.25;.15;.1" repeatCount="indefinite"></animate></line><line y1="12" y2="20" transform="translate(32,32) rotate(270)"><animate attributeName="stroke-opacity" dur="750ms" values=".15;.1;0;1;.85;.7;.65;.55;.45;.35;.25;.15" repeatCount="indefinite"></animate></line><line y1="12" y2="20" transform="translate(32,32) rotate(300)"><animate attributeName="stroke-opacity" dur="750ms" values=".25;.15;.1;0;1;.85;.7;.65;.55;.45;.35;.25" repeatCount="indefinite"></animate></line><line y1="12" y2="20" transform="translate(32,32) rotate(330)"><animate attributeName="stroke-opacity" dur="750ms" values=".35;.25;.15;.1;0;1;.85;.7;.65;.55;.45;.35" repeatCount="indefinite"></animate></line><line y1="12" y2="20" transform="translate(32,32) rotate(0)"><animate attributeName="stroke-opacity" dur="750ms" values=".45;.35;.25;.15;.1;0;1;.85;.7;.65;.55;.45" repeatCount="indefinite"></animate></line><line y1="12" y2="20" transform="translate(32,32) rotate(30)"><animate attributeName="stroke-opacity" dur="750ms" values=".55;.45;.35;.25;.15;.1;0;1;.85;.7;.65;.55" repeatCount="indefinite"></animate></line><line y1="12" y2="20" transform="translate(32,32) rotate(60)"><animate attributeName="stroke-opacity" dur="750ms" values=".65;.55;.45;.35;.25;.15;.1;0;1;.85;.7;.65" repeatCount="indefinite"></animate></line><line y1="12" y2="20" transform="translate(32,32) rotate(90)"><animate attributeName="stroke-opacity" dur="750ms" values=".7;.65;.55;.45;.35;.25;.15;.1;0;1;.85;.7" repeatCount="indefinite"></animate></line><line y1="12" y2="20" transform="translate(32,32) rotate(120)"><animate attributeName="stroke-opacity" dur="750ms" values=".85;.7;.65;.55;.45;.35;.25;.15;.1;0;1;.85" repeatCount="indefinite"></animate></line><line y1="12" y2="20" transform="translate(32,32) rotate(150)"><animate attributeName="stroke-opacity" dur="750ms" values="1;.85;.7;.65;.55;.45;.35;.25;.15;.1;0;1" repeatCount="indefinite"></animate></line></g></svg>"""

VIEW 'userQrCode', ->
  if @owner
    div id:'user-qrcode', class:'pic', ngShow:'qrcd', style:"margin:0 0; position:absolute; bottom:0px; z-index:20;
     width: 100%;padding: 5px; text-align:center; display: inline-block; vertical-align: top;
     background-color: #fff;", ->
      # a href:'/1.5/wecard/qrcode',->
      span style:'font-size:16px; color:#000;display:inline-block; margin-bottom:5px;', ->
        text _ 'Follow Me On Wechat'
      br()
      img style:'height:200px;width:200px;', src:(@owner?.qrcd or '/img/logo.png'), alt:@owner?.nm or @owner?.id
      br()
      span style:'font-size:14px; color:#666;display:inline-block', ->
        text _ 'Long Press to Scan'
      div style: 'border-bottom: 2px solid #F0EEEE; margin:10px 15px 10px 15px;'
      button class:'btn btn-block btn-long', ngClick:'toggleQrcd();', style:'border:1px none;', ->
        text _ 'Close'
###
VIEW 'userModal', ->
  newVer = @req.needUpdateApp()
  div id: 'userModal', class: 'modal', style:"z-index: 13;",->
    header class: 'bar bar-nav', ->
      #a class: 'icon icon-left-nav pull-left', href: '/1.5/index', dataIgnore: 'push'
      a class: 'icon icon-close pull-right avator', onclick:'event.preventDefault();', href: '#userModal'
      a class:'pull-right icon icon-home  avator', style:'font-size: 1.35em;line-height: 30px;', href:'/1.5/index', dataIgnore:'push'
      h1 class: 'title', -> _ 'RealMaster'
    div class: 'content', ->
      div class: 'userPanel', ->
        if user = @req.session.get('user')
          img class: 'media-object', src: (user.avt or '/img/logo.png'), style:'height:80px;width:80px;border-radius:40px;'
          div class: 'userName', ->
            text user.nm
          div class: 'media-body', ->
            a id: 'login-btn', class: 'btn btn-positive', href: '/1.5/user/logout', dataIgnore: 'push', dataAjax:'false', -> _ 'Logout'
        else
          img class: 'media-object', src: '/img/logo.png', style:'height:80px;width:80px;border-radius:40px;'
          div class: 'media-body', style:'margin-top:20px;', ->
            a id: 'login-btn', class: 'btn btn-positive', href: '/1.5/user/login', dataIgnore: 'push', dataAjax:'false', -> _ 'Login or Sign Up'
      ul class: 'table-view', ->
        li class: 'table-view-cell ', ->
          text _('Language') + ":"
          div id: 'langSelect', class: 'btn-group', role: 'group', ariaLabel: '...', ->
            lang = @req.locale()
            thisurl = @req.url
            langOption = (l, name) ->
              a onclick:"localStorage.lang='#{l}';delete localStorage.translateCache", class:("btn btn-default#{if lang is l then " active" else ''}"), href:"/1.5/settings/lang?l=#{l}&d=#{thisurl}", -> text name
            langOption 'en', 'English'
            langOption 'zh-cn', '简'
            langOption 'zh', '繁'
        li class: 'table-view-cell ', id:'versionLine', ->
          span class: 'pull-left', -> _('Version') + ':'
          span class: 'pull-right font-size-p8', ->
            text '3.8.2' + (if @req.isChinaMode() then 'c' else '') + (if @req.isDeveloperMode() then "d" else '')
        li class: 'table-view-cell ', style:'padding-right:15px', ->
          span class: 'pull-left', -> _('Core', 'app version') + ':'
          span class: 'pull-right font-size-p8', ->
            a href:'/getapp', ->
              if k = @req.cookies['apsv']
                text k
                if newVer
                  text ' '
                  span style:"  padding: 0 6px;
                  color: white;
                  background-color: #D9522C;;
                  border-radius: 23px;", ->
                    text "Upgrade"
              else
                text _ "Download APP"
        # #hide for now
        # li class: 'table-view-cell  ', style:'padding-right: 10px;',->
        #   span class: 'pull-left',-> _('Membership') + ':'
        #   if user = @req.session.get('user')
        #     span class: 'pull-left',->
        #       if user.roles?.indexOf('realtor') >= 0
        #         text _ "Lite",'Edition'
        #       else
        #         text _ 'Free','Edition'  #@useType
        #     span class: 'pull-right font-size-p8', style:'margin-left: 10px;',->
        #       text '( '
        #       text _ 'Expires'
        #       text ' 2015.12.31'         #@expireDate
        #       text ' )'
        #   else
        #     span class: 'pull-left',->
        #       text 'Free'

        li class: 'table-view-cell ', style:'height:36px;'
        li class: 'table-view-cell ', style:'padding-right:15px;', ->
          span class: 'pull-left', ->
            text _ 'Help'
            # i class:'fa fa-phone'
            text ': '
          span class: 'pull-right font-size-p8', ->
            a href:"javascript:void(0)", onclick:"javascript:RMSrv.showInBrowser('http://www.realmaster.ca/#!help/ob24a');", ->
              #http://#{@req.host}/html/RMAPP-Helpcenter.html
              text _ 'Help Center'
          br()
          span class: 'pull-right font-size-p8', ->
            a href:'tel:+19056142609', class:'nobusy', ->
              text '************'
            # span style:'background-color:#D9522C;border-radius:10px;padding:1px 10px;margin-left:10px;',->
            #   a href:'tel:9056142609' ,style:'color:white',->
            #     # text '<EMAIL>'
            #     text _ 'Dial'
          span class: 'pull-right font-size-p8', style:'margin-right:8px;', ->
            a href:'mailto:<EMAIL>' , class:'nobusy', ->
              text '<EMAIL>'
        li class: 'table-view-cell ', style:'padding-right:15px;', ->
          span class: 'pull-left', -> _('WeChat') + ':'
          span class: 'pull-right font-size-p8', -> 'RealMasterCanada'
        # li class: 'table-view-cell ',id:'', ->
        #   span class: 'pull-left',-> _ 'Support ' + ': '
        #   span class: 'pull-right',->
        #     a href:'mailto:<EMAIL>' ,->
        #       text '<EMAIL>'
      text ckup 'generalFooter', {noMarTop:true, hideLink:true}
###
###
VIEW 'generalFooter', ->
  if @noMarTop?
    param = class: 'generalFooter', style:'margin-top:10px;'
  if @smallScreen?
    param = class: 'generalFooter', style:'position: relative;  margin-top: -93px;'
  else
    param = class: 'generalFooter'

  div param, ->
    if  @hideLink?
      div  style:'margin-top:20px;line-height: 1em;', ->
        span  style:'font-size:10px;color:#ADACAC', ->
          #text 'RealMaster does not guarentee the accuracy, completeness, or timeliness of information available for listings and schools.
          #Double-checking with Real Estate Board and District School Board  is strongly recommended.'
          text 'RealMaster does not guarentee the accuracy of information available for listings and schools.
          Double-checking with Real Estate Board and District School Board is strongly recommended.'
    else if @noLink?
      div style:'height:28px;'
    else
      div class:'narrow-div', ->
        a href: 'javascript:void(0)', onclick:'RMSrv.showInBrowser("http://realmaster.ca/");', ->
          span -> _ 'RealMaster'
        a href: 'javascript:void(0)', onclick:'RMSrv.showInBrowser("http://realmaster.ca/jjr");', ->
          span -> _ 'Member', 'service'
        a href: 'javascript:void(0)', onclick:'RMSrv.showInBrowser("http://realmaster.ca/lxwm");', ->
          span -> _ 'Contact Us'
        a href: 'javascript:void(0)', onclick:'RMSrv.showInBrowser("http://realmaster.ca/gywm");', ->
          span -> _ 'About'

    div class:'narrow-div', style:'padding:0', ->
      # a href: '',class:'gray-text',->
      #   span -> _ 'Licenses'
      span class:'cpRight gray-text', ->
        i class:"fa fa-copyright"
        text ' RealMaster Technology Inc. All Rights Reserved'
###
###
VIEW 'bottomNav', ->
  actPos = @actPos or 0
  divParam = class:'bar bar-tab'
  if actPos is 10
    divParam.class = "bar bar-tab"
    # divParam.style = "position:relative"
  isApp = @req.getDevType() is 'app'
  div id:'bottomNavBar', divParam, ->
    showItem = (pos, url, icon, name, extra) ->
      aparam = class:'tab-item', dataTransition:'slide-in', href:url
      iparam = ''
      if actPos is pos
        aparam.href = '#'
        aparam.class += ' active'
        icon += '-focus'
      else if pos is 25
        iparam = style:'top:4px; height: 27px;'
      if extra?
        for k, v of extra
          aparam[k] = v
      a aparam, ->
        span class:"icon fa #{icon} fa-fw", iparam
        span class:'tab-label', -> _ name, 'menu'
    showItem 10, 'javascript:void(0);', 'fa-index', 'Home', {onclick:"javascript:document.getElementById('busy-icon').style.display = 'none';window.location='/1.5/index';"}
    #showItem 20,'javascript:void(0);','map-marker','Map',{onclick:"javascript:document.getElementById('busy-icon').style.display = 'block';window.location='/1.5/map';"}
    showItem 20, 'javascript:void(0);', 'fa-map-o2', 'Map', {onclick:"javascript:document.getElementById('busy-icon').style.display = 'block';window.location='/1.5/mapSearch?d=#{@req.url}';"}
    if isApp # only show news/chat/leads in app
      if (not @req.isChinaIP()) or (@req.isAllowed 'newsAdmin')
        showItem 25, '/1.5/forum', 'fa-news', 'Forum & News'
    showItem 40, '/1.5/settings', 'fa-me', 'Me', {style:'padding-top: 2px;', ngClass:"{'new':hasNewMsg}"}
    # if (user = @req.session.get('user')) and (user.roles?.indexOf('realtor') >= 0)
    #   showItem 60, '/1.5/crm/leads', 'realtor', 'Realtor'
  text ckup 'busy-icon'
###
###
VIEW 'headerbarUserModalBtn', ->
  newVer = @req.needUpdateApp()
  if avator = (user = @req.session.get('user'))?.avt
    a class:'pull-right icon avator nobusy', onclick:'event.preventDefault();', href:'#userModal', ->
      img src:avator, alt:user.nm
      if newVer
        div class:'red-dot', style:'top:-36px'
  else
    a class:'pull-right icon icon-person icon-inactive avator nobusy', onclick:'event.preventDefault();', href:'#userModal', ->
      if newVer
        div class:'red-dot'
###
###
VIEW 'needFollowRealtor', ->
  div class:"card action action-follow", ngHide:"focus", ->
    a href:"/1.5/user/follow", ->
      div class:'action-icon', ->
        i class:"fa fa-user"
      div class:"reminderText", ->
        span -> _ "Find a Realtor"
        p -> _ "Follow a realtor, get full property information."
      div class:'chevron-right', ->
        span class:'pull-right fa fa-chevron-right'
  div class:"card action action-verify", ngHide:"focus", ->
    a href:"/1.5/user/verify?d=index", ->
      div class:'action-icon', ->
        img src:'/img/ICON_Realtor.png'
      div class:"reminderText", ->
        span -> _ "You are a Realtor"
        p ->
          text _ "Verify as a Realtor. Get full property data and more dedicated features."
      div class:'chevron-right', ->
        span class:'pull-right fa fa-chevron-right'

VIEW 'realtorCard', ->
  div class:"card agent-card", ngHide:"focus", ->
    div ->
      img src:@rltr.avt, id:"agentPic"
    div ->
      div class:"agent-name", ->
        text "#{@rltr.fn or ''} #{@rltr.ln or ''}"
        img class:'icon-realtor', src:'/img/realtor.png'
      div class:"agent-cpny", ->
        p ->
          if @rltr.cpny_pstn
            text @rltr.cpny_pstn
            br()
          if @rltr.cpny
            text @rltr.cpny
      div class:"agent-contact", ->
        a class:'nobusy', href:("tel:" + @rltr.mbl),  -> "Call"
        a class:'nobusy', href:('mailto:' + @rltr.eml),  -> "Email"
        a href:"/1.5/iframe?u=/1.5/wesite/#{@rltr.id or @rltr._id}", -> "WeCard"
###

APP 'index', true

# find daily page view and mylistings count
# getUserCounts = (req, resp, cb) ->
#   UserModel.appAuth {req,resp}, (user) ->
#     findErr = (err) ->
#       ret.err = err.toString()
#       cb ret
#
#     ret = {wv:0, lc:0}
#     unless req.hasRole 'realtor'
#       return cb ret
#     # use aggregate, find 1. wepage daily view; 2, find user listing count
#     wecardOpts = []
#     wecardOpts.push {$match:{uid:user._id, tp:{$exists:true, $ne:"user"}}}
#     wecardOpts.push {$group:{_id: null, "count": {$sum: "$meta.vcd"}}}
#
#     listingOps = {uid:user._id}
#
#     Wecard.aggregate wecardOpts, {cursor:{batchSize:0}}, (err, result) ->
#       if err then findErr err
#       ret.wv = result[0]?.count or 0
#       UserListings.coucountDocumentsnt listingOps, (err, count) ->
#         if err then findErr err
#         ret.lc = count
#         return cb ret
#         # oneWeek = new Date( new Date().valueOf() - 3600000 * 24 * 7 * 1)
#         # q = {
#         #   "market.st": "Promoted"
#         #   $or:[{status:'Active'}, {status:'Sold', mt:{$gte: oneWeek  }}, {status:'Leased', mt:{$gte: oneWeek  }}] }
#         # UserListings.countDocuments q, (err, count) ->
#         #   if err then findErr err
#         #   ret.tml = count

# find 3 listings that is in market
# TODO: add translate
marketRcmdListCache = {}


# return list of all top rmlistings
# getTopUpRMListings = (req, cb)->
#   return cb null,[]
#   q = {
#     status:'Active',
#     topTs:{$gte:new Date()}
#   }
#   getOneTypeRecommend q,{limit:10},(err,lret)->
#     cb err,lret

#TODO: @rain move to properties model
getMarketRecommend = ({req, resp, mode}, cb) ->
  # NOTE: mls home page no longer show market recommend
  if mode is 'mls'
    return cb {ok:1,l:[]}
  rcmdTypeMap = {
    new:req.l10n('New Listing'),
    rcmd:req.l10n('Recommend'),
    ad:req.l10n('TOP','advertise'),
    pc:req.l10n('New Price')
  }
  UserModel.appAuth {req,resp}, (user) ->
    idx = parseInt(req.param('idx')) or 0
    cities = user?.cities or req.session.get('cities') or []
    nCities = cities.slice()
    if curCity = user?.city or curCity = req.session.get('city') or curCity = defaultCityToronto
      nCities.unshift(curCity)
    idx = idx%(nCities.length)
    userCity = getUserCity(req, user, nCities, idx)
    cityName = userCity?.city #cityListObject[cityKey].n
    provName = userCity?.prov
    cached = marketRcmdListCache['cached'+provName]
    if cached and cached.ts and ((new Date() - new Date(cached.ts)) < 3600*1000) and cached.l?.length
      ret =
        ok: 1
        l: propTranslate.translate_prop_list req, cached.l, rcmdTypeMap
      return cb ret

    error = (err)->
      ret.l = []
      ret.err = err.toString()
      ret.ok = 0
      return cb ret
    # getTopUpRMListings req,(err,lret)->
    #   return error(err) if err
    #   if lret?.length
    #     ret.l = ret.l.concat lret
    Properties.getMarketRecommend {provName,isAssignAdmin:false}, (err,ret)->
      return error(err) if err
      marketRcmdListCache['cached'+provName] = {ts:new Date(), l:ret.l}
      ret.l = propTranslate.translate_prop_list req, ret.l, rcmdTypeMap
      return cb ret

#TODO: node > 0.11.12 supports Promise
# doThisAsync()
#   .then(doThatAsync)
#   .then(null, console.error



POST 'recommendForWp', (req, resp) ->
  return resp.send {ok:0} unless p = req.body
  # console.log "body from wordpress"
  # console.log p
  # console.log req.headers
  req.setLocale lang if lang = p.lang
  # console.log lang
  # console.log req.locale()
  host = p.site_url
  wordpressHelper.isValidToken req, host, (token)->
    # return resp.send {ok:0} unless token
    result = {ok:1}
    getRecommendListings req, resp, (obj) ->
      result.recos = obj.l
      for prop in result.recos
        prop.picUrls = listingPicUrls(prop, {isCip:req.isChinaIP(),isPad:req.isPad()})
      getMarketRecommend {req, resp}, (mreco) ->
        for prop in mreco.l
          scope = {ml_num:prop.sid}
          prop.picUrls = libproperties.convert_rm_imgs(scope,prop.pic,'reset')
          unless prop.ptype2
            prop.ptype2 = (prop.ptp or '')+(prop.pstyl or '')
          unless prop.sid
            prop.sid = prop.id

        result.mreco = mreco.l
        resp.send result

getRecommend = (req,resp,{mode},cb)->
  #ts = Date.now()
  # TODO: from backend
  UserModel.appAuth {req,resp}, (user) ->
    ret = {ok:1}
    hasWechat = if user?.hasWechat? then user.hasWechat else true
    # getCurCityStat req, resp, (err,stat)->
    #   if err
    #     console.error err
    #     ret.stat = {}
    #   else
    #     ret.stat = stat
    getRecommendListings req, resp, (obj) ->
      #ts = helpers.showTs ts, "Recommend"
      ret.recos = obj
      # getUserCounts req, resp, (data) ->
      #   ret.cts = data
      getMarketRecommend {req, resp, mode}, (mreco) ->
        #ts = helpers.showTs ts, "MarketRecommend"
        ret.mreco = mreco
        lang = req.locale()
        # if hasWechat
        ret.banner = filterBanners(req, user, getBanners(lang,'banner',true), true,mode)
        # else
        #   ret.banner = []
        projectBanner = filterBanners(req, user, getBanners(lang,'project',true), true)
        ret.indexAd = deepCopyObject(helpers.shuffle(projectBanner),2,true) if projectBanner
        # ret.banner = []
        # ret.indexAd  = []
        if ret.indexAd?.length
          for p in ret.indexAd
            if not hasWechat
              delete p.avt
              p.tgt += '&hasWechat=0&rmsrc=homead'
            if lang not in ['zh-cn', 'zh']
              for k in ['src', 'desc', 'name']
                p[k] = p[k + '_en'] or ''
                delete p[k + '_en']
        # ret.evtBanner = getBanners lang,'event'
        # ret.indexSpWeb = getBanners(lang,'banner_web') or []
        cb null,ret

# was for wordpress
# POST 'recommend.json', (req, resp) ->
#   getRecommend req,resp, (err,ret)->
#     resp.send ret

addToSubscribe = (req, user, city, cb)->
  return cb 'no city name' unless city?.o
  return cb 'no user' unless user._id
  return cb(req.l10n('Maximum 10 Cities')) if user?.cities?.length > 9
  if user.cities?.length
    for c in user.cities
      if c.city is city.o
        return cb(req.l10n('Already subscribed'))
  city.prov ?= city.p_ab
  unless city.prov
    city.prov = getProvFromCity(city.o)
  #add to user.cities
  UserModel.addSubscribeCity {req,city},(err,nUser)->
    cb err, nUser

unSubscribe = (req, user, city, cb)->
  return cb 'Need login' unless user
  # return cb 'No data' unless idx or user?.cities
  # return cb 'Not valid data' if idx > user.cities?.length
  # unset = {}
  # idx = parseInt(idx)-1
  # idx = parseInt(idx)
  # return cb 'Cannot remove user city' if idx is -1
  # unset['cities.'+idx] = 1
  prov = city.p_ab
  unless prov
    prov = getProvFromCity(city.o)
  #   return cb err if err
  UserModel.unSubscribeCity {req,city,prov},(err,nUser)->
    if err
      console.log err
      return cb 'error when unSubscribe city'
    cb err, nUser
    # UserModel.updateOneById user._id, {update:{$pull : {'cities.city' : city.o,'cities.prov':prov}}, param:{returnDocument:'after'}}, (err, ret)->
    #   nuser = ret.value
    #   if user is req.session.get('user') # update current user
    #     req.session.set 'user',nuser,->
    #       cb null,nuser.cities
    #   else
    #     cb err, nuser.cities

#1.5/index/subscribe
POST 'subscribe', (req, resp) ->
  error = (err)->
    resp.send {ok:0, e:err}
  return error('no city') unless city = req.body.city
  UserModel.appAuth {req,resp}, (user) ->
    return error('Need login') unless user
    mode = req.body.mode
    if mode is 'unsubscribe'
      unSubscribe req, user, city, (err, nUser)->
        return error(err.toString()) if err
        resp.send {ok:1, msg:req.l10n('Unsaved','favorite'), cnt:nUser.cities?.length}
    else
      addToSubscribe req, user, city, (err, nUser)->
        return error(err.toString()) if err
        resp.send {ok:1, msg:req.l10n('Saved','favorite'), cnt:nUser.cities?.length}

l10nCityList = null

dataMethods = DEF 'dataMethods'
dataMethods.userFollowedRltr = (req, user, cb)->
  uid = user?.flwng?[0].uid
  return cb null, {} unless uid
  UserModel.findPublicInfo {lang:req.locale(),id:uid}, (err, agent) ->
    if err
      console.error err
      return cb null, {}
    # if agent?.vip
      # ctx.rltr = agent
    agent.uid = agent._id
    return cb null, agent
    # cb null,{}

dataMethods.hasFollowedRealtor = (req, user)->
  if uid = user?.flwng?[0].uid
    return true
  return false

# CORE_VER_IOS_NUM = DEF 'CORE_VER_IOS_NUM'
# CORE_VER_ANDROID_NUM = DEF 'CORE_VER_ANDROID_NUM'

# dataMethods.popup = (req, user, cb)->
#   list = getBanners req.locale(),'popup',true
#   list ?= []
#   ret = list[0]
#   ret ?= {}
#   unless ret?._id
#     return cb null,{}
#   #TODO: session expire time
#   # req.session.set('hasViewedPopup'+ret._id,false)
#   # console.log '++++'+req.session.get('hasViewedPopup'+ret._id)
#   if req.session.get('hasViewedPopup'+ret._id) #and not ret.persistent
#     return cb null,{}
#   # verGTE returns true if not need update
#   # unless req.verGTE(getNewestCoreVer(req))
#   #   if ret.newVer
#   #     return cb null, ret
#   if (not user?._id) and (not ret.newVer)
#     return cb null, ret
#   UserModel.findProfileById user?._id,{}, (err,userp)->
#     return cb err if err
#     if userp?.popup?[ret._id]
#       return cb null, {}
#     return cb null, ret unless ret.newVer
#     cb null, {}


bannerClicked = DEF 'bannerClicked'

POST 'popup', (req, resp) ->
  error = (err)->
    resp.send {ok:0, e:err}
  cmd = req.body.cmd
  id = req.body.id
  return error('no id') unless id
  viewed = ()->
    req.session.set('hasViewedPopup'+id,true)
    # console.log '+++set session'
  #close will update session too
  if req.param 'close'
    viewed()
    return resp.send {ok:1}
  UserModel.appAuth {req,resp}, (user) ->
    unless user
      viewed()
      return resp.send {ok:1}
    UserModel.updatePopup user._id,{cmd,id},(err,ret)->
      if err
        console.error err
        return error(err)
      viewed()
      bannerClicked(id)
      resp.send {ok:1}

hasFollowedVipRealtor = (req, user, cb)->
  if uid = user?.flwng?[0].uid
    UserModel.findById uid,{projection:{roles:1}}, (err, followU)->
      return cb err if err
      return cb null, req.isAllowed 'vipRealtor',followU
  else
    return cb null, false

dataMethods.hasFollowedVipRealtor = hasFollowedVipRealtor
# dataMethods.userCityKey = (req, user, cb)->
#   cb null, (user?.ck or req.session.get('ck') or '01._DT')


dataMethods.userCity = (req, user, cb)->
  city = getUserCity(req, user, null)
  ret = {
    o:city?.city,
    n:req.l10n(city?.city),
    lat:city?.lat,
    lng:city?.lng,
    p: ProvAndCity.getProvFullName city?.prov
    p_ab: ProvAndCity.getProvAbbrName(city?.prov)
  }
  ret.cnt = if user?.cities then user.cities.length else 0
  cb null, ret

dataMethods.langCover = (req, user, cb)->
  # unless verGTE req, 2.5003
  #   return cb null, true
  #user has no locale, show cover
  # console.log '++++',req.cookies.locale
  unless req.cookies?.locale
    return cb null, true
  # unless user?.locale or req.session.get('sL')
  #   return cb null, true
  cb null, false

getNewestCoreVer = DEF 'getNewestCoreVer'
# normalizeVer = DEF 'normalizeVer'

dataMethods.hasNewVer = hasNewVer = (req, user, cb)->
  # return cb null, true
  # targetVer = normalizeVer(APP_VER) or 5.6003
  # coreVer = normalizeVer(getNewestCoreVer(req))
  coreVer = getNewestCoreVer(req)
  # console.log '------',coreVer,'++++++',req.cookies,req.verGTE coreVer
  # return cb null,false
  unless req.verGTE coreVer
    return cb null, true
  cb null, false

dataMethods.hasNewMsg = (req, user, cb)->
  getHasNewMsgs req, (has)->
    cb null, has
  return

dataMethods.newFormInput = (req, user, cb)->
  return getNewFormInput req, (cnt)->
    cb null, cnt

# dataMethods.appVerNum = (req, user)->
#   return req.getAppVerNumber()

getHomepageUrl = DEF 'getHomepageUrl'
getConfig = DEF 'getConfig'
getGoogleAPILink = mapServer.getGoogleAPILink

POST 'userCities', (req, resp) ->
  UserModel.appAuth {req,resp}, (user) ->
    cities = user?.cities or req.session.get('cities') or []
    ret = []
    for city in cities
      ret.push  {
        o:city.city,
        n:req.l10n(city.city),
        lat:city?.lat,
        lng:city?.lng,
        p: ProvAndCity.getProvFullName(city?.prov),
        p_ab: ProvAndCity.getProvAbbrName(city?.prov)
      }
    return resp.send {ok:1,cities:ret}

# Looks like not optimal;
# TODO:@rain review code
needReplyForumsKeys = {}
needReplyForums = null
fetchNeedReplayForum = (user,force,cb)->
  if (not force) and needReplyForums
    return cb()
  city = user.city.city
  ForumModel.findPropertyRelated {city},(err,forums)->
    for f in forums
      needReplyForumsKeys[f._id] = true
    needReplyForums = forums
    cb()

_forumNeedRealtorReply = (user)->
  return null if user.local is 'en' or needReplyForums?.length is 0
  uid = user._id.toString()
  availableForums = []
  for f in needReplyForums
    foundUser = false
    for u in (f.cmnts or [])
      #objectid comparision
      if u?.uid?.toString() is uid
        foundUser = true
    if foundUser is false
      availableForums.push f
  if availableForums.length > 0
    return availableForums[helpers.randomTo availableForums.length - 1]
  return null

forumNeedRealtorReply = (user,cb)->
  fetchNeedReplayForum user,true, () ->
    cb null,_forumNeedRealtorReply(user)

getToDos = (user,opt,cb)->
  req = opt.req
  todos = {}
  unless user
    return cb todos
  # personalQcnt = 0
  # if user.eml
  #   personalQcnt+=1
  # if user.mbl
  #   personalQcnt+=1
  # if user.itr
  #   personalQcnt+=1
  # if user.nm
  #   personalQcnt+=1
  # if (user.wx or user.wxgrp) and (user.qrcd or user.grpqrcd)
  #   personalQcnt+=1
  todos.personalQcnt = user.stars or 0
  forumNeedRealtorReply user,(err,forum)->
    todos.forum = forum
    cb todos

getShowingList = ({user,showShowing},cb)->
  if not showShowing
    return cb null
  if not user
    return cb null
  afterDate = helpers.date2num new Date()
  afterTime = helpers.getTheCurrentTime new Date()
  try
    list = await ShowingModel.getList user._id,{afterDate,afterTime,projection:{dtNum:1,endT:1,stT:1}}
  catch err
    debug.error err
  return cb list?.length or 0

# NOTE: user could be null
addRealtorContext = (user,ctx,opt,cb)->
  req = opt.req
  ret = opt.ret
  getShowingList {user,showShowing:true},(count)->
    ctx.upcomingShowing = count
    getToDos user,{req},(todos)->
      ctx.todos = todos
      getHasNewMsgs req, (has)->
        ctx.hasNewMsg = has
        getNewFormInput req, (newFormCnt)->
          ctx.newFormCnt = newFormCnt
          ctx.isOntarioRealtor = libUser.isOntarioRealtor(req,user)
          lang = req.locale()
          if ret.indexAd?.length is 0
            projects = deepCopyObject(getBanners(lang,'project',true),2,true)||[]
          else
            projects = ret.indexAd or []
          if lang not in ['zh-cn', 'zh']
            for p in projects
              for k in ['src', 'desc', 'name']
                p[k] = p[k + '_en'] or ''
                delete p[k + '_en']
          Properties.getPromotions {req,ctx,ret,projects,opt:['topExclusives','project','exlisting']},(newCtx)->
            cb newCtx

getMLSUpdateTime = (cb) ->
  TrebKeyModel.getMLSUpdateTime (err,ts)->
    debug.debug 'get data from trebkey model'
    if err
      debug.error 'getMLSUpdateTime', err
      return cb(null)
    cb ts

getHomeTruAssigmList = (req, resp) ->
  UserModel.appAuth {req,resp}, (user) ->
    userCity = getUserCity(req, user, null)
    {city,prov} = userCity
    # 先按order降序排列查找一次；之后按照用户所在城市和省份在查找一次
    # 如果房源没有sortOrder则与用户所在的省份相同+100分，城市相同+10分，最终根据分数排序显示
    params = {
      ltp: 'assignment',
      page: 0,
      ptype: 'Assignment',
      sort: 'sortOrder-desc',#'auto-ts',
      src: 'rm',
      rmProp: true,
      limit: 10
    }
    key = 'cached'+prov+city
    cached = objectCache.getObjectCacheList TRUSTEDASSIGM,key
    if cached and cached.l?.length
      result = propTranslate.translate_rmprop_list(req, cached.l)
      return result
    propList = await getTruAssigmList {req, resp, user, params}
    params.city = city
    params.prov = prov
    sameCityList = await getTruAssigmList {req, resp, user, params}
    mergedProp = [...propList,...sameCityList]
    mapProp = {}
    for item in mergedProp
      if (not mapProp[item._id])
        if item.sortOrder is 0
          if (item.city is city)
            item.sortOrder += 10
          # mongo找回的数据prov是全拼，prov_abbr是缩写；es返回的数据prov是缩写
          if ((item.prov is prov) or (item.prov_abbr is prov))
            item.sortOrder += 100
        mapProp[item._id] = item
    newPropArr = Object.values(mapProp)
    newPropArr.sort((a, b) -> return b.sortOrder - a.sortOrder)
    newPropArr = newPropArr.slice(0,10)
    objectCache.setObjectCacheList {type:TRUSTEDASSIGM,key,value:{l:newPropArr}}
    result = propTranslate.translate_rmprop_list req, newPropArr
    return result
# 获取真楼花列表数据
getTruAssigmList = ({req, resp, user, params={}}) ->
  params.locale = req.locale()
  params.notApp =  'app' isnt req.getDevType()
  params.isAssignAdmin = false
  baseOpts = libproperties.getBaseForPropList req,user
  params = Object.assign params,baseOpts
  libproperties.filterSearchParams params
  list = await Properties.findListings {params,isRealtor:req.hasRole 'realtor'}
  isCip = req.isChinaIP()
  isPad = req.isPad()
  libproperties.genListingThumbUrlForList(list.result, isCip, isPad, cfg.serverBase?.use3rdPic, cfg.share?.hostNameCn)
  for p in list.result
    p.priceValStrRed = libproperties.currencyFormat p.lp,'CAD$',0 if p.lp
    Properties.deletePropPriviteFields({user,prop:p})
    if p.thumbUrl
      p.image = p.thumbUrl
    else if p.pic?.l?.length > 0
      p.image = p.pic.l[0]
    else
      p.image = '/img/noPic.png'
    if (p.market?.sortOrder or p.sortOrder) and ((not isNaN(p.market?.sortOrder)) or (not isNaN(p.sortOrder)))
      p.sortOrder = p.market?.sortOrder or p.sortOrder
    else
      p.sortOrder = 0
    if p.sqft and ('string' is typeof p.sqft)
      [sqft1,sqft2] = libproperties.getEdges p,'sqft'
      p.sqft = sqft1 if sqft1?
    delete p['uid'] if p.uid
  return list.result

userUpdate = {}
GET (req, resp) ->
  homeurl = '/home/'
  appmode = req.cookies[libUser.APPMODE]
  if appmode is 'rm'
    homeurl += 'marketplace'
  else
    homeurl += 'mls'
  homeurl +="?src=#{req.param('src')}" if req.param('src')
  homeurl +="&from=#{req.param('from')}" if req.param('from')
  return resp.redirect homeurl

# VIEW 'indexServerRender_unused', ->
#   text @html
#   js '/js/localCache.min.js'
#   js '/js/entry/commons.js'
#   js '/js/entry/appIndex.js'

# VIEW 'indexNew', ->
#   css '/css/apps/appIndex.css'
#   div id:'vueBody',->
#     text """
#     <app-index></app-index>
#     """
#   js '/js/entry/commons.js'
#   js '/js/entry/appIndex.js'

# index more page, post before jump
POST 'testserver',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    return resp.send {ok:0,msg:'No access'} unless req.isAllowed 'devGroup',user
    return resp.send {ok:0,msg:'No parameter'} unless num = req.body.num
    testUrl = ''
    num = parseInt(num,10)
    # console.log '@@@@@@',num,isNaN(num)
    if not isNaN(num)
      if num is '0'
        url = "realmaster.com/app/apptest"
      else
        url = "d#{num}.realmaster.com/app/apptest"
    else
      url = "realmaster.com/app/apptest"
      if num is 'c1'
        url = "c1.#{gShareHostNameCn}/app/apptest"
      else
        switch req.host?.toLowerCase()
          when "ch.#{gShareHostNameCn}"
            url = "c1.#{gShareHostNameCn}/app/apptest"
          when "c1.#{gShareHostNameCn}"
            url = "ch.#{gShareHostNameCn}/app"
          else
            console.log "unsupported debug source host:#{req.host.toLowerCase()}"
    # TODO: use config.testServerProtocol
    protocol = 'https'
    testUrl = ("#{protocol}://#{url}?sv=" + req.cookies['apsv'])
    resp.send {ok:1, testUrl, domain:helpersHttp.extractDomainFromUrl(testUrl)}


drawsAddUrlParam = (req,ctx,draws,user,hasWechat)->
  userCity = getUserCity(req, user, null)
  {city,prov,lat,lng} = userCity
  # TODO: use appendCityToUrl
  geo = ('&city='+encodeURIComponent(city)+'&prov='+encodeURIComponent(prov)+'&cityName='+req.l10n(city)).replace(/\'/g,'')
  if city in ['Etobicoke', 'North York' , 'Scarborough' , 'East York' , 'Toronto and East York']
    if city is 'East York'
      city = 'Toronto and East York'
    subGeo = ('&city=Toronto&prov='+encodeURIComponent(prov)+'&subCity='+encodeURIComponent(city))
  loc = '&loc='+lat+','+lng
  latLng = '&lat='+lat+'&lng='+lng

  for draw in draws
    if draw.urlcn and req.isChinaIP()
      draw.url = draw.urlcn
    if (draw.k in ['agent','yellowpage']) and (not hasWechat)
      #and (not req.hasRole('realtor',user))
      draw.url = ''
      draw.signUp = true
    if draw.needLogin
      draw.url = '/1.5/user/login' if not user
      continue
    # cn show listmode, else map mode, no need geo in mapmode
    withGeo = draw.geo
    if req.isChinaIP()
      withGeo = draw.geocn
    if withGeo
      draw.url = draw.url.split('&city')[0]+geo
    if draw.loc
      draw.url = draw.url.split('&loc')[0]+loc
    if draw.subGeo
      draw.url = draw.url.split('&city')[0]+(subGeo or geo)
    if draw.latLng
      draw.url = draw.url.split('&loc')[0]+latLng
  if not hasWechat
    draws = draws.filter (draw)->
      return draw.k not in ['agent','yellowpage']
  ctx.newDrawers = draws

APP 'home'

# TODO: handle this in coffeemate3
# /home/<USER>
# GET 'E503', (req, resp) ->
#   resp.ckup 'E503', {}, '_', {noAngular:1, noAppCss:1, noUserModal:1}


# /home/<USER>
GET 'mls',(req, resp) ->
  appmode='mls'
  l10n = (a,b)->req.l10n a,b
  UserModel.appAuth {req,resp}, (user) -> # continue user login status here
    # resp.noCache()
    unless ((req.getDevType() is 'app') or (req.param('src') is 'appnative'))
      return resp.redirect '/adPage/needAPP'
    isCip = req.isChinaIP()
    # console.log 'xxxxxxxx',getGoogleAPILink({tp:'googleapi',cn:isCip,src:'index'})
    resp.cookie libUser.APPMODE,appmode,{path:'/',maxAge:1000*3600*24*365}
    if user
      UserModel.updateAppMode {req,appmode,user}
    ctx =
      isChinaIP:isCip
      title: req.l10n('RealMaster')
      showSoldPriceBtn:getConfig('showSoldPriceBtn')
      googleapiJs: getGoogleAPILink({tp:'googleapi',cn:isCip,src:'index'})
      os: if req.isIOSDevice() then 'ios' else 'android'

    [jsArray,jsScript] = req.getCordovaJs({pubOnly:@pubOnly,wxcfg:@wxcfg,vctx:@vctx})
    ctx.appjs = jsArray[0]
    homeServices = getHomeServices()
    draws = homeServices.drawsMls
    drawsAddUrlParam req,ctx,draws,user
    getRecommend req,resp,{mode:appmode}, (err,ret)->
      ctx.banners = ret.banner
      ctx.indexAd = ret.indexAd
      ctx.services = homeServices.services
      ctx.page = 'home'
      ctx.appmode = appmode
      ctx.lang = req.locale()
      ctx.isAdmin = req.hasRole '_admin',user
      ctx.isVipUser = req.isAllowed 'vipUser',user
      ctx.isVipPlus = req.isAllowed 'vipPlus',user
      ctx.showRealtorPanel = req.hasRole 'realtor',user
      ctx.mapUrl = '/1.5/mapSearch?d=/home/<USER>'
      ctx.userRoles = dataMethods.userRoles req,user
      if not isCip
        ctx.mapUrl += '&gps=1'
      getShowingList {user,showShowing:ctx.showRealtorPanel},(count)->
        ctx.upcomingShowing = count if count
        ctx.isLogin = user?
        libAppVer.getAppUpgradeSetting req, {os:ctx.os}, (setting) ->
          # NOTE: 首页在没有user和appver时，不显示app升级，此url是app里browser默认显示的
          if (req.param('src') isnt 'appnative')
            ctx.appUpgrade = setting
          getHeadLinesHome req,resp,(ret)->
            ctx.headlines = ret.l if ret.l?.length
            try
              ctx.truAssign = await getHomeTruAssigmList req,resp
            catch err
              debug.error err
              ctx.truAssign = []
            ret = resp.renderFile 'homePageMls', ctx #, '_', {noAngular:1, noAppCss:1, noUserModal:1}
            # BUG: timeout www.realmaster.cn:/home/<USER>
            # this theme is app only but accessed from web
            if not ret
              err = MSG_STRINGS.NOT_FOUND
              return resp.ckup 'generalError', {err_tran:req.l10n(err)}

# /home/<USER>
GET 'marketplace',(req, resp) ->
  appmode = 'rm'
  l10n = (a,b)->req.l10n a,b
  UserModel.appAuth {req,resp}, (user) -> # continue user login status here
    # resp.noCache()
    unless ((req.getDevType() is 'app') or (req.param('src') is 'appnative'))
      return resp.redirect '/adPage/needAPP'
    isCip = req.isChinaIP()
    # console.log 'xxxxxxxx',getGoogleAPILink({tp:'googleapi',cn:isCip,src:'index'})
    ctx =
      isChinaIP:isCip
      title: req.l10n('RealMaster')
      showSoldPriceBtn:getConfig('showSoldPriceBtn')
      googleapiJs: getGoogleAPILink({tp:'googleapi',cn:isCip,src:'index'})
      os: if req.isIOSDevice() then 'ios' else 'android'
    resp.cookie libUser.APPMODE,appmode,{path:'/',maxAge:1000*3600*24*365}
    if user
      UserModel.updateAppMode {req,appmode,user}
    [jsArray,jsScript] = req.getCordovaJs({pubOnly:@pubOnly,wxcfg:@wxcfg,vctx:@vctx})
    ctx.appjs = jsArray[0]
    homeServices = getHomeServices()
    draws = homeServices.drawsMarket
    hasWechat = if user?.hasWechat? then user.hasWechat else true
    drawsAddUrlParam req,ctx,draws,user,hasWechat
    ctx.services = homeServices.services
    getRecommend req,resp,{mode:appmode}, (err,ret)->
      ctx.banners = ret.banner
      ctx.rcmdCity = ret.recos?.city
      recos = (ret.recos?.l?.concat ret.mreco.l) or []
      _ab = (a,b)->req._ab(a,b)
      for reco in recos
        libproperties.setUpPropRMPlusFields reco,l10n,_ab
      randomRecos = libproperties.getrecos {recos,isCip,shareHostNameCn:cfg.share.hostNameCn}
      ctx.toplistings = randomRecos.toplistings
      ctx.exclusives = randomRecos.exclusives
      ctx.topExclusives = randomRecos.topExclusives
      ctx.recos = recos
      ctx.page = 'home'
      ctx.appmode = appmode
      ctx.indexAd = ret.indexAd
      ctx.lang = req.locale()
      ctx.isAdmin = req.hasRole '_admin',user
      ctx.isVipUser = req.isAllowed 'vipUser',user
      ctx.isVipPlus = req.isAllowed 'vipPlus',user
      ctx.userRoles = dataMethods.userRoles req,user
      libAppVer.getAppUpgradeSetting req, {os:ctx.os}, (setting) ->
        if (req.param('src') isnt 'appnative')
          ctx.appUpgrade = setting
        getHeadLinesHome req,resp,(ret)->
          ctx.headlines = ret.l if ret.l?.length
          ctx.showRealtorPanel = req.hasRole 'realtor',user
          if not hasWechat
            if not (req.hasRole 'vip_plus',user)#not vip
              ctx.showRealtorPanel = false
          if ctx.showRealtorPanel
            user.city ?= {} if user
            # ctx.newDrawers2[0].url = '/1.5/realtor?d=/1.5/index'+geo
            return addRealtorContext user,ctx,{req,ret},(newCtx)->
              # puts "4-1 " + (Date.now() - indexTs)
              resp.renderFile 'homePageRm', newCtx #, '_', {noAngular:1, noAppCss:1, noUserModal:1}
            return
          hasFollowedVipRealtor req,user,(err,ret)->
            if ret
              ctx.newDrawers2[0].url = '/1.5/wesite/'+user?.flwng?[0].uid+'?inFrame=1'
            # puts "4-2 " + (Date.now() - indexTs)
            resp.renderFile 'homePageRm', ctx #, '_', {noAngular:1, noAppCss:1, noUserModal:1}

