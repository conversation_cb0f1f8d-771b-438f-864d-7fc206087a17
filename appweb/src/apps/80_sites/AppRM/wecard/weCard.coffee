UserModel = MODEL 'User'
setLang = DEF 'setLang'
helpers = INCLUDE 'lib.helpers'
libUser = INCLUDE 'libapp.user'
libWecard = INCLUDE 'libapp.wecard'
libPropertyImage = INCLUDE 'libapp.propertyImage'
debug = DEBUG()

checkTextContent = DEF 'checkTextContent'
PromptsModel = MODEL 'Prompts'
config = CONFIG(['share','s3config'])
gShareHostNameCn = config.share.hostNameCn
# isAllowed = DEF '_access_allowed'
Wecard = COLLECTION 'chome','wecard'
User = COLLECTION 'chome','user'
Media = COLLECTION 'chome','media'
Template = COLLECTION 'chome','wecardtemplate'
# UserListings = COLLECTION "chome",'user_listing'
s3config = config.s3config
path = require 'path'
fs = require 'fs'
getConfig = DEF 'getConfig'
getWechatConfig = DEF 'getWechatConfig'
libProperties = INCLUDE 'libapp.properties'
get_shared_to_from_share = DEF 'get_shared_to_from_share'
wecardTemplateTypes = ['assignment', 'exlisting', 'blog', 'event', 'xmas1', 'xmas2', 'spring_fest', 'flyer', 'evtad']

NO_PERMISSION = 'No permission to delete system wecard.'
DETELE_SYSTEM_WECARD = 'System wecard may be used by some features. Remove this wecard may render those features unusable or result in error!'
DELETE_WECARD = 'Are you sure to delete this WePage?'

MAX_WECARD_VIP_ALLOWED = 2000
MAX_WECARD_ALLOWED_USER = 80
WeCardModel = MODEL 'Wecard'

# DEF 'wecardTemplateTypes', wecardTemplateTypes

# VIEW "img-select-modal" , ->
#   div id:"imgSelectModal", class:"modal" , style:"z-index:15", ->
#     header class:"bar bar-nav" , ->
#       a class:"icon icon-close pull-right", href:"javascript:;", id:'toggleImgSelect', style:'color:white; cursor:pointer;'
#       h1 class:"title", ->
#         text _ "Insert Image"
#     div class:"bar bar-standard bar-header-secondary", ->
#       div class:"segmented-control", ->
#         a class:"control-item active", href:"#item1upload", -> text _ "Upload"
#         a class:"control-item", href:"#item2input", -> text _ "Input URL"
#         a class:"control-item", href:"#item3select", id:"listUserPics", -> text _ "Uploaded"

#     div class:"content" , ->
#       div class:"content-padded", style:'height: 94%; overflow: hidden;', ->

#         span id:"item1upload", class:"control-content active", ->
#           # div style:"font-size:17px; font-weight:bold;", ->
#           #   text _ "Select From Files"
#           input type:"file", class:"btn", id:"imgInputFiles", style:'width: 100%;', ->
#             span class:"icon fa fa-upload"
#             #TODO: android 4.4.2 upload
#           div style:"margin-top:20px; text-align:center;", ->
#             img id:"previewImg", style:"max-width:100%; max-height:350px;"

#         span id:"item2input", class:"control-content ", ->
#           # div style:"font-size:17px; font-weight:bold;", ->
#           #   text _ "Image URL"
#           input type:"text", placeholder:_("Enter Image URL Here"), id:'imgInputURL', ngModel:"imgInputURL", -> ""

#         span  id:"item3select", class:"control-content", style:'height: 100%; overflow-y: scroll;', ->

#           div id:'del-btn-wrapper', ngClass:"{'active':picS3RmConfirm}", ->
#             button class:'btn fa fa-trash', id:"gal-del-btn", ngHide:"picS3RmConfirm", ngClick:"picS3RmConfirm = true", ->
#             button class:'btn btn-negative', id:"gal-del-yes-btn", ngHide:"!picS3RmConfirm", ngClick:"removePicS3()", ->
#               text "Yes"
#             button class:'btn', id:"gal-del-can-btn", ngHide:"!picS3RmConfirm", ngClick:"picS3RmConfirm = false", ->
#               text "Cancel"

#           div id:'imgSelectPicList', ->#
#             if @angular
#               span class:"thumb-wrapper" , ngRepeat:"(k,v) in userFiles.pl", ->
#                 img src:'/img/p1.png', alt:"{{k}}", ngSrc:"{{userFiles.base + '/' + (v.tA || v.nm) }}", ngClick:"selectImg($event, k)"

#               div id:'moreImgBtn', ngShow:'hasMoreImg', ->
#                 text _ "Showing last 40 images only"
#                 button class:'btn btn-positive btn-block btn-long', ngClick:"showAllImg()", ->
#                   text _ "Show all"

#     div class:'bar bar-standard bar-footer', ->
#       a class:'btn btn-positive btn-block btn-long', id:'insertImage', ngClass:"{'disabled' : disabledInsert}", ngClick:"insert()", ->#onclick:"save(event);", ->
#         text _ "OK"
#---------------------------------------------------------------------------
#          public view    for slider show

VIEW 'propCardPublic', ->
  js '/libs/pageSwitch.js'
  js '/js/wepage.min.js'
  css '/css/animate.min.css'
  css '/css/userInfo.css'
  # js '/js/jgesture.min.js'
  js '/js/hammer.min.js'
  body id: 'housecard-page-show-body', class: 'ng-scope housecard-page-show-body', ->
    div class:'WSBridge', style:'display:none', ->
      span id:'share-title', ->  text ''
      span id:'share-desc', -> text ''
      span id:'share-image', -> text '/img/share_news_thumb.png'
      # span id:'share-image-uri', -> text ''

    # footer ->
    #   div class: 'bar bar-standard bar-footer show-footer', style:'background-color: #E03131; border-top:1px none;', ->
    #     div class: 'pull-left', ->
    #       a class: 'icon icon-footer-left1', style:'color:white', ->
    #         text _ 'Save WeCard'#'收藏名片夹'
    #       a href:'/getapp', class: 'icon icon-footer-left2', style:'color:white;margin-left: 8px;', ->
    #         text _ 'I want it too' #'我要作秀'

    div id: 'housecard-page-contents', class: 'contents housecard-page blog-style', ->
      div class: 'top-right-icon', ->
        a id: 'quarstr-btn', class: 'quarstr icon', href: '#'
        a id: 'mp3-btn', class: 'mp3 icon ', href: '#', style:'cursor:pointer', ->
          audio id: 'musicPlayer',
          loop: '',
          src: '/musics/baical.MP3',
          style: 'display:none;position:absolute;z-index:-11', #autoplay: 'autoplay',
      div class: 'up-btns fade', style:'display:none;', ->
        img id: 'drag-btn-up', class: 'drag-btn-up', src: '/img/up_arrow.png'
      div class: 'down-btns fade', style: 'display:block;', ->
        img id: 'drag-btn-down', class: 'drag-btn-down', src: '/img/up_arrow.png'
      ul id: 'items-pages', class: 'items-ul', style:'display:none;', ->
      # if @withSign
      text ckup 'signUpForm' unless @req.getDevType() is 'app'
      div id:'androidWechatPadding', style:'height:300px;display:none'
      div id:'userDetail', style:'display:none', ->
        # text ckup 'userBriefInfo',{ showDetail:1 }
        text ckup 'userBriefInfoNew', {}
        # text ckup 'userQrCode',{ showDetail:1 }


    text ckup 'wechat-qr-modal', {}

    # div id: 'qrcode-cover', class: 'qrcode-cover', style: 'display:none;', ->
    #   h4 ->
    #     text _ 'Follow Me On Wechat' #'扫描收藏我的微名片'
    #   div id: 'qr_code_img', class: 'i', ->
    #     img src:"/img/logo.png"
    #   p ->
    #     text _ 'Long Press to Scan' #'长按二维码可保存到手机里'
    #     #text _ '' #'可印在纸质名片和宣传单上'
    #   div class: 'line'
    #   a class: 'btn-close', href: '#', ->
    #     text _('Close') #'关闭'

    div id: 'content-cover', class: 'content-cover', style: 'display:none;', ->
      div class: 'arrow', ->
        img src: '/img/arrow.png'
      div class: 'layer', ->
        p class: 'tip', href: '#', -> text _ 'Follow to save' #'关住才能收藏哦'
        p ->
          text _ '1. Click top right corner'
          img src: '/img/share-icon.png', alt: ''
        p ->
          text _ "2. Click 'View Official Account' "
          # img src: '/img/p.png', alt: ''
          text 'to follow'
    if @withDl
      div id:'withDl', style:'display:none'
    text ckup 'getAppBar', {dlApp:1}

VIEW 'wecard-api', ->
  coffeejs {vars:{
    withSign:@withSign,
    sharedBy:@sharedBy,
    lang:@req.param('lang') or @req.locale(),
    viewCountStr:@viewCountStr,
    openMapStr:@openMapStr,
    emurl:@emurl,
    uid:@uid,
    type:@type,
    domain:@domain,
    ml_num:@ml_num,
    action:@action,
    newsId:@newsId,
    shSty:@shSty,
    followMeStr:@followMeStr,
    realmasterString:@realmasterString,
    s3bucket:@s3bucket,
    s3protocal:@req.getProtocol(),
    wecardTemplateTypes:@wecardTemplateTypes,
    host:@host}}, ->
      amGloble = amGloble or {}
      amGloble.getQueryParam = (name) ->
        r = window.location.search.substr(1)
      vars.domain = decodeURIComponent(vars.domain) if vars.domain
      amGloble =
        lang:     vars.lang
        withSign: vars.withSign
        emurl:    vars.emurl
        host:     vars.host
        type:     vars.type
        action:   vars.action
        ml_num:   vars.ml_num
        id:       vars.uid
        domain:   vars.domain
        newsId:   vars.newsId
        shSty:    vars.shSty
        followMeStr: vars.followMeStr
        viewCountStr: vars.viewCountStr
        openMapStr  : vars.openMapStr
        realmasterString: vars.realmasterString
        getPropCardInfo: '/1.5/wecard/prop/' + vars.uid + '/' + vars.ml_num + \
        '.json' + '?type=' + vars.type + '&lang=' + vars.lang + \
        (if vars.withSign then '&wId=1' else '')
        getUserCardInfo: '/1.5/wecard/user/get/' + vars.uid
        templateTypes: vars.wecardTemplateTypes
        getTemplate    : '/1.5/wecard/getTemplate'
        getMusicList   : '/1.5/wecard/list/music'
        getBgImageList : '/1.5/wecard/list/wecardBgs'
        getS3Config    : '/1.5/s3sign'
        getRMConfig    : '/1.5/rmSign'
        savePropCard   : '/1.5/wecard/prop/save'
        savePicServer  : '/file/uploadImg'
        #savePicS3      : 'http://' + vars.s3bucket + '.s3.amazonaws.com/'
        savePicS3      : vars.s3protocal + '://' + vars.s3bucket + '.s3.amazonaws.com/'
        uploadFail     : '/1.5/uploadFail'
        uploadSuccess  : '/1.5/uploadSuccess'
        getUserFiles   : '/1.5/userFiles.json'
        deleteWecard   : '/1.5/wecard/delete'
        deleteFiles    : '/1.5/deleteFiles'
      null
  if @req.getDevType() is 'app'
    # coffeejs -----
    coffeejs {}, ->
      amGloble.getPropDetail =  '/1.5/props/detail'
      amGloble.getNewsTitle =   '/1.5/news/newsTitleAndDesc?id=' + vars.newsId
      amGloble.publishToNews =  '/1.5/news/publish'
      null

LAYOUT ['wecard', '_'], ->
  block 'customHeader', ->
    meta charset:'utf-8'
    # NOTE: youtube blocks iframe embed if no-referrer
    meta name:'referrer', content:'no-referrer' if @noref
    css '/css/cards.css'
    script src: '/libs/jquery-2.2.3.min.js'
    script src: '/libs/jquery-qrcode.js'
    js '/js/rmapp.min.js?v=1.5.3'
    emurl = @req.exMapURL()
    openMapStr = @req.l10n 'Open Map'
    viewCountStr = @req.l10n 'Views'
    followMeStr = @req.l10n 'Follow Me','Click' #点击关注
    realmasterString = @req.l10n 'RealMaster'
    #sharedBy:_('Shared By'),
    text ckup 'wecard-api',{
      realmasterString,
      followMeStr,
      openMapStr,
      viewCountStr,
      emurl,
      wecardTemplateTypes:@wecardTemplateTypes,
      shSty:@shSty,
      withSign:@withSign,
      uid:@uid,
      type:@type,
      ml_num:@ml_num,
      domain:@domain,
      action:@action,
      newsId:@newsId,
      s3bucket: @s3bucket,
      host:@req.fullUrl('')
    }
    unless @req.param('inFrame') and (@req.getDevType() is 'app')#and inapp
      text ckup 'cordovaJs', {wxcfg:@wxcfg, pubOnly:@pubOnly} unless @noCordova

#---------------------------------------------------------------------------------
#            List

VIEW 'wecard-list-page', ->
  css '/css/wecard.min.css'
  div ngApp:'app', ngController:'ctrlWecard', id:'WecardListPage', class:'ng-cloak', ->
    div class:'backdrop', ngShow:'backdrop' , ->

    text ckup 'flashMessage',{id:'tooMany',msg:_('Upgrade to VIP for more quota.')}
    text ckup 'flashMessage',{id:'vipTooMany',msg:_('Reached limit, please remove unused articles.')}
    text ckup 'flashMessage',{id:'tag-exist',msg:_('Tag already exists.')}
    text ckup 'becameVip', {line1:@line1, line2:@line2, url:@url, auto:1}

    div class:'overlay loader-wrapper', id:'busy-icon', ngShow:'loading', style:'display:block',->
      div class:'loader'

    header class: 'bar bar-nav', ->
      backHref = '/1.5/index'
      if @req.getDevType() is 'app'
        span class: 'icon fa fa-back pull-left',
        ngClick:'goBack()',
        dataIgnore: 'push'#, onclick:'window.location = window.history.back()'
      #   text ckup 'headerbarUserModalBtn'
      h1 class: 'title', -> _ 'WePage'

    div class:'bar bar-standard bar-header-secondary', style:'padding:0', id:'statusBar', ->
      div ->
        span ->
          text '{{meta.length}}'
        p ->
          text _ 'Total WePages'
      div ->
        span ->
          text '{{meta.vc | number:0}}'
        p ->
          text _ 'Total Views'
      div ->
        span ->
          text '{{meta.vcd | number:0}}'
        p ->
          text _ "Today's Views"
      div ngClick:"toggleModal('filterModal'); filterResultCount = meta.length ",
      ngClass:"{'active' : hasFilter()}", ->
        span class:'fa fa-filter ', ->
        p ->
          text _ 'Filter'

    div class: 'bar bar-standard bar-footer footer-tab', id:'newWecardBar', ->
      a class:'create ',
      ngClick:'createNewWecard()',
      style:'padding: 3px 5px 0 5px; display: inline-block;  width: 40px;',->
        span class:'icon fa fa-plus',
        style:'color: #2f3e46;'
        span class:'tab-label', ->
          text _ 'create'

    div id:'filterModal', class:'modal ', style:'z-index: 20;', ->
      header class: 'bar bar-nav', ->
        # a class: 'icon icon-close pull-right', href:'#', ngClick:'toggleModal('filterModal');', dataIgnore: 'push'
        h1 class: 'title', ->
          text _ 'WePage Filter'
          span class:'badge',
          style:'margin-left: 8px;',
          ngShow:'filterResultCount || filterResultCount === 0', ->
            '{{filterResultCount}}'

      div class:'bar bar-standard bar-footer' ,  ->
        # a class: 'icon fa fa-plus ', ngClick:'toggleModal('newWecardModal')'
        button class:'btn btn-half btn-fill btn-split', ngClick:'applyFilter()', ->
          text _ 'Filter'
        button class:'btn btn-half btn-fill btn-split', ngClick:'clearFilter()', ->
          text _ 'Clear'
        # button class:'btn btn-third btn-fill btn-split', ngClick:'toggleModal('filterModal')', ->
        #   text _ 'Cancel'

      div class:'content', style:'background: #efefef;', ->
        div class:'user-fields', ->
          for n in [['Type','tp'], ['Feature','feature'], ['Tag','tag' ] ]#(Recommend if you have)
            div class:'field-wrapper', ->
              div class:'field-header' , ->#ngShow:'template['#{n[1]}']',
                text _ n[0]

              div class:'fields', ->
                if n[1] is 'tp'
                  wecardTypes = ['Listing','Topic','Blog','Flyer','Event']
                  if @req.isAllowed 'eventAdmin'
                    wecardTypes.push 'Admin Eventad'
                  for type in wecardTypes#
                    div class:'row',  ngClick:"setFilter($event, 'tp','#{@req.l10n(type)}')", ->
                      div class:'field-name',   ->
                        label ngClass:'', ->
                          text _ type,'wepage'
                      div class:'field-input',  ->
                        i class:'pull-right fa ',
                        style:'',
                        ngClass:"{ 'fa-circle-thin':!(filtData['tp'] == '#{@req.l10n(type)}'), 'fa-dot-circle-o active': filtData['tp'] == '#{@req.l10n(type)}' }"
                        # input class:"pull-right", type:'radio', ngChecked:"filtData['tp'] == '#{@req.l10n(type)}'" #ngClick:"setFilter($event, 'tp','#{@req.l10n(i)}')",

                else if n[1] is 'feature'
                  div class:'row',  ngClick:"setFilter($event, 'rcmd',1)", ->
                    div class:'field-name',   ->
                      label ngClass:'', ->
                        text _ 'Recommend', 'wepage'
                    div class:'field-input',  ->
                      i class:'pull-right fa ',
                      style:'',
                      ngClass:"{'fa-square-o' : !(filtData['rcmd'] == 1), 'fa-check-square-o active': filtData['rcmd'] == 1 }"
                      # input class:"pull-right", type:'checkbox',  ngChecked:"filtData['rcmd'] == 1" #ngClick:"setFilter($event, 'rcmd',1)",

                else
                  div class:'row',
                  ngRepeat:'k in keys(meta.tag)',
                  ngClick:"setFilter($event, 'tag', k)", ->
                    div class:'field-name', ->
                      label ngClass:'', ->
                        text  '{{k}}'
                    div class:'field-input',  ->
                      i class:'pull-right fa',
                      style:'',
                      ngClass:"{'fa-square-o': !(filtData['tag'] && filtData['tag'][k]), 'fa-check-square-o active': filtData['tag'] && filtData['tag'][k] }"
                      # input class:"pull-right", type:'checkbox', ngChecked:"filtData['tag'] && filtData['tag'][k]"

    div id:'tagEditModal', class:'modal ', style:'z-index: 20;', ->
      header class: 'bar bar-nav', ->
        a class: 'icon icon-close pull-right',
        href:'#',
        ngClick:"toggleModal('tagEditModal'); initValues(true);",
        dataIgnore: 'push'
        h1 class: 'title', -> _ 'WePage Tags'

      div class:'bar bar-standard bar-header-secondary', style:'padding:0', ->
        input type:'text', placeholder:_('Input a Tag'), ngModel:'tagInput'
        button class:'pull-right btn btn-default btn-nooutline fa fa-plus',
        ngClick:'addTag();',
        style:'margin-top: -60px;'

      div class:'content', style:'background-color:#efefef', ->
        div class:'tags', style:'margin-top: 20px;', ->
          div class:'row', ngRepeat:'k in keys(meta.tag) ', ngClick:'toggleTag(k)', ->
            div class:'field-name',   ->
              label ngClass:'', ->
                text '{{k}}'
            div class:'field-input',  ->
              i class:'pull-right fa ',
              style:'',
              ngClass:"{'fa-square-o':  !curData.curCard.meta.tag || curData.curCard.meta.tag.indexOf(k) < 0, 'fa-check-square-o active': curData.curCard.meta.tag && curData.curCard.meta.tag.indexOf(k) >= 0 }"
              # input class:"pull-right", type:'checkbox' , ngClick:"toggleTag(k)",ngChecked:"curData.curCard.meta.tag && curData.curCard.meta.tag.indexOf(k) >= 0"




    div id:'newWecardModal', class:'modal modal-half', style:'z-index:20;', ->

      div class:'bar bar-standard bar-footer' , ->#style:'border-top: 0; ',
        # div style:'width:90%; margin-left:5%; border-top: 1px solid #eee; ', ->
        button class:'btn btn-block  btn-long',
        style:'border:1px none;',
        ngClick:"toggleModal('newWecardModal')", ->
          text _ 'Cancel'

      header class:'bar  bar-nav', id:'wecardSearchBar', -> #bar-header-secondary bar-standard
        if @req.getDevType() is 'app'
          a class: 'icon fa fa-qrcode pull-right',
          ngClick:'scanQR()'
          a class: 'icon fa fa-plus pull-right',
          ngClass:"{'disabled' : !validUrl}",
          ngClick:'editURL()'
          input class:'',placeholder:_('Scan QR Code or Paste URL'),
          ngModel:'url',
          style:'color:black; background-color:#eee'

      div class:'content', ->
        div class:'content-padded' , ->
          div class:'row', ->
            div class:'type-wrapper', ->
              a href:'/1.5/wecard/edit/flyer', ->
                img class:'type-img', src:'/img/create_templates.png'
              p ->
                text _ 'Flyer'
            if @req.isAllowed 'eventAdmin'
              div class:'type-wrapper', ->
                a href:'/1.5/wecard/edit/evtad', ->
                  img class:'type-img', src:'/img/create_templates.png'
                p ->
                  text _ 'Event Ad'
            if @req.isAllowed 'wecardRltrMnu' #, @user
              div class:'type-wrapper', ->
                a href:'/1.5/wecard/edit/exlisting', ->
                  img class:'type-img', src:'/img/create_exlisting.png'
                p ->
                  text _ 'ExListing'
              div class:'type-wrapper', ->
                a href:'/1.5/wecard/edit/assignment', ->
                  img class:'type-img', src:'/img/create_assignment.png'
                p ->
                  text _ 'Assignment'

            # div class:'row', ->
            div class:'type-wrapper', ->
              a href:'/1.5/wecard/edit/blog', ->
                img class:'type-img', src:'/img/create_topic.png'
              p ->
                text _ 'Blog'
            div class:'type-wrapper', ->
              a href:'/1.5/wecard/edit/event', ->
                img class:'type-img', src:'/img/create_event.png'
              p ->
                text _ 'Event'
          # div class:'row', ->
            div class:'type-wrapper', ->
              a href:'/1.5/wecard/edit/xmas1', ->
                img class:'type-img', src:'/img/create_xmas1.png'
              p ->
                text _ 'Xmas Biz'

            div class:'type-wrapper', ->
              a href:'/1.5/wecard/edit/xmas2', ->
                img class:'type-img', src:'/img/create_xmas2.png'
              p ->
                text _ 'Xmas Cartoon'

            if @isZh
              div class:'type-wrapper', ->
                a href:'/1.5/wecard/edit/spring_fest', ->
                  img class:'type-img', src:'/img/create_springfestival.png'
                p ->
                  text _ 'Spring Festival'

    div class: 'content ',
    id:'wecardList',
    style:'',
    ngClass:'{gray: cards.length == 0}', -> #update this if list
      div class:'', id:'newCardToolTip',ngShow:'cards.length == 0', ->
        div class:'tip-wrapper', ->
          div class:'heading', ->
            text _ 'WePage can be easily created in following 4 ways.'
          div class:'method', ->
            div class:'icon', ->
              img src:'/img/create_templates.png'
            div class:'text', ->
              div class:'h5', ->
                text _ 'Using a template'
              div ->
                p ->
                  text _ 'Create Exclusive and Assignment flyer, personal Blog, Event notice.'
          div class:'method', ->
            div class:'icon', ->
              img src:'/img/create_copyurl.png'
            div class:'text', ->
              div class:'h5', ->
                text _ 'Copy & Paste an URL'
              div ->
                p ->
                  text _ 'Grab a webpage and modify it into your own Blog, and then share it.'
          div class:'method', ->
            div class:'icon', ->
              img src:'/img/share_news_thumb.png'
            div class:'text', ->
              div class:'h5', ->
                text _ 'Copy a News'
              div ->
                p ->
                  text _ 'Transform a news directly into your own Blog and then share it.'
          div class:'method', style:'padding-bottom:10px', ->
            div class:'icon', ->
              img src:'/img/create_exlisting.png'
            div class:'text', ->
              div class:'h5', ->
                text _ 'Copy a property'
                span ->
                  text _ '(Realtor only)'
              div ->
                p ->
                  text _ 'Transform any property directly into an WePage or Virtual Tour and then share it.'

          div class:'center tip', ->
            text _ 'Click On The "+" To Create A new Wepage'
          div class:'center', ->
            i class:'fa fa-long-arrow-down'

      div class:'listWrapper ng-cloak', ->
        ul class: 'table-view', ->
          div class: 'card wecard-card ',
          ngRepeat:'card in cards track by $index',
          ngController:'ctrlWecardLiitem',
          ngShow:'showCard(card)', ->
            a class: '',
            href: 'JavaScript:;',
            ngClick:'editCard(card)',->
              div class: '', ->
                img class:'',
                ngSrc:'{{getMetaImage(this,card)}}',
                onerror:'hanndleImgUrlError(this)'
                div class:'tp', -> '{{card.tp}}'

              div class: 'detail-wrapper', ->
                div class:'card-title', ->
                  '{{card.meta.title}}'

            div class:'edit-tag fa fa-ellipsis-v pull-right',
            ngClick:"toggleModal('tagEditModal'); curData.curCard = card; curData.curIndex = $index", ->

            div ->
              div class:'meta', ->
                p ->
                  span class:'status', -> "{{card.meta.ts | date:'yyyy-MM-dd' }}"
                  span class:'status', ->
                    text "#{_('Viewed')} #{_('Today')}:" #今日阅读
                    span ngClass:"{'active': card.meta.vcd > 0}",
                    style:'margin-left: 0;', ->
                      text '{{ card.meta.vcd || 0}}'
                    text "/#{_('Total', 'viewed')}:"
                    span style:'margin-left: 0;', ->
                      text '{{ card.meta.vc || 0}}'

                  span class:'status', ->
                    text "#{_('Shared')} #{_('Today')}:" #今日阅读
                    span ngClass:"{'active': card.meta.vcd > 0}",
                    style:'margin-left: 0;', ->
                      text '{{ card.meta.shrd || 0}} '
                    text "/#{_('Total', 'viewed')}:"
                    span style:'margin-left: 0;', ->
                      text '{{ card.meta.shr || 0}}'

              div class:'btns', ->
                if @req.isAllowed 'wepageClone'
                  div class:'tip',
                  ngHide:'del', ->
                    text _ 'Clone', 'wecard' #推荐阅读
                  button class: 'icon btn btn-nooutline icon-pages ',
                  ngHide:'del',
                  ngClick:'createCLone($event, $index)',
                  style:'font-size: 16px; margin-right: 20px;'
                div class:'tip', ngHide:'del', ->
                  text _ 'Recommend', 'wecard' #推荐阅读
                button class: 'btn btn-nooutline fa fa-thumbs-o-up ',
                ngHide:'del',
                ngClass:"{'fa-thumbs-up' : card.rcmd}",
                ngClick:'recommend($event, $index)'
                button class: 'btn btn-nooutline fa fa-trash ',
                ngHide:'del',
                ngClick:'toggleApprove($event)'
                button class: 'btn btn-nooutline pull-right no',
                ngShow:'del',
                ngClick:'toggleApprove($event)', ->
                  text _ 'Cancel'
                button class: 'btn btn-nooutline btn-negative pull-right yes',
                ngShow:'del',
                ngClick:'delete($event, $index); initValues(true)', ->
                  text _ 'Delete'
      div class:'showMore',
      ngShow:'hasMore', ->
        div class:'btn btn-block btn-long btn-positive',
        ngClick:'showMore()', ->
          text _ 'Show More'
  coffeejs {vars:{
    d:@d
    l:@l,
    isVipUser:(@req.isAllowed('vipUser')),
    vipTipStr: @req.l10n('Available only for Premium VIP user! Upgrade and get more advanced features.'),
    vipLaterStr: @req.l10n('Later'),
    vipSeeStr: @req.l10n('See More'),
    lang:@req.locale(),
    strDeleteTip: @req.l10n(@DETELE_SYSTEM_WECARD),
    strCancle: @req.l10n('Cancel'),
    strConfirm: @req.l10n('Confirm'),
    isAdmin: @req.isAllowed('newsAdmin'),
    no_permission: @req.l10n(@NO_PERMISSION),
    }}, ->
      null
  js '/js/wecard_list_page.min.js'

process_wecard_req = ({req, resp, message}, cb) ->
  # return template
  uid = message?.a or message?.id or req.param('uid')
  ml_num = message?.r or message?.uid or req.param('ml_num')
  to = message?.h or message?.to or req.param('to')
  withDl = req.param('wDl') or null
  withSign = req.param('sgn') or null
  setLang req
  message ?= {
    "id" : ml_num,
    "wDl" : withDl,
    "uid" : uid,
    "sgn" : withSign,
    "to" : to
  }

  # 检查访问权限
  unless WeCardModel.checkAccessPermission({
    host: req.host,
    isWeChat: req.isWeChat(),
    isChinaIP: req.isChinaIP()
  })
    # return webErrPage req, resp, 'no id', 'NA'
    return resp.ckup 'generalError', {err_tran: req.l10n(MSG_STRINGS.CONTACT_SERVICES)}
  # 获取微图文数据
  try
    result = await WeCardModel.getWecardDataForShareAsync {
      id: ml_num,
      uid: uid,
      to: libProperties.get_shared_to_from_share(to)
    }
  catch error
    debug.error error,'data:',{uid, ml_num}
    return resp.ckup 'generalError', {err_tran:req.l10n(MSG_STRINGS.DB_ERROR)}
  wxconfig = getWechatConfig req,req.fullUrl()
  # 获取页面和微图文数据
  {view, layout, view_cfg, layout_cfg} = libWecard.getPageAndWecardDataForShare {
    hostname: req.hostname,
    lang: req.locale(),
    message: message,
    user: result?.user,
    wecard: result?.wecard,
    wxconfig
  }

  if view_cfg.cardUrl
    view_cfg.cardUrl = req.fullUrl(view_cfg.cardUrl)
  # 返回页面
  resp.ckup view, view_cfg, layout, layout_cfg

DEF 'process_wecard_req', process_wecard_req

APP '1.5'
APP 'wecard', true
# '/1.5/wecard'
#get wecard page, POST to login
GET (req, resp) ->
  d = req.param 'd'
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user) -> #sort by time?
    isZh = req.locale() in ['zh' , 'zh-cn']
    url = "http://#{gShareHostNameCn}/mp/5714b2bb1984ae8b3d600cc299d41354a216b78e62edda6918155f07\
      c405cc6bcd1c6cdc880858aa82d3b04aa24a936a434b9e942d8cae9c07ac1ee5c4d8228d1ac31b?lang=zh-cn&"
    line1 = "Due to recent WeChat's sharing policy restriction, we made some change on WePage \
    features to protect our users' interest."
    line2 = 'Click to see detail.' # 近期由于腾讯公司对微信分享的管理规范要求，为保护广大房大师用户的利益，我们对微图文功能做了一些调整。点击查看详情。
    resp.ckup 'wecard-list-page', {d:d, isZh:isZh, url:url, line1:req.l10n(line1), \
      line2:req.l10n(line2),DETELE_SYSTEM_WECARD,NO_PERMISSION}, '_', {noref:true} #, viewCount:vc}

calcIsTooMany = (req, user, total)->
  tooMany = 0
  #admin has no limit
  if req.isAllowed 'forumAdmin',user
    tooMany = 0
  else if req.isAllowed 'vipUser', user
    tooMany = 2 if total > MAX_WECARD_VIP_ALLOWED
  else
    tooMany = 1 if total > MAX_WECARD_ALLOWED_USER
  tooMany

POST (req, resp) ->
  UserModel.appAuth {req,resp}, (user) -> #sort by time?
    unless user?._id
      return resp.send {l:[], success:0, total:0, tooMany:false}
    cfg = {
      fields:{_id:1, meta:1, uid:1, tp:1, id:1, rcmd:1, sid:1},
      sort:[['meta.ts', 'descending']],
      limit:20
    }
    if p = req.body?.page
      cfg.skip = 20*(parseInt(p) || 0)
    Wecard.countDocuments {uid:user._id, tp:{$exists:true, $ne:"user"}}, (err, total)->
      Wecard.findToArray {uid:user._id, tp:{$exists:true, $ne:"user"}}, cfg,(err, list) ->
        if err
          debug.error err
          return resp.send {success:0, err:err.toString()}
        l = list or []
        # console.log l
        ### not used
        Wecard.findOne { id:user.id, tp:'user' },(err,data)->
          if err
            vc = 0
          else if data
            vc = data.vc
          else
            vc = 0
        ###
        for i in l
          i.meta.img = libPropertyImage.buildRMListingImage {sid:i.sid,idx:i.meta.img} if i.meta?.img
          if i.tp in ['assignment', 'exlisting', 'listing']
            i.tp = req.l10n "Listing"
          else if i.tp in ['blog']
            i.tp = req.l10n 'Blog'
          else if i.tp in ['event']
            i.tp = req.l10n 'Event'
          else if i.tp is 'evtad'
            i.tp = req.l10n 'Admin Eventad'
          else if i.tp in ['topic']
            i.tp = req.l10n 'Topic'
          else if i.tp in ['xmas1', 'xmas2', 'spring_fest', 'flyer']
            i.tp = req.l10n 'Flyer'
          else
            i.tp = req.l10n 'Unknown'
        resp.send {l:l, success:1, total:total, tooMany:calcIsTooMany(req, user, total)}

# =============================================
#                  API part


# nm:
# _id:path
# sz: size
# mime: jpg/mp3
# ctg: country  #music catagory


getMediaListJson = (req, resp, purpose) -># ,cb)->
  # read db
  Media.findToArray {for:purpose}, (err, list) ->
    if err
      l = []
    else
      l = list
    # console.log l
    resp.send {l:l, n:l.length}



# '/1.5/wecard/list/music'
# returns nusic list in json
GET 'list/music', (req, resp) ->
  resp.setHeader('Access-Control-Allow-Origin', '*')
  #read folder '/music' and auto get list
  getMediaListJson req, resp, 'wbgm'


# '/1.5/wecard/list/bgPic'
# returns pic list in json
GET 'list/wecardBgs', (req, resp) ->
  resp.setHeader('Access-Control-Allow-Origin', '*')
  #read folder '/img/wecardBgs' and auto get list
  getMediaListJson req, resp, 'wbgp'



GET 'getTemplate', (req, resp) ->
  UserModel.appAuth {req,resp}, (user) ->
    unless user?._id
      return resp.send {success:0, err:'Need Login'}
    ret = {}
    ret = helpers.shallowExtendObject ret, user
    delete ret.verify
    delete ret.verify
    delete ret._id
    delete ret.src
    delete ret.login_id
    # delete ret.rid
    delete ret.pn
    delete ret.site
    lang = req.locale()
    type = req.param 'type'
    if lang is 'en'
      type += 'En'
    Template.findOne {tp:type}, (err, r) ->
      if err
        resp.send {err:err, type:type, success:false}
      else if not r
        resp.send {err:'This Template Not found!', type:type, success:false}
      else
        delete r._id
        r.user = ret
        resp.send {content:r, type:type , success:true}


#===============================================
#               Post Routes

# [resource/json] get user  and wecard in json, if wecard not exists return default
POST 'user/get/:wid',(req,resp) ->
  # user wecard id
  wid = req.param('wid')
  # resp.setHeader('Access-Control-Allow-Origin','*')
  # find in user table
  #TODO: use get user public info
  UserModel.findPublicInfo {lang:req.locale(), id:wid},(err,retu)->
  # User.findOne q,(err,r) ->
    if err
      resp.send {err:err, id:wid}
    else if not retu?.eml
      resp.send {err:'User Not found!' ,id:wid}
    else
      # retu = r
      # delete retu.verify
      # # delete retu._id
      # delete retu.src
      # delete retu.login_id
      # # delete retu.rid
      # delete retu.pn
      # delete retu.site
      # # delete retu.roles
      # # delete retu.eml

      # queries wecard collection, _id = wid
      Wecard.findOne {uid:retu._id, tp:'user'}, (err,cardr) ->
        if err
          resp.send {err:err, id:wid}
        else
          if cardr
            # if user is not realtor and has mnu in seq
            isRealtor = req.hasRole 'realtor', retu
            hasMnu = (p = cardr.seq?.indexOf('mnu')) >= 0
            if (not isRealtor) and hasMnu
              if Array.isArray cardr.seq
                cardr.seq.splice p,1
            retc = cardr
          else
            retc = {}
        resp.send {user:retu, card:retc}
      # else
      #   resp.send {err:'This user is not found!' ,id:wid, ok:0}


# /1.5/wecard/delete
# delete according to user id and _id
POST 'delete',(req,resp) ->
  # resp.setHeader('Access-Control-Allow-Origin','*')
  UserModel.appAuth {req,resp},(user) ->
    # set fields
    unless user?._id
      return resp.send {success:false, err:'Need Login'}
    body = req.body
    tp = req.param 'tp' or 'prop' #user wecard or prop
    _id = req.param '_id' or null #_id
    ml_num = req.param 'ml_num' or null
    body.id = user.id or "No ID!"

    # remove from table
    if _id
      Wecard.deleteOne {_id:_id,uid:user._id},(err,r) ->
        return resp.send {err:err, success:false} if err
        Wecard.countDocuments {uid:user._id, tp:{$exists:true, $ne:"user"}}, (err, total)->
          return resp.send {err:err, success:false} if err
          resp.send {success:true, message:'Deleted', card:body, tooMany:calcIsTooMany(req, user, total), total:total}
    # else use id and ml_num
    else
      resp.send {success:false, message:'No ID Specified!', card:body}

# /1.5/wecard/recommend
# set rcmd fld
POST 'recommend',(req,resp) ->
  # resp.setHeader('Access-Control-Allow-Origin','*')
  UserModel.appAuth {req,resp},(user) ->
    unless user?._id
      return resp.send {success:0, err:'Need Login'}
    # set fields
    body    = req.body
    tp      = req.param 'tp' or 'prop' #user wecard or prop
    _id     = req.param '_id' or null #_id
    ml_num  = req.param 'ml_num' or null
    body.id = user.id or "No ID!"
    unset   = req.param('unset') or false
    q = {$set:{rcmd:1}}
    if unset is 1
      q = {$unset:{rcmd:1}}
    # remove from table
    if _id
      Wecard.findOneAndUpdate {_id:_id,uid:user._id}, q ,{upsert:false,returnDocument:'after'},(err,r) ->
        if err
          return resp.send {success:false, err:err.toString()}
        else if not r.value
          return resp.send {success:false, message:'card not found!'}
        else
          # card = r.value
          resp.send {success:true,  _id:_id}
        # else use id and ml_num
    else
      resp.send {success:false, message:'No ID Specified!', card:body}

# /1.5/wecard/clone
# set rcmd fld
POST 'clone',(req,resp) ->
  # resp.setHeader('Access-Control-Allow-Origin','*')
  findErr = (err)->
    resp.send {success:false, err:err}
  UserModel.appAuth {req,resp},(user) ->
    _id     = req.param '_id'
    return findErr('no user') unless user
    return findErr('no id') unless _id
    return findErr('not valid id') unless ('string' is typeof _id) and /^[a-f0-9]{24}$/.test _id
    Wecard.findOne {_id:_id,uid:user._id},(err,ret) ->
      return findErr(err:err.toString()) if err
      return findErr('not found') unless ret
      #make a clone
      delete ret._id
      ret.meta.vc = 0
      ret.meta.vcd = 0
      ret.meta.vcrm = 0
      ret.meta.shr = 0
      ret.meta.shrd = 0
      ret.meta.title = '[copy] ' + ret.meta.title
      Wecard.insertOne ret, (err, ret)->
        return findErr(err:err.toString()) if err
        resp.send {success:1}

# ref-> get property detail json in propDetail.coffee
# POST to '/1.5/prop/detail/:ml_num.json'
#  Todo: add support for access ctrl

#will not update fields that are not interested, safe update
POST 'prop/update', (req, resp) ->
  UserModel.appAuth {req,resp},(user) ->
    unless user?._id
      return resp.send {success:false, err:'Need Login'}
    _id = req.param '_id'
    unless ('string' is typeof _id) and /^[a-f0-9]{24}$/.test _id
      return resp.send {success:false, err:'Not Valid ID'}

    update = {}
    metas = ['tag']
    for i in metas
      if (req.param i)
        update['meta.' + i] = req.param i

    Wecard.findOneAndUpdate { _id:_id, uid:user._id },{$set:update},{upsert:false,returnDocument:'after'},(err,r) ->
      if err
        resp.send {err:err, success:false}
      else if r?.value
        resp.send {success:true, message:'Updated', _id:r.value._id}
      else
        resp.send {success:false, err:'Not updated'}


# user post to save edited prop wecard, modify wecard collection
# save _id id mls_num card centent
# User Save wecard here!
POST 'prop/save',(req,resp) ->
  # resp.setHeader('Access-Control-Allow-Origin','*')
  UserModel.appAuth {req,resp},(user) ->
    # set fields
    return resp.send {err:'No user', success:0} unless user
    body = req.body
    return resp.send {err:'No data', success:0} unless body
    update = {}
    fields = ['uid','id','music','bkgimg','seq','tp','card','meta','type']
    for k in fields
      if (body.hasOwnProperty(k))
        update[k] ?= body[k]

    if update.seq
      for seq in update.seq
        seq.m = seq.m.replace(/&amp;tp=webp/ig,'')
        seq.m = seq.m.replace(/&tp=webp/ig,'')

    update.id = user.id
    update.uid = user._id
    update.tp = body.tp or body.type
    if update.tp is 'evtad' and (not req.isAllowed 'eventAdmin')
      return resp.send {success:false, err:'no permission'}
    #save to wecard table as new record
    _id = body?._id
    # if _id update
    delete update.type
    update.meta.mt = new Date() #time stamp
    update.meta.ts = new Date()
    {title,desc,tp,srcUrl} = update.meta
    checkContentObj =
      l10n:(a,b)->req.l10n a,b
      id: _id
      user:user
      collection:'wecard'
      content:[title,desc]
      bypass:['ad','spam','flood','contraband','meaningless']
    if update.seq and Array.isArray(update.seq) and update.seq.length > 0
      seqContent = ''
      for i in [0..update.seq.length]
        seq = update.seq[i]
        if typeof seq is 'object'
          seqContent += seq.m.replace(/<[^>]*>?/gm, '')
      checkContentObj.content.push seqContent if seqContent
    # srcUrl = 'https://mp.weixin.com/s/T8qEH2zLK1nhq9w__IP1tA'
    isWeChatArticleUrl = (url)->
      return false unless url
      return (url.indexOf('mp.weixin.qq.com/s/') > -1)
    if isWeChatArticleUrl(srcUrl) or (tp in ['exlisting','assignment'])
      # if is wechat article url or exlisting/assignment, skip moderation
      checkContentObj.bypassAll = true

    # 统一的审核结果处理函数
    handleModerationResult = (err, ret) ->
      if err
        debug.error err
        return resp.send {success:false, err}
      return resp.send {success:false, err:ret.msg} if ret?.block
      # 继续原有的处理逻辑
      {seq,sid,meta} = libWecard.getWecardSeqWithPics(update)
      update.seq = seq
      update.sid = sid if sid
      update.meta = meta
      if not _id
        update.meta.vc = 0 #init view count with 0
        update.meta.vld = true #valid for 2 monthes, auto update db later use .ts
        # TODO: before insert check role and length, if > 20 return error feedback
        Wecard.insertOne update,(err,r) ->
          if err
            debug.error err
            return resp.send {err:err, success:false}
          else
            _id = update._id or r?.insertedId or r?.insertedIds?['0']
            unless _id
              errMsg = 'No new wecard _id'
              debug.error errMsg,update
              return resp.send {err:errMsg, success:false}
            return resp.send {success:true, message:'Saved', _id}
      # else createOne
      else
        update.meta.custvc = parseInt(update.meta.custvc) or 0
        delete update.meta.vld #prevent user from updating valid
        # delete update.meta.vc
        delete update.meta.ts
        delete update._id
        #flattern meta object
        for k,v of update.meta
          update['meta.'+k] = v
        delete update.meta
        # TODO: delete meta.vc?
        # console.log update
        Wecard.findOneAndUpdate {_id:_id, uid:user._id}, {$set:update}, {upsert:false, returnDocument:'after'}, (err, r) ->
          if err
            return resp.send {err:err, success:false}
          return resp.send {success:true, message:'Updated', _id:r.value?._id}

    # 尝试使用LLM审核，失败时回退到原有审核方式
    try
      # 获取内容过滤提示词模版
      promptTemplates = await PromptsModel.getCachedPromptTemplates('comment_filter')
    catch error
      debug.error "获取提示词模版异常，回退到原有审核:", error
      # 异常情况下回退到原有审核方式
      checkTextContent checkContentObj, (err, ret) ->
        handleModerationResult(err, ret)

    # 使用LLM审核（如果有模版）
    if promptTemplates.length > 0
      checkTextContent checkContentObj, promptTemplates, (err, ret) ->
        if err
          debug.error "LLM WeCard审核失败，回退到原有审核:", err
          # 回退到原有审核方式
          checkTextContent checkContentObj, (err, ret) ->
            handleModerationResult(err, ret)
        else
          handleModerationResult(err, ret)
    else
      # 没有模版，使用原有审核方式
      checkTextContent checkContentObj, (err, ret) ->
        handleModerationResult(err, ret)

getRcommendCardList = DEF 'getRcommendCardList'

# propCardInfo
# POST get json file of  user:uid edited prop:ml_num property
# db.find( id:uid , ml_num: ml_num )
POST 'prop/:wid/:ml_num.json',(req,resp) ->
  wid = req.param('wid')
  lang = req.param('lang')
  # req.setLocale lang
  setLang req
  # resp.setHeader('Access-Control-Allow-Origin','*')
  ml_num = req.param('ml_num')
  type = req.param('type') or 'undefined'

  q = {_id:ml_num}
  if type is 'listing'
    q = {ml_num:ml_num, tp:type}
    if ('string' is typeof wid) and /^[a-f0-9]{24}$/.test wid
      q.$or = [{uid:new Wecard.ObjectId(wid)},{id:wid}]
    else
      q.id = wid
  #1. find( id:uid , ml_num: ml_num ) user in Cards table
  Wecard.findOne q,(err,retc) ->
    #2. fail goes to error page
    #3. edit and create new one uses this api, if type is listing and not ret, card = {}
    if err
      resp.send {err:err, _id:wid, ml_num:ml_num}
    else
      id = retc?.uid
      unless retc
        if type isnt 'listing'
          return resp.send {err:'Card With This ID Not Found!', success:false, _id:wid, ml_num:ml_num}
        else
          id = wid
      img = retc?.meta?.img
      retc.meta?.img = libPropertyImage.buildRMListingImage {sid:retc.sid,idx:img} if img
      if seq = retc?.seq
        retc.seq = libPropertyImage.buildWecardSeqContentWithImageUrls {seq,sid:retc.sid}
      return UserModel.findPublicInfo {lang:req.locale(), id:id}, (err, retu) ->
        resp.send {err:err, id:wid, success:false} if err
        if not retu
          resp.send {err:'User With This ID Not Found!', success:false, _id:wid, ml_num:ml_num}
        else
          # if user type is realtor
          # if UserModel.accessAllowed 'wepageSgn', retu
          # get Recommend listings here
          # TODO: get recommend in ML
          wid = retu.id if retu?.id # user may changed their id after sharing the wecard
          return resp.send {success:true, _id:wid, ml_num:ml_num, card:{}, user:retu} unless retc
          #note: user share without sign dont show rcmd readings
          if req.param 'wId'
            getRcommendCardList retu._id, (err, cards) ->
              return resp.send {success:false, err:err.toString()} if err
              resp.send {id:wid, _id:ml_num,  ml_num:ml_num, card:retc, user:retu, success:true, rcmds:{t:req.l10n('Recommend Topics','wepagercmd'), l:cards}}
          else
            resp.send {id:wid, _id:ml_num,  ml_num:ml_num, card:retc, user:retu, success:true, rcmds:{t:req.l10n('Recommend Topics','wepagercmd'), l:[]}}


#=============================================
#               Views  Routes
featureAllowed = (req,resp) ->
  unless (req.getDevType() is 'app') #or (req.hostname is 'app.test')
    resp.redirect '/adPage/needAPP'
    return false
  return true

###
# '/1.5/wecard/user/edit'
# shows edit page, get user from session
GET 'user/edit', (req, resp) ->
  return unless featureAllowed req, resp
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user) ->
    #resp.noCache()
    wid = user.id
    resp.ckup 'wecardEdit', {user:user}, 'wecard', {uid:wid, noref:true}
###
# checkVersionNShow = DEF 'checkVersionNShow'

# '/1.5/wecard/edit/:ml_num'
# shows property card edit page, get user from session
# ml_num could be url
mapServer = INCLUDE 'lib.mapServer'
getGoogleAPILink = mapServer.getGoogleAPILink

GET 'edit/:ml_num',(req,resp) ->
  # return if req.redirectHTTPWhenCnDitu()
  if req.redirectHTTP()
    # console.log 'redirected https'
    return
  # return unless featureAllowed req,resp
  # return unless checkVersionNShow req,resp,2.5003
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user) ->
    #resp.noCache()
    withSign = req.isAllowed 'shareSign'
    ml_num = req.param('ml_num')
    action = req.param('action')
    newsId = req.param('newsId')
    shSty  = req.param('shSty')
    d = req.param('d')
    if domain = req.param('url')
      type = 'topic' #blog
      action = 'create'
      domain = encodeURIComponent(domain)
      if (domain is '') or (domain is 'undefined')
        type = 'undefined'
    # Change this if ml_num updates!
    else if prop = ml_num.match(/^(DDF|TRB|BRE)\w+$/)
      type = 'listing'
      action = 'create'
      # pass ml_num to api.js
    else if ml_num in wecardTemplateTypes
      type = ml_num
      action = 'create'
    else
      #edit existed wepage
      type = 'Undefined'
    gmap_url = getGoogleAPILink({tp:'googleapi',cn:req.isChinaIP(),src:'wcrd'})
    maxImageSize = getConfig('maxImageSize')
    user.fnm = libUser.fullNameOrNickname req.locale(),user
    noref = true
    if type is 'blog'
      noref = false
    resp.ckup 'propCardEdit',{d:d, user:user,gmap_url:gmap_url,maxImageSize,\
      DETELE_SYSTEM_WECARD,NO_PERMISSION,DELETE_WECARD}, 'wecard', { \
      withSign:withSign, \
      wecardTemplateTypes:wecardTemplateTypes, shSty:shSty, \
      uid:user.id, type:type, domain:domain, ml_num:ml_num, \
      action:action, newsId:newsId, s3bucket:s3config.params.Bucket, \
      noref:noref} # req:req,  no need to set req


#----------------------------------------------
#              public views

###
# '/1.5/wecard/card/:Uid'
# shows wecard of user with uid Uid
GET 'card/:uid',(req,resp) ->
  setLang req
  # return template
  uid = req.param('uid')
  # return resp.redirect '1.5/wesite/'+uid
  q = {tp:"user"}
  if ('string' is typeof uid) and /^[a-f0-9]{24}$/.test uid
    q.$or = [{uid:new Wecard.ObjectId(uid)},{id:uid}]
  else
    q.id = uid
  # set view count +1
  # TODO: only update when different IPs card save to card obj
  Wecard.findOneAndUpdate q,{$inc:{vc: 1}},{upsert:false,returnDocument:'after'},(err,data) ->
    if err
      resp.send {err:'user not found!', success:false}
    # else if not data.ok #data.result.n
    #   # send card aswell, but not update vc
    #   title = req.l10n("RealMaster Wecard")
    #   getWXConfig req,req.fullUrl(),(err,cfg)->
    #     wxcfg = cfg if cfg
    #     resp.ckup 'wecardPublic',{title:title, fullUrl:req.fullUrl() }, 'wecard',{uid:uid, wxcfg:cfg}
    #   # resp.send {err:'user not found!', success:false}
    else
      # title = req.l10n("RealMaster")
      # title += ' '
      # title += req.l10n("Wecard")
      title = req.l10n("WeCard")
      #if set title here need another db access. ?
      # UserModel.findById uid,{},(err,user)->
      #   title += user.fn + user.ln unless err and not user
      noBottomBar = false
      getWXConfig req,req.fullUrl(),(err,cfg) ->
        if /i.realmaster/.test req.hostname
          cfg?.debug = true
        if req.param('inFrame') and (req.getDevType() is 'app')#and inapp
          noBottomBar = true
        resp.ckup 'wecardPublic',{title:title, uid:uid, fullUrl:req.fullUrl(), noBottomBar:noBottomBar}, 'wecard', {uid:uid, wxcfg:cfg,pubOnly:true,noref:true}
###

# '/1.5/wecard/prop/:Uid/:ml_num'
# shows wecard of some property wirh ml_num edited by user id -> user wecard id
# does not perform search,
GET 'prop/:uid/:ml_num',(req,resp) ->
  return process_wecard_req({req, resp})

#==============================================
#           Layouut and Views


#------------------------------------------------------------
#      View
###
VIEW 'wecardPublic', ->
  js '/js/cardshowpage.js'

  div class:'WSBridge', style:'display:none', ->
    # span id:'share-title-en', ->
    #   text "RealMaster WeCard "
    # span id:'share-desc-en', ->
    #   text
    span id:'share-title', ->
      text ""
    span id:'share-desc', ->
      text _ "Cell:"
    span id:'share-url', ->
      link_url = "#{@fullUrl}"
      text link_url
    span id:'share-image', ->
      text "#{@req.shareHost()}/img/logo.png"

  body id: 'show-page-body', class: 'ng-scope show-page-body', ->
    unless @noBottomBar
      footer ->
        div class: 'bar bar-standard bar-footer show-footer', style:'  background-color: #E03131; border-top:1px none;' , ->
          div class: 'pull-left', ->
            a class: 'icon icon-footer-left1', style:'color:white',onclick:'toggleCover();', ->
              text _ 'Add Favourite' #'收藏名片夹'
            if @uid
              # dlpage show followee info accoring to uid
              aparam = href:'/getapp'+'?uid='+@uid
            else
              aparam = href:'/getapp'
            a aparam, class: 'icon icon-footer-left2', style:'color:white; margin-left:8px; color:white', ->
              text _ 'Get WeCard'#'我要名片'
    style = ""
    if @noBottomBar
      style = "margin-bottom: 0;"
    div id:'show-page-contents', class: 'contents show-page-contents edit-page',style:style, ->
      div class: 'top-right-icon', ->
        a id: 'mp3-btn', class: 'mp3 icon ', href: '#', ->
          audio id: 'musicPlayer', loop: '',  src: '', style: 'display:none;position:absolute;z-index:-11'
      ul id: 'items-ul', class: 'items-ul', ->
        li class: 'company-name', dataRole: 'company-name', ''
        li class: 'nick-photo', dataRole: 'nick-photo', -> #style:'display:none',
          div class: 'photo', ->
            img dataRole: 'nick-img', src: ''#/img/p.png'
          p class: 'nn', style:'margin-top:5px', ->
            span dataRole: 'nick-nm'
            i class: 'icon1', style: 'display:none;'
        li dataRole: 'intr', ''
        li class: 'map-item-ul', dataRole: 'map-item-ul', ->
          ul ->
            li ->
              a href: '/1.5/srh', ->
                div class: 'photo fa fa-map-marker', style:'', ->
                p -> "Properties"#'周边房源'
            # li ->
            #   a href: '#', ->
            #     div class: 'photo', ->
            #       img src: '/img/listing.png'
            #     p -> '我的房源'
            li ->
              a href: '/1.5/srh?ss=1', ->
                div class: 'photo fa fa-graduation-cap',style:'font-size: 35px;  padding-top: 12px;  padding-left: 3px;', ->
                p -> "Schools"#'周边学校'
            li ->
              a href: '/1.5/school/rank', ->
                div class: 'photo fa fa-trophy', style:'font-size: 35px;  padding-top: 13px;  padding-left: 2px;', ->
                p -> _ "School Rank"#'学校排名'
        li class: 'item5', dataRole: 'ctct', ->
          ul ->
            li ->
              span class:'label', -> _ "Mobile"#'手机:'
              i dataRole: 'ctct-tel'
              a class: 'tel-btn', href: '#', dataRole: 'ctct-tel2', ->
                i()
                span -> _ "Dial"#'一键拨号'
            li ->
              span class:'label', -> _ "Email"#'邮箱:'
              i dataRole: 'ctct-eml'
              label class: 'link-color', ''
            li ->
              span class:'label', -> _ "Wechat"#'微信:'
              i dataRole: 'ctct-wx'
              a id: 'quarstr-btn', href:'#', class:'fa fa-qrcode ', style:'  border: 1px solid white;  border-radius: 50%;  color: white;  margin-right: 43px;  font-size: 15px;  padding: 4px 5px 3px 5px;  float: right;'
            li ->
              span class:'label', -> _ "Website" #'网站:'
              a dataRole: 'ctct-web'
              label class: 'link-color', ''
        li class: 'item5', dataRole: 'cpnydtl', ->
          ul ->
            li ->
              span class:'label', -> _ "Company Phone"#'公司电话:'
              i dataRole: 'cpnydtl-tel'
              # a class: 'tel-btn', href: '#', dataRole: 'cpnydtl-tel2', ->
              #   i()
              #   span -> '一键拨号'
            li ->
              span class:'label', -> _ "Company Fax"#'公司传真:'
              i dataRole: 'cpnydtl-fax'
            li ->
              span class:'label', -> _ "Company Website" #'公司网站:'
              a dataRole: 'cpnydtl-web'
              label class: 'link-color', ''
            li ->
              span class:'label', -> _ "Company Adress"#'公司地址:'
              i dataRole: 'cpnydtl-addr'

        li class: 'media-link', dataRole: 'media-link', ->
          a href:'#', class:'sns-link', dataRole:'media-facebook', ->
            i class:'fa fa-facebook'
          a href:'#', class:'sns-link', dataRole:'media-twitter', ->
            i class:'fa fa-twitter'
          # a href:'#', dataRole:'media-linkedin', ->
          #   i class:'fa fa-linkedin'
          a href:'#', class:'sns-link', dataRole:'media-weibo', ->
            i class:'fa fa-weibo'

    div id: 'qrcode-cover', class: 'qrcode-cover', style: 'display:none;', ->
      h4 _ "Scan And Save My Card"#'扫描收藏我的微名片'
      div id: 'qr_code_img', class: 'i', dataRole:'ctct-wxqr', ''
      p ->
        text _ "Long Press To Save or Scan"#'长按二维码可保存或识别'
        br()
        #text _ "For Print"#'可印在纸质名片和宣传单上'
      div class: 'line', style:'margin:10px;'
      a class: 'btn-close', href: '#', ->
        text _ "Close"#'关闭'
    div id: 'content-cover', class: 'content-cover', style: 'display:none;', ->
      div class: 'arrow', ->
        img src: '/img/arrow.png', style:'margin-left: 31px;'
      div class: 'layer', ->
        p class: 'tip', href: '#', ->
          # text '关住才能收藏哦'
        p ->
          text _ "1. Click top right corner"#'1. 点击右上角'
          img src: '/img/share-icon.png', alt: ''
        p ->
          text _ "2. Click 'Favorite' "#'2. 点击收藏'
          text _ "to save"#'即可收藏'
###
#----------------------------------------------------------------------
#         User Card Edit

###
VIEW 'wecardEdit', ->
  js '/js/cardeditpage.js'
  js '/js/app.js'
  # js '/js/classie.js'
  css '/css/smb.css'
  body id: 'edit-page-body', class: 'ng-scope edit-page-body', ->
    header class: 'bar bar-nav', ->
      a class: 'icon icon-left-nav pull-left', href: '#', dataIgnore: 'push', style:'color:white',onclick:'goBack(event);'
      h1 class: 'title', ->
        text  _ "WeCard"#'房大师'


    footer ->
      div class: 'bar bar-standard bar-footer edit-footer', ->
        div class: 'pull-left', ->
          a class: 'icon icon-footer-left1 fa fa-picture-o',style:'color:#2f3e46; margin:0px ;font-size:22px; margin-left:6px;', onclick:"toggleBgs();"
          a class: 'icon icon-footer-left2 fa fa-volume-up',style:'color:#2f3e46; margin:0px; margin-left: 16px; ;font-size:22px;',onclick:"toggleMius();"
          link_url = "#{@req.shareHost()}/1.5/wesite/#{@req.user.id}"
          if true#(k = @req.cookies['apsv'])
            aparam = onclick:"RMSrv.showInBrowser('#{link_url}')"
          else
            aparam = href:'#' # TODO
          a class: 'icon icon-footer-left3 fa fa-desktop  pull-right',style:'color:#2f3e46; margin:0px; margin-left:16px;
            margin-top: 2px; ;font-size:22px;',aparam
        a class: 'icon icon-footer-right2 icon-share  pull-right', onclick: "RMSrv.share('show');",style:'color:#E03131; margin:0px'
    # if @req.isAllowed 'shareWeCard'
    text ckup 'shareDialog2',{page:'wecard', req:@req}

    div id: 'edit-page-contents', class: 'contents edit-page wecard-edit', ->
      ul id: 'edit-page-contents-ul', class: 'items-ul', ->
        li class: 'company-name', dataRole: 'company-name', ->
          a class: 'btn-r btn-sort', href: '#'
          a class: 'btn-r btn-see', href: '#'
        li class: 'nick-photo', dataRole: 'nick-photo', ->
          div class: 'photo', ->
            img class: 'nick-img', dataRole: 'nick-img', src: ''
          p class: 'nn', ->
            span class: 'nick-nm', dataRole: 'nick-nm'
            i class: 'icon1', style: 'display:none;'
        li class: 'intr', dataRole: 'intr'
        if @req.isAllowed 'wecardRltrMnu' #, @user
          li class: 'map-item-ul', dataRole: 'map-item-ul', style:'padding-top:13px;', ->
            ul ->
              li ->
                a href: '#', ->
                  div class: 'photo fa fa-map-marker', style:'', ->
                  p -> _ "Properties" #'周边房源'
              # li ->
              #   a href: '#', ->
              #     div class: 'photo', ->
              #     p -> '我的房源'
              li ->
                a href: '#', ->
                  div class: 'photo fa fa-graduation-cap',style:'font-size: 35px;padding-top: 12px;padding-left: 3px;', ->
                  p -> _ "Schools"#'周边学校'
              li ->
                a href: '#', ->
                  div class: 'photo fa fa-trophy', style:'font-size: 35px;padding-top: 13px; padding-left: 2px;', ->
                  p -> _ "School Rank"#'学校排名'



        li class: 'item5 ctct', dataRole: 'ctct', ->
          ul ->

            li ->
              span class:'label', -> _ "Mobile"#'手机:'
              i class: 'ctct-tel', dataRole: 'ctct-tel'
            li ->
              span class:'label', -> _ "Email"#'邮箱:'
              i class: 'ctct-eml', dataRole: 'ctct-eml'
              label class: 'link-color', ''
            li ->
              span class:'label', -> _ "Wechat"#'微信:'
              i class: 'ctct-wx', dataRole: 'ctct-wx'
            li ->
              span class:'label', -> _ "Website"#'网站:'
              i class: 'ctct-web', dataRole: 'ctct-web'
              label class: 'link-color', ''
        li class: 'item5 cpnydtl', dataRole: 'cpnydtl', ->
          ul ->
            li ->
              span class:'label', -> _ "Company Phone"#'公司电话:'
              i class: 'cpnydtl-tel', dataRole: 'cpnydtl-tel'
            li ->
              span class:'label', -> _ "Company Fax" #'公司传真:'
              i class: 'cpnydtl-fax', dataRole: 'cpnydtl-fax'
            li ->
              span class:'label', -> _ "Company Website"#'公司网站:'
              i class: 'cpnydtl-web', dataRole: 'cpnydtl-web'
              label class: 'link-color', ''
            li style:'  height: auto;', ->
              span class:'label', -> _ "Company Address" #'公司地址:'
              i class: 'cpnydtl-addr', dataRole: 'cpnydtl-addr'

        li class: 'media-link', dataRole: 'media-link', ->
          a href:'#', class:'sns-link', dataRole:'media-facebook', ->
            i class:'fa fa-facebook'
          a href:'#', class:'sns-link', dataRole:'media-twitter', ->
            i class:'fa fa-twitter'
          # a href:'#', dataRole:'media-linkedin', ->
          #   i class:'fa fa-linkedin'
          a href:'#', class:'sns-link', dataRole:'media-weibo', ->
            i class:'fa fa-weibo'

    div class: 'bgs-map bottom-cover', style: 'display:none;', ->
      div class: 'select-header', ->
        a href:'#', id:'li-bgs-close', class:'icon icon-close li-items-close'
        text _ 'Select Background Picture'
      ul id: 'bgs-map-ul', style:'padding-top: 54px;', ->
        li ->
          img src: '/img/bg0.png'

    div id: 'backdrop', class: 'backdrop', style: 'display:none;'

    div class: 'bgs-mp3 bottom-cover', style: 'display:none;', ->
      div class: 'select-header', ->
        a href:'#', id:'li-music-close', class:'icon icon-close li-items-close'
        text _ 'Select Background Music'
      ul id: 'bgs-mp3-ul', style:'padding-top: 54px;', ->
        comment '<li><a href=""></a> 音乐名字 <span>选择<span/></li>'
  coffeejs { vars:{
    wDlDisable: not (@req.isAllowed('newsAdmin') or (@req.isAllowed 'vipUser')),
    isVipUser: @req.isAllowed('vipUser'),
    newsAdmin: @req.isAllowed('newsAdmin'),
    vipTipStr: @req.l10n("Available only for Premium VIP user! Upgrade and get more advanced features."),
    vipLaterStr: @req.l10n('Later'),
    vipSeeStr: @req.l10n('See More'),
    allowedShareSignProp:@req.isAllowed('shareSignProp')} }, ->
    null
###
#=====================================================================
#             General purpose Card Edit (listing/event/)

VIEW 'propCardEdit', ->
  css '/css/smb.css'
  css '/css/summernote-bs3.css'
  css '/css/summernote.css'
  body id: 'housecard-page-edit-body', class: 'ng-scope housecard-page-edit-body', ->
    href = @d || '/1.5/wecard'
    unless @req.isMobile()
      js '/js/ratchet/fingerblast.js'
      #add touchstart simulator for desktop devices
      text """
      <script>
      window.onload = function() {
        if (window.FingerBlast) {
          new FingerBlast('#fb-toggle');
          new FingerBlast('#mblreq-toggle');
          console.log('inited');
        }
      }
      </script>
      """
      text """<style>
      div.note-editor .note-editable{
        margin-top:75px;
      }
      </style>"""
    header class: 'bar bar-nav', ->
      a class: 'icon fa fa-back pull-left', href:'javascript:;',id:'listPageBtn', dataIgnore: 'push'#javascript:window.location.href = document.referrer;
      h1 class: 'title', ->
        text _ "WePage"#'房大师'

    div class:'WSBridge',style:'display:none', ->
      span id:'share-title-en', ->
        text "RealMaster WeCard " + @user.fnm
      span id:'share-desc-en', ->
        text (@user.mbl or @user.tel) + " " + (@user.itr or @user.sgn or '')
      span id:'share-title', ->
        text _("RealMaster") + " " + _("WeCard") + " " + @user.fnm
      span id:'share-desc', ->
        text _('Tel:') + (@user.mbl or @user.tel) + "<br/>" + " " + (@user.itr or @user.sgn or '')
      span id:'share-url', ->
        link_url = "#{@req.shareHost()}/1.5/user/unlock?lang=#{@req.locale()}"
        if @req.isAllowed 'shareWeCard' #, @user
          link_url = "#{@req.shareHost()}/1.5/wesite/#{@user.id}?lang=#{@req.locale()}"
        text link_url
      span id:'share-data', ->
        text ""
      span id:'share-image-uri', -> text ''
      span id:'share-image', ->
        if @user.avt
          text @user.avt
        else
          text "#{@req.shareHost()}/img/logo.png"

      span id:'share-avt', ->
        if @user.avt
          text @user.avt
        else
          text "#{@req.shareHost()}/img/icon_nophoto.png" #TODO: change pic

    # div class:"overlay",id:'loading-bar-spinner',style:"z-index: 20; position: fixed;    top: 50%; left: 50%; height:0; width:0;  display:none;   ", ->
    #   img src:"/img/ajax-loader.gif",style:"    margin-left: -109px;   margin-top: -75px;"

    div class:"overlay loader-wrapper", id:'loading-bar-spinner', ngShow:"loading", style:'display:block',->
      div class:'loader'

    text ckup 'flashMessage',{id:'no-title',msg:_("Title is Required")}
    text ckup 'flashMessage',{id:'page-saved',msg:_("WePage Saved")}
    text ckup 'flashMessage',{id:'img-inserted',msg:_("Image Inserted")}
    text ckup 'flashMessage',{id:'url-error',msg:_("Not A Valid URL Address")}
    text ckup 'flashMessage',{id:'server-error',msg:_("Server Error")}
    text ckup 'flashMessage',{id:'vip-only',msg:_("VIP only")}

    # Allow all users to share wecard for now
    #if @req.isAllowed 'shareWeCard'
    opt = {page:'wepage', noLang:1, wDlDisable:1, req:@req}
    if @req.isAllowed('newsAdmin') or (@req.isAllowed 'vipUser')
      opt.wDlDisable = 0
    text ckup 'shareDialog2', opt

    # if @req.isAllowed 'newsAdmin'
    #   text ckup 'shareDialog', {noLang:1, publishNews:1}
    # else if @req.isAllowed 'shareSign'
    #   opt = {noLang:1 , showSign:1}
    #   unless @req.isAllowed 'canHidePromo'
    #     opt.forceDownload = 1
    #   text ckup 'shareDialog', opt#height:'290px'
    # else
    #   text ckup 'shareDialog', {noLang:1 , forceDownload:1}
    #else
    #  text ckup 'toBeUnlocked'

    div id: 'housecard-page-contents', class: 'contents housecard-page edit', ->
      div class:'' ,id:'wecardSearchBar', style:' background-color: #EEE; padding:5px 10px;   font-size: 13px;', ->
        # div ->
        #   label class:'col-3' , for:"template" , -> text _("Template") + "*"
        #   select  id:'tpSelect',dataRole:'meta-template', style:'background-color:#EEEEEE; width:69%;-webkit-appearance: menulist; padding-left: 5px;', -> #onchange:"setType()", ->
        #     option value: "Topic", -> text _ "Topic",'blog_type'
        #     option value: "Listing", -> text _ "Listing",'blog_type'
        #     option value: "Exclusive Listing", -> text _ "Exclusive Listing",'blog_type'
        #     option value: "Assignment", -> text _ "Assignment",'blog_type'
        #     option value: "Event", -> text _ "Event",'blog_type'
        #     option value: "Blog", -> text _ "Blog",'blog_type'
        #     option value: "Xmas Biz", -> text _ "Xmas Biz",'blog_type'
        #     option value: "Xmas Cartoon", -> text _ "Xmas Cartoon",'blog_type'
        #     option value: "Flyer", -> text _ "Flyer",'blog_type'
        div ->
          label class: 'col-3', for:"title", style:"vertical-align: top;", -> text _("Title") + "*"
          # input class:'no-outline col-9 ',type:'text',dataRole:'meta-title', placeholder:''
          textarea class:' col-9 no-outline',dataRole:'meta-title',onblur:"$(this).attr('rows', '2');", onfocus:"$(this).attr('rows', '4');",rows:'2',placeholder:'', style:'padding-left: 5px; resize: none; font-size:14px;'

        div ->
          label class: 'col-3', for:"editor" , style:"vertical-align: top;", -> text _("Editor") + "*"
          textarea class:' col-9 no-outline',dataRole:'meta-editor', rows:'1',placeholder:'Name', style:'line-height: 2em; padding-left: 5px; resize: none; font-size:14px;', ->
            text "" + (@user.wxgrpnm or @user.fnm)

        # div style:'height:33px;', ->
        #   label class: 'col-3', for:"showstyle" , -> text _("Show Style")
        #   span id: 'showStyleSelect', class: 'btn-group pull-right', role: 'group', ariaLabel: '...',style:'padding-right:8px;   margin-top: -1px;', ->
        #     button class: 'btn btn-default active', id:'showStyleBlog', type: 'button',  style:"font-size: 10px;  padding: 5px 6px; ", -> _ 'Blog' #onclick:'setStyle("Blog");',
        #     button class: 'btn btn-default', id:'showStyleVT',  type: 'button', style:"font-size: 10px;  padding: 5px 6px; ", -> _ 'Virtual Tour' #onclick:'setStyle("VT");',
        div ->
          label class: 'col-3', for:"desc" , style:'vertical-align: top;', -> text _ "Summary"
          textarea class:' col-9 no-outline',dataRole:'meta-desc',onblur:"$(this).attr('rows', '2');", onfocus:"$(this).attr('rows', '4');",rows:'2',placeholder:'', style:'padding-left: 5px; resize: none; font-size:14px;'
        div ->
          label class: 'col-3',  for:"vc" , -> text _ "Base Count"
          input class:' col-9 no-outline', type:'number',dataRole:'meta-vc', style:'width:56%;', placeholder:''
          #a class:'', href:'#', style:'width:9%; text-align: center; margin-left:5px;', id:'viewCount', -> "0"
        div ->
          label class: 'col-3',  for:"map" , -> text _ "Address"
          input class:' col-9 no-outline', type:'text',id:'meta-addr', dataRole:'meta-addr', style:'width:56%;', placeholder:_('Address Or Coordinate')
          a id:'showMap',class:'fa fa-map-marker', style:'width:9%; text-align: center; margin-left:5px; cursor: pointer;' #onclick:"showMap()"
        div id:'id_map_outer',style:'margin:5px 0px 0px 0px;  display:none; height:210px;', ->
          div id:'id_d_map', style:'height:205px;', ->
        div ->
          label class: 'col-3', for:"thumb", style:"vertical-align: top;", -> text _ "Thumb Image" #缩略图
          if @user.avt
            thumbSrc =  @user.avt
          else
            thumbSrc = "#{@req.shareHost()}/img/icon_nophoto.png" #TODO: change pic
          img src:thumbSrc, id:'thumbImg'
        div class:'meta-split',->
        div ->
          label class: 'col-3',  for:"music" , style:"vertical-align: top;margin-top: 1px;", -> text _ "Music"
          div class:"col-9", style:"display:inline-block", id:'musicWrapper',->
            div class:"toggle rm flat", id:'music-toggle',->
              div class:"toggle-handle"
        div ->
          label class: 'col-3',  for:"fb" , style:"vertical-align: top;margin-top: 1px;", -> text _ "Feedback"
          div class:"col-9", style:"display:inline-block", id:'fbWrapper',->
            divClass = "toggle rm flat"
            notAllowedFb = not @req.isAllowed 'webEdit'
            if notAllowedFb
              divClass += ' disabled'
            div class:divClass, id:'fb-toggle',->
              div class:"toggle-handle"
            if notAllowedFb
              div class:'vip',->
                text 'VIP only'
              locale = @req.locale()
              # url = 'https://www.realmaster.ca/membership'
              a href:'javascript:void 0', onclick:"RMSrv.showInBrowser('https://www.realmaster.ca/membership')", ->
                text _ "Learn More", 'getvip'
        div id:"fbTitle", style:'display:none; margin-top: 5px;',->
          label class: 'col-3',  for:"fb" , -> text _ "Form Title"
          input class:' col-9 no-outline', type:'text', dataRole:'meta-fb-tl', placeholder:_('Feedback Form Title')
        div id:"fbMblReq", style:'display:none; margin-top: 5px;',->
          label class: 'col-3',  for:"mblreq" , style:"vertical-align: top;margin-top: 1px;", -> text _ "Tel # Required"
          div class:"col-9", style:"display:inline-block", id:'mblWrapper',->
            divClass = "toggle rm flat"
            notAllowedFb = not @req.isAllowed 'webEdit'
            if notAllowedFb
              divClass += ' disabled'
            div class:divClass, id:'mblreq-toggle',->
              div class:"toggle-handle"
            # if notAllowedFb
            #   div class:'vip',->
            #     text 'VIP only'
            #   locale = @req.locale()
            #   a href:'javascript:void 0', onclick:"RMSrv.showInBrowser('/event?tpl=getvip&lang=#{locale}')", ->
            #     text _ "Learn More", 'getvip'

      ul id:'edit-page-contents-ul', class: 'items-ul', style:'padding-top: 10px; display:none;', ->

        li class: 'item-img edit-in-summernote', dataRole:'prop-img-pane', ->
          div ->
            img class:'prop-pic', src: '/img/p1.png'
            div class: 'des', ->
              p ->
                '$800,000.0 For Sale'
              p ->
                '123 Main St Toronto, Qntrao'
              p ->
                'Semi-Detached 2-Stores'


        li class: 'item-detail edit-in-summernote', dataRole:"prop-detail-pane", ->
          div ->
            div class: 'detail-ul', style:"  text-align: left; margin-left: 0px;", ->
              for n in ['lp_price',['type_own1_out','style','prop_type','bus_type'],['br','mark_plus','br_plus'],['num_kit','mark_plus','kit_plus'],
                ['gar_spaces','gar_type','park_spcs'],['bsmt1_out','bsmt2_out'],['bath_tot','bath_details'],
                ['front_ft','mark_x','depth','lotsz_code','irreg'],['constr1_out','constr2_out'],['taxes','mark_div','yr'],'sqft','a_c',
                'central_vac','pool',['fuel','heating'] ,'rltr'] # , 'yr_built' 'condo_exp', 'stories', 'comp_pts',  ['condo_corp','corp_num'] ,
                #,'ad_text' 'prop_mgmt','com_cn_fee', 'share_perc', 'locker_num','fin_stmnt','franchise','freestandg',,'email']
                if n instanceof Array
                  div class:'',  ->
                    span dataRole:'', style:'width: 33%; min-width: 90px; display: inline-block; font-weight: bold;', ->
                      text _ab n[0], 'propertyListing'
                    label dataRole:"prop-#{n[0]}", style:'display: inline-block;line-height: 18px;  font-weight: normal; font-size: 14px;', ->
                      for i in n
                        text "#{i}  "
                else
                  div class:'', ->
                    span dataRole:'', style:'width: 33%; min-width: 90px; display: inline-block; font-weight: bold;', ->
                      text _ab n, 'propertyListing'
                    label dataRole:"prop-#{n}", style:'display: inline-block;line-height: 18px;  font-weight: normal; font-size: 14px;', ->
                      text "#{n}"

        li class:'item-remark edit-in-summernote', dataRole:"prop-remark-pane", ->
          div ->
            div class:'', style:'padding:10px 10px 10px 0;', ->
              span dataRole:'', style:'width: 33%; min-width: 90px; display: inline-block; font-weight: bold;', ->
                text _ab 'ad_text', 'propertyListing'
              label dataRole:"prop-ad_text", style:'display: inline-block;line-height: 18px; font-weight: normal; font-size: 14px;', ->
                text "Remarks"


        li class: 'item-contact edit-in-summernote', dataRole:'ctct', ->
          div id:'realtorInfoFrame', style:"height: 100%;   text-align: left; ", ->
            div style:"width: 100%; font-size: 15px; text-align: center; padding:0 0 33px 0;", ->
              text _ "Thanks For Watching"

            div class:'wrapper', style:"width:100%;     overflow: hidden;", ->
              div style:" width:calc(100% + 230px);     background: #312440;", ->
                div class:"info", style:"height:100%; width:calc(100% - 320px); display:inline-block;  padding: 20px 0; transition: all 0.3s; -webkit-transition: all 0.3s;", ->
                  div class: 'photo', dataRole:'nick-photo', style:'text-align:center;', ->
                    img src: '/img/p.png', dataRole:'nick-img', style:'border-radius: 50px; height: 100px; width: 100px;'
                  p class: 'nn',  style:'text-align:center;     font-size: 18px; color:white;', ->
                    span dataRole:'nick-nm', ->
                      text _ 'First Name/Last Name'
                    #TODO: req.isAllowed
                    i class: 'icon1', style:"background: url('/img/realtor.png') center center;  background-size: 14px 14px;  display: inline-block;  width: 14px;  height: 14px;  margin: 0 0 -2px 5px;"
                    # br ->
                    # span dataRole:'ctct-cpny_pstn', style:'', ->
                    #   text "Position."

                  div style:'border-top: 3px solid #F38337;     width: 75%;    margin-left: 15%;    margin-top: 10px;'
                  ul class: 'contact-ul', style:"  width: 100%; margin: 20px auto 0; list-style: none;", ->
                    li  style:"border: none;  text-align: center;  transform: padding: 0px 0 0 0;  margin: 0;  font-weight: bold;", dataRole: 'ctct-wx-wrapper', ->
                      div style:'min-width:160px; text-align:left; display: inline-block;', ->
                        # text _('WeChat') + ": "
                        i class:"fa fa-wechat", style:"color:white; font-size: 16px; width: 23px;  vertical-align: sub;"
                        i class: 'ctct-wx', style:'font-weight: normal;   color:white;  font-size: 15px;', dataRole: 'ctct-wx'

                    li  style:"border: none;  text-align: center;  transform: padding: 0px 0 0 0;  margin: 0;  font-weight: bold;", dataRole: 'ctct-tel-wrapper', ->
                      div style:'min-width:160px; text-align:left; display: inline-block;', ->
                        # text _ 'Cell Phone' #15101012229
                        i class:"fa fa-phone-square", style:"color:white; font-size: 18px; width: 23px;  vertical-align: sub;"
                        # text ": "
                        label class: 'ctct-tel', style:'font-weight: normal;   color:white;  font-size: 15px;', dataRole: 'ctct-tel'
                    li style:"border: none;  text-align: center;  transform: padding: 0px 0 0 0;  margin: 0;  font-weight: bold;",  ->
                      div style:'min-width:160px; text-align:left; display: inline-block;', ->
                        # text _ 'Email'
                        i class:"fa fa-envelope", style:"color:white; font-size: 16px; width: 23px;  vertical-align: sub;"
                        # text ": "
                        label class: 'link-color', style:'font-weight: normal; font-size: 15px;', dataRole:'ctct-eml', ->
                          text '' #<EMAIL>
                    li style:"border: none;  text-align: center;  transform: padding: 0px 0 0 0;  margin: 0;  font-weight: bold;", dataRole:'ctct-web-wrapper', ->
                      div style:'min-width:160px; text-align:left; display: inline-block;', ->
                        # text _ 'Website'
                        i class:"fa fa-globe  ", style:"color:white; font-size: 18px; width: 23px;  vertical-align: sub;"
                        # text ": "
                        label class: 'link-color', style:'font-weight: normal; font-size: 15px;',  dataRole:'ctct-web', ->
                          text ''
                    # li style:"border: none;  text-align: center;  transform: padding: 0px 0 0 0;  margin: 0;  font-weight: bold;", ->
                    #   div style:'min-width:160px; text-align:left; display: inline-block;', ->
                    #     text _ 'Company'
                    #     text ": "
                    #     label class: '', style:'font-weight: normal;', dataRole:'ctct-cpny', ->
                    #       text ''
                # coffeejs {}, ->
                #   alert 'test'
                #   null
                div class:"wrapper", style:"width: 210px;  display: inline-block; vertical-align: top;", ->
                  div class:"qrcd", id:"userQrcd", style:"height:100%; width:450px; display:inline-block;   vertical-align: top;
                  margin-top: 20px;     margin-bottom: 20px; overflow-x:auto;
                  transform: translate3d(-13px, 0px, 0px); ", ->
                    div style:" width:450px; ", ->

                      div style:" height: 260px; vertical-align: top;  text-align: center; display: inline-block;
                      background-color: white;  padding: 5px; ", dataRole:"ctct-wxqr-wrapper", ->
                        img src:"/img/icon_noqrcode.png", style:"height:160px; width:160px; ", dataRole:"ctct-wxqr"
                        div style:"color: black;     padding-top: 20px;", ->
                          text _ "Follow my WeChat"
                        div style:"color: black", ->
                          text _ "Long Press or Scan"

                      div style:" height: 260px; vertical-align: top; text-align: center; display: inline-block;
                      margin-left:10px; background-color: white; padding: 5px; ", dataRole:"ctct-grpqrcd-wrapper", ->
                        img src:"/img/icon_noqrcode.png", style:"height:160px; width:160px; ", dataRole:"ctct-grpqrcd"
                        div style:"color: black;     padding-top: 20px;", ->
                          text _ "Follow my WeChat"
                        div style:"color: black", ->
                          text _ "Long Press or Scan"


            div style:"width: 100%;      font-size: 15px;    text-align: center;    padding-top: 33px;", ->
              text _ "RealMaster WePage Show"

        li class: 'item-topic edit-in-summernote', dataRole:'topic', ->
          div dataRole:'topic-content', ->
            text "topic content from domain"
        # added use js
        # li class: 'item-add', dataRole:'new-frame', style:'height: 73px; font-size:27px; padding-top: 20px; ', ->
        #   div ->
        #     a class: 'fa fa-plus-circle', href: '#', style:"color: #666", onclick:'javascript:void(0);'


    footer ->
      div class: 'bar bar-standard bar-footer footer-tab', ->
        if @req.getDevType() is 'app'
          a id:'wepageShareBtn', href:"#", onclick: "RMSrv.share('show');", class: 'pull-right disabled', ->
            span class:"icon icon-share"
            span class:"tab-label", ->
              text _ "Share"
        a class: 'pull-left', id:'saveCard', ->
          span class:"icon fa fa-save"
          span class:"tab-label", ->
            text _ "Save"
        a class: 'pull-left', id:'delCard', ->
          span class:"icon fa fa-trash"
          span class:"tab-label", ->
            text _ "Delete"
        a class: 'pull-left footer-icon-music',  ->
          span class:"icon fa fa-volume-up "
          span class:"tab-label", ->
            text _ "Music"
        a class: 'pull-left', id:'wepagePreviewBtn',  ->
          span class:"icon fa fa-desktop"
          span class:"tab-label", ->
            text _ "Preview"
        if @req.isAllowed('newsAdmin')
          a class: 'pull-left', id:'shareToNews', ->
            span class:"icon fa fa-save"
            span class:"tab-label", ->
              text _ "Push to Forum"
        # TODO: upload to s3
        # if true #@req.isAllowed('waterMark')
        #   a class: 'pull-left footer-icon-wmrk',  ->
        #     span class:"icon fa fa-user-plus"
        #     span class:"tab-label", ->
        #       text _ "Mark"

    # footer ->
    #   div class: 'bar bar-standard bar-footer edit-footer housecard-edid-footer', ->
    #     div class: 'pull-left', ->
    #       a class: 'icon icon-footer-left1 fa fa-save', id:'saveCard' # onclick:"saveCard();"
    #       a class: 'icon icon-footer-left2 fa fa-volume-up'
    #       a id:'wepagePreviewBtn', class: 'icon icon-footer-left3 fa fa-desktop disabled' #, aparam
    #       if true #@req.isAllowed('waterMark')
    #         a class: 'icon fa fa-user-plus footer-icon-wmrk', style:''
    #     a id:'wepageShareBtn', class: 'icon icon-footer-right2 icon-share  pull-right disabled', onclick: "RMSrv.share('show');", style:'margin:0px'

    div id:"editorModal", class:"modal" , style:"z-index:15;", ->
      header class:"bar bar-nav", ->
        a class:"icon icon-close pull-right", href:"#", id:'saveFrame', onclick:"toggleModal('editorModal');"
        h1 class:"title", ->
          text _ "Editor"
          text _ " No."
          span id:'frameNumber', -> _ ""
          text _ " Frame"

      div class:"content" , ->
        #p class:"content-padded" , ->
        #  text ""
      div class:"summernote", ->
        text ""


    div id:"frameEditorModal", class:"modal ", ->
      header class:"bar bar-nav", ->
        a class:"icon icon-close pull-right", href:"#", id:'savePicFrame', onclick:"toggleModal('frameEditorModal');"
        h1 class:"title", ->
          text _ "Editor"

      div class:"bar bar-standard bar-header-secondary", ->
        div class:"row row1", ->

          div style:" ", class:"row", ->#width: 50%;
            div class:"desc", ->
              text _ "Background"
            div class:"btn-group", ->
              button class:"btn btn-default btn-sm", id:"pvReplaceImg", ->
                i class:"fa fa-image"
              button class:"btn btn-default btn-sm", id:"pvRemoveBgImg", ->
                i class:"fa fa-eraser"
              button class:"btn btn-default btn-sm",id:"pvShiftImgPosition", ->
                i class:"fa fa-arrows-v"

          div style:" text-align: right; padding-right:5px;", class:"row", ->
            div class:"desc", ->
              text _ "Text"
            div class:"btn-group", ->
              button class:"btn btn-default btn-sm", id:"pvEditPreviewText", ->
                i class:"fa fa-pencil"
              button class:"btn btn-default btn-sm", id:"pvShiftTextPosition", ->
                i class:"fa fa-text-height"
              button class:"btn btn-default btn-sm", id:"pvTextBg", ->
                i class:"fa fa-square"
              button class:"btn btn-default btn-sm", id:"pvPreviewAnimation", ->
                i class:"fa fa-play"

        div class:"row row2", ->
          div class:"desc", ->
            text _ "Device Size"
          div style:"padding-top: 8px;  width: 125px;", ->
            input id:"devWidthSlider", type:"range", style:"width:100%", min:"0", max:"4", step:"1", value:'0'
          div class:"desc", ->
            span id:"fs-tip-sm", -> text _ "Small Screen Mobile"
            span id:"fs-tip-md", style:"display:none", -> text _ "Mid Screen Mobile"
            span id:"fs-tip-lg", style:"display:none", -> text _ "Large Screen Mobile"
            span id:"fs-tip-pd", style:"display:none", -> text _ "Pad"
            span id:"fs-tip-pc", style:"display:none", -> text _ "PC"

      div class:"content" , id:"previewContent", style:"padding-top: 119px;", ->
        div style:"height:100%; padding: 10px; overflow:hidden; width:100%;", id:"frame-preview-wrapper", ->
          iframe id:"frame-preview", src:"/adPage/news"


    # text ckup "img-select-modal"

    div id: 'backdrop', class: 'backdrop', style: 'display:none;'

    div class: 'bgs-map bottom-cover', style: 'display:none;', ->
      div class: 'select-header', ->
        a href:'#', id:'li-bgs-close', class:'icon icon-close li-items-close'
        text _ 'Select Background Picture'
      ul id: 'bgs-map-ul', style:'padding-top: 54px;', ->
        li ->
          img src: '/img/bg0.png'

    div class: 'bgs-mp3 bottom-cover', style: 'display:none;', ->
      div class: 'select-header', ->
        a href:'#', id:'li-music-close', class:'icon icon-close li-items-close'
        text _ 'Select Background Music'
      ul id: 'bgs-mp3-ul', style:'padding-top: 54px;', ->

    js '/js/app.js'
    js '/js/bootstrap.min.js'
    js '/js/ratchet/toggles.js'
    #js '/js/summernote.mi.js'
    js '/js/sn2.mi.js' # Fred add delToEnd/delToStart
    coffeejs { vars:{
      # appVerNum:@req.getAppVerNumber(),
      gurl:@gmap_url,
      maxImageSize: @maxImageSize,
      ERR_PRO_IMG: @req.l10n('Cannot remove profile images!'),
      tooBigErrStr: @req.l10n('Too many Content, please reduce content or contact admin'),
      editStr: @req.l10n('Edit'),
      liftStr: @req.l10n('Move Up'),
      hideStr: @req.l10n('Hide'),
      tooLargeStr: @req.l10n('File too large'),
      wDlHide: not (@req.isAllowed('newsAdmin') or (@req.isAllowed 'vipUser')),
      isVipUser: @req.isAllowed('vipUser'),
      publishNews: @req.isAllowed('newsAdmin') or @req.isAllowed('vipAlliance'),
      allowedShareSignProp:@req.isAllowed('shareSignProp'),
      vipTipStr: @req.l10n("Available only for Premium VIP user! Upgrade and get more advanced features."),
      vipLaterStr: @req.l10n('Later'),
      vipSeeStr: @req.l10n('See More'),
      strDeleteTip: @req.l10n(@DELETE_WECARD),
      strDeleteSystemTip: @req.l10n(@DETELE_SYSTEM_WECARD),
      strCancle: @req.l10n('Cancel'),
      strConfirm: @req.l10n('Confirm'),
      isAdmin: @req.isAllowed('newsAdmin'),
      no_permission: @req.l10n(@NO_PERMISSION),
      newFrameStr: @req.l10n('Add New Frame')} }, ->
        window.gurl =  vars.gurl
        null
    js '/js/lz-string.min.js'
    js '/js/wepageEdit.min.js'

