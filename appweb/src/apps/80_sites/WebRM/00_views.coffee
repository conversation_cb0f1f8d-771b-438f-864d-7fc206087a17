VIEW 'realtor', ->
  u = @realtor
  url = '/1.5/wesite/' + u._id + '?inFrame=1&isWeb=1'
  div class: 'listing-agent', ->
    a class:'listing-agent-avtContainer',href:url,target:'_blank',->
      img class: 'listing-agent-avt', src:u.avt
    listingAgentClass = 'listing-agent-info ' + u.canSelect
    div class: listingAgentClass,->
      span class: 'listing-agent-name',-> text u.nm
      if u.cert
        span class: 'listing-agent-label cert',-> text _ 'CERT'
      if u.reco
        span class: 'listing-agent-label reco',-> text _ 'RECO'
      div class: 'listing-agent-pst',-> text u.cpny_pstn
      div class: 'listing-agent-cpny',-> text u.cpny
      a class:'listing-agent-website',href:url,target:'_blank',->
        span class:'inline-block',-> _ 'View Profile'
        span class:'fa fa-angle-right inline-block'
    a class:"prop-button red inline-block listing-agent-btn",href:"mailto:#{u.eml}?subject=#{@mailSubject}&body=#{@mailBody}", -> _ 'Email'
    a class:"prop-button red inline-block listing-agent-btn",href:"tel:#{u.mbl}", -> _ 'Call'

VIEW 'requestForm', ->
  renderInput = (title,id,value,require)->
    div class: 'search-field',->
      if require
        span class: 'require', -> text '*'
      input class: 'search-field-input', value:value, id: id,placeholder:_(title, 'signUpForm'),name:id
  formClassStyle = 'detail-section box-shadow border-radius-all request-form '
  if @fullAccess is false
    formClassStyle += 'blur'
  section class: formClassStyle, ->
    {action,formInfo,agentInfo,mailBody,mailSubject} = @contactRealtorData
    formPlaceHolder = formInfo?.formPlaceHolder or ''
    if formPlaceHolder
      formPlaceHolder = _(formPlaceHolder)
      if formInfo?.formPlaceHolderLocation
        formPlaceHolder += " #{formInfo.formPlaceHolderLocation}"
    if action
      showForm = 'form' in action?.UIType
      showForm = false if formInfo?.noForm
      showAgent = 'agentCard' in action?.UIType
      showMLSPanel = 'mlsAgentPanel' in action?.UIType
    isRMListing = @prop.src is 'RM'
    if isRMListing
      showForm = true
    # if (isRMListing) or @user
    title = _(action?.title) or 'Contact Me'
    h4 class: 'detail-section-title', -> text title
    if agentInfo
      text ckup 'realtor', {realtor:agentInfo,mailBody,mailSubject}
    if @topU and Array.isArray(@topU)
      selectRealtor = true
      for r in @topU
        r.canSelect = 'canSelect'
        r.selected = true if selectRealtor
        selectRealtor = false
        text ckup 'realtor',{realtor:r,webRmInfoDisp:true}
    input type: 'hidden', id: 'sid', value: @prop._id
    imageUrl = if @prop.pho and @prop.picUrls then @prop.picUrls[0] else ''
    input type: 'hidden', id: 'img', value: imageUrl
    if formInfo?.msg
      div class:'detail-section-msg',-> text _(formInfo.msg)
    if formInfo?.submitLink
      a class: 'prop-button red submitLink', href:formInfo.submitLink,target:'_blank', ->
        span class: 'submit', -> text _(formInfo.submitTxt)
    if showForm
      showWxLine = (@req.locale() in ['zh-cn','zh'])
      div class:'detail-section-form',->
        div id:'firstLastNameContainer',class:'search-field',->
          div class:'search-field-container',->
            span class:'require',-> text '*'
            input class: 'search-field-input', value:formInfo?.fn,id:'fn',name:'fn',placeholder:_('First Name')
          div class:'search-field-container',->
            span class:'require',-> text '*'
            input class: 'search-field-input', value:formInfo?.ln,id:'ln',name:'ln',placeholder:_('Last Name')
        renderInput 'Email','eml',@user.eml,true
        renderInput 'Mobile','mbl',@user.mbl,true
        if showWxLine
          div id:'wxLineContainer', class: 'search-field',->
            if @req.isChinaIP()
              span class: 'require', -> text '*'
            input class: 'search-field-input', value:@user.wxid, id:'wechat',name:'wechat',placeholder:'ID'
            input class: 'search-field-input', value:'', id:'line',name:'line',placeholder:'ID'
            span class:'line'
            span class: 'fa fa-angle-down'
            select class:'wxLineMode',->
              option value:'wechat', selected:true,-> text _('WeChat', 'signUpForm')
              option value:'line',-> text _('Line', 'signUpForm')
        div class: 'search-filed',->
          textarea class: 'textarea-input', id: 'm',placeholder:_('Message','signUpForm'),-> text formPlaceHolder
        a class: 'prop-button red', id: 'send-request', ->
          span class: 'submit', -> text _('Submit','signUpForm')
          span class: 'submiting', -> text _('Submitting','signUpForm')
    if showMLSPanel
      div id:'listingAgents',->
        text '<Brkg-Phone-List src="web" />'
    # unless (isRMListing or @user._id)
    #   a class: 'prop-button red', id: 'open-login-form', -> text _('Login to submit')
    div id: 'request-form-success', ->
      span class:'fa fa-check-circle'
      span -> text _('Your feedback has been submitted.')
    div id: 'request-form-failed', ->
      span class:'fa fa-check-circle'
      span id:'msg', -> text _('Submission Failed! Please check your input and try again or contact us')
  js '/js/web/validator.min.js'
  js '/js/web/requestForm.min.js'
  coffeejs {vars:{
    prop:@prop,
    }},->
      null
  js '/web/packs/listingAgents.js'


VIEW 'propQRcode', ->
  div class: 'qrcode-wrapper box-shadow qrcode-text-wrapper', ->
    div class: 'fa fa-qrcode'
    div class: 'qrcode-text', -> text '点击<br/>扫码'
  div class: 'qrcode-wrapper box-shadow float', ->
    span class: 'fa fa-times qrcode-wrapper-close'
    span class: 'qrcode-title', -> text '手机浏览&分享'
    div id: 'qrcode-img'
    span class: 'qrcode-title',-> text 'APP体验更丰富'
    img src: '/img/app-download.png', alt: 'APP QRcode'
  coffeejs {},->
    window.addEventListener 'load', () ->
      qrcode = new QRCode(document.getElementById('qrcode-img'), {
        width : 100,
        height : 100
      })
      $('.qrcode-wrapper-close').on 'click', ->
        $('.qrcode-wrapper.float').hide('fast')
      $('.qrcode-text-wrapper').on 'click', ->
        if qrcode
          qrcode.clear()
        qrcode.makeCode(decodeURIComponent(window.location.href))
        $('.qrcode-wrapper.float').toggle('fast')
    null
