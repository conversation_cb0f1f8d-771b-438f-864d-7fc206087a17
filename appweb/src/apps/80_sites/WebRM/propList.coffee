i18n = INCLUDE 'lib.i18n'
{strCapitalize,deepCopyObject} = INCLUDE 'lib.helpers'
{getProvFullName} = INCLUDE 'lib.cityHelper'
{
  genListingPicReplaceUrls,
  ptypeArray,
  getPropSearchSort
} = INCLUDE 'libapp.properties'
{ FOR_SALE,
  FOR_RENT,
  SOLD_PRICE,
  OPEN_HOUSE,
  LEASE_PRICE,
  ASSIGNMENT,
  EXCLUSIVE_FOR_SALE,
  EXCLUSIVE_FOR_RENT,
  isSaletpValid
} = INCLUDE 'libapp.propWeb'
mapServer =  INCLUDE 'lib.mapServer'
rateReducer = INCLUDE 'lib.rateReducer'
getConfig = DEF 'getConfig'
keyWords = getConfig('blockUAFields') or 'SM-G900P'
keyWords = keyWords.split(' ')
rateReducer = rateReducer({reduceBy:'UA',keyWords})

domFilterValsShortArr = DEF 'domFilterValsShortArr'

UserModel = MODEL 'User'
ProvAndCity = MODEL 'ProvAndCity'
getPropList = DEF 'getPropList'

FOR_SALE_TITLE_APPEND = '-地产局官方房源|房大师找房|Realmaster.com'
ASSIGNMENT_TITLE_APPEND = 'Assignments | Realmaster.com'
EXLISTING_TITLE_APPEND = 'Exclusive listings | Realmaster.com'

POST '1.5/props/websearch', (req, resp)->
  error = (err)->
    resp.send {ok:0, err:err}
  UserModel.auth {req,resp}, (user) ->
    params = req.body or {}
    params.needLoc = true
    getPropList req, resp, user, params, (err, ret)->
      return error(err) if err
      ret.ok = 1
      ret.ts = req.param('ts')
      if ret.query?.param
        ret.q = ret.query.param
      if ret.query?.readable
        ret.readable = ret.query.readable
      if params.showQuery
        ret.query = ret.query.q
      else
        delete ret.query
      if rateReducer.isBadAccess(req)
        ret.items = rateReducer.reduceResults(ret.items)
      resp.send ret

#/for-sale/West%20Vancouver/view=map.dom=-90.src=mls.prov=BC.ptype=Residential.sort=auto
GET /(for-sale|for-rent|sold-price|open-house|assignment|exclusive-for-(sale|rent)){1}\/.+\/[a-zA-Z\=\.]*/, (req, resp, next)->
  urlPart = req.url.replace(/^\/|\/$/g, '').split('/')
  filters = urlPart.pop()
  city = decodeURIComponent urlPart.pop()
  if /undefined|null/i.test city # handle toronto-undefined
    return next()
  feature = urlPart.pop()

  unless isSaletpValid(feature)
    return next()
  if filters.indexOf('=') is -1
    return next()

  arr = filters?.split('.') or []
  params = {ptypeArray}
  fields = ['cmty','mlsonly','city', 'bdrms', 'bthrms','gr', 'no_mfee', 'prov', 'ptype', 'saletp', 'src', 'ptp', 'pstyl', 'ltp', 'label', 'oh', 'page','sort','search','bbox','soldOnly','dom','hasCenter']
  for obj in arr
    [key,val] = obj.split('=')
    if fields.indexOf(key)>=0
      val = false if val is 'false'
      val = true if val is 'true'
      params[key] = decodeURIComponent val
    if key is'ptype2'
      params.ptype2 = val.split(',')
    if key is 'bbox'
      params.bbox = val.split(',')
    if key is'lang'
      req.setLocale val
    if ['max_lp','min_lp',"max_mfee"].indexOf(key)>=0
      params[key] = Number(val)
    if key is 'view'
      #map or list
      url = val
  params.sort = getPropSearchSort params.sort
  if params.ltp == 'assignment'
    params.mt = {$gt: new Date(Date.now() - 3600000 * 24 * 30)}

  if city isnt 'canada'
    params.city = city

  # handle ptype not in ptypeArray
  params.ptype = strCapitalize params.ptype
  if not (params.ptype in ptypeArray)
    params.ptype = ptypeArray[0]

  saletp = 'Sale'
  switch feature
    when FOR_RENT then saletp = 'Lease'
    when OPEN_HOUSE
      params.oh = 1
      params.src = 'mls'
    when SOLD_PRICE
      params.soldOnly = true
      saletp = 'Sold'
      params.src = 'mls'
    when ASSIGNMENT
      params.src = 'rm'
      params.ltp = 'assignment'
      params.ptype = 'Assignment'
    when EXCLUSIVE_FOR_SALE
      params.src = 'rm'
      params.ltp = 'exlisting'
      params.ptype = 'Exclusive'
    when EXCLUSIVE_FOR_RENT
      saletp = 'Lease'
      params.src = 'rm'
      params.ltp = 'rent'
      params.ptype = 'Exclusive'
      params.marketListPage = true

  params.saletp = saletp

  params.cityTranslate = req.l10n params.city if params.city
  params.saletpTranslate = req.l10n params.saletp if params.saletp
  newDomFilterValsShortArr = deepCopyObject domFilterValsShortArr
  for dom in newDomFilterValsShortArr
    dom.v = req.l10n dom.v
  params.domFilterValsShortArr = newDomFilterValsShortArr
  params.webRmInfoDisp = req.getConfig('webRmInfoDisp')

  UserModel.auth {req,resp},(user)->
    ctx = {params,url,user}
    # saletp = params.saletpTranslate
    # prov = params.provTranslate
    ptype2 = params.ptype2

    # params.currentProv = prov
    prov = params.prov?.toUpperCase() #prov might be undefined string
    params.provid = if ((typeof prov is 'string') and (prov.length is 2)) then prov else 'ON'
    if supportedProvs = req.setting.supportedProvs
      params.provid = supportedProvs[0]
    lang = req.locale()
    if lang is 'en'
      params.currentCity = params.city
      canadaCity = "#{req.l10n 'Canada'} #{if params.currentCity then params.currentCity else ''}"
      if saletp is 'Sale'
        actionType = req.l10n('For Sale')
      else
        actionType = req.l10n('For Rent')
      ptype2 = if ptype2 then req.l10n ptype2 else req.l10n('Homes')
      soldOnly = if params.soldOnly then req.l10n('Sold Price') else ''
      saletp = if params.soldOnly then 'Sold' else saletp
      actionType = if soldOnly then soldOnly else actionType
    else
      params.currentCity = params.cityTranslate
      canadaCity = "#{req.l10n 'Canada'}#{if params.currentCity then params.currentCity else ''}"
      if saletp is 'Sale'
        actionType = req.l10n('Buy House')
      else
        actionType = req.l10n('Rent House')
      ptype2 = if ptype2 then req.l10n ptype2 else req.l10n('For Sale','used')
      soldOnly = if params.soldOnly then req.l10n('Sold Price','prop') else ''

      # No need for SEO title
      # location = ''
      # if params.currentCity
      #   location += "#{params.currentCity},"
      # if prov
      #   location += "#{prov},"
      # if params.ltp == 'assignment'
      #   ctx.title  =  "#{location} #{req.l10n ASSIGNMENT_TITLE_APPEND }"
      # if params.ltp == 'exlisting'
      #   ctx.title = "#{location} #{req.l10n EXLISTING_TITLE_APPEND}"

      ctx.keywords = "#{canadaCity}#{ptype2}，#{canadaCity}#{actionType}，#{canadaCity}#{if soldOnly then soldOnly else '房价'}，#{canadaCity}找房，APP,MLS,CREA,TREB,#{canadaCity}地产局，加拿大地产协会"

    ctx.isCip = req.isChinaIP()
    ctx.author = req.l10n('Realmaster Technology Inc.')
    ctx.lang = lang
    urlbase = "//#{req.host}/www/prop/list/saletp=#{params.saletp}#{if params.src then '.src='+params.src}#{if params.ltp then '.ltp='+params.ltp}#{if params.ptype then '.ptype='+params.ptype}#{if params.city then '.city='+params.city}#{if params.prov then '.prov='+params.prov}#{if params.bdrms then '.bdrms='+params.bdrms}#{if params.bthrms then '.bthrms='+params.bthrms}"
    ctx.url = urlbase + "#{if params.page then '.page='+params.page else ''}"
    ctx.url = url
    ctx.pageTitle = req.l10n 'MLS® Map Search for Canada'
    # NOTE: if is bad access, remove this key
    # console.log '+++++',rateReducer.isBadAccess(req)
    if not rateReducer.isBadAccess(req)
      ctx.mapboxkey = mapServer.getMapboxAPIKey {isApp:false}
    else
      ctx.mapboxkey = 'pk.eyJ1IjoicmVhbG1hd4RlciIsImEiOiJjazV3czQzdjQxeW9kW21tanpjcjQ5bnV6Jn0.M3WqPMfG2cFEOHczsP1U6A'
    # menu = if params.soldOnly then 'sold' else saletp.toLowerCase()
    title = req.l10n 'MLS® Map Search for Canada Real Estate Listings | Realmaster.com'
    description = req.l10n 'Search Canadian MLS® listings by map with Realmaster.com. Find real estate listings and homes for sale and for rent with our MLS® map search for CA listings.'
    resp.ckup 'prop-map',ctx,'layout', {title,description,url, params,noFooter:1,lang,page:'prop'}

VIEW 'prop-map', ->
  if @mapboxkey
    text """<script> key= {mapboxKey: '#{@mapboxkey}'};</script>"""

  js '/js/angular/angular.min.js'
  js '/js/angular/1_3/angular-resource.min.js'
  js '/js/jquery.cookie.min.js'
  h1 style:'display:none',-> @pageTitle
  div id:'result-container',class:'map-view ng-cloak',ngApp:'mapsearchServices',ngController:'mapsearchCtrl',->
    div class: 'back-drop',ngShow:'showBackdrop',ngClick:'backdropOnClick()'
    text ckup 'generalJSFunction', {url:@url,params:@params,user:@user,lang:@lang}
    text ckup 'searchBar', {params:@params,ptpTypes:@ptpTypes}

    div class: 'result-container', ngShow: 'url=="list"', ->
      text ckup 'listingResultFilters', {params: @params}
      div class: 'listing-operation-container',->
        text ckup 'pagination', {}
        text ckup 'sortDropdown', {class:'float-right'}
      div class: 'lds-dual-ring', ngShow: 'loading'
      div class: 'row listing-list', ngShow: '!loading', ->
        div class: 'col-xs-12 col-sm-6 col-md-4 col-lg-3', ngRepeat: 'l in listings',ngClick:'openUrl(l.webUrl)', ->
          text ckup 'propCardMap', {}
      text ckup 'pagination', {}
      text ckup 'noResults', {}

    div id:'id-map-search', class: 'result-container', ngShow: 'url=="map"', ->
      div id:'map-data-container', class: 'row', ->
        text ckup 'listingResultFilters', {params: @params}
        div id:'id_list', class: 'col-sm-6', ->
          div class: 'listing-operation-container',->
            text ckup 'pagination', {}
            text ckup 'sortDropdown', {class:'float-right'}
          div class: 'lds-dual-ring', ngShow: 'loading'
          ul class:'list', ngShow: '!loading', ->
            li ngClick:'select_cluster()',ngShow:'clusterSelected',class:'prop-button',-> _ "Show all"
            li class: 'row listing-list', ->
              div class: 'listing-prop-card col-xs-12', ngClass:'l.cssClass', ngRepeat:'l in listings',ngClick:'openUrl(l.webUrl)',ngMouseover:'select(l._id,true)',ngHide:'l.hide', ->
                text ckup 'propCardMap', {}
            li ngClick:'select_cluster()',ngShow:'clusterSelected',class:'prop-button',->  _ "Show all"
          text ckup 'pagination', {}
          text ckup 'noResults', {}
        div id: 'map-view-container', class: 'col-xs-12 col-sm-6', ->
          div class: 'lds-dual-ring', ngShow: 'loading'
          div id: 'map-toggle', class: 'fa fa-angle-double-left'

          div id: 'map-result-msg', class:'box-shadow', -> '{{mapResultMsg()}}'
          div class: 'map-controls', ->
            div class: 'map-control fa', ngClass: 'mapType=="roadmap"?"fa-globe":"fa-map-o"', ngClick: 'updateMapType()'
            div class: 'map-control', ngClick: 'updateMapZoom("+")', -> text '+'
            div class: 'map-control', ngClick: 'updateMapZoom("-")', -> text '-'
          div id: 'map-container'
      div class: 'listing-prop-selected', ngClass: 'selected.length > 1?"full":""', ngShow: 'selected.length > 0', ->
        div class: 'listing-prop-selected-redbar', ->
          span -> text '{{selected[0].addr}}'
          span class: 'fa fa-times', ngClick: 'closeSelected()'
        a ngRepeat: 's in selected', class: 'row listing-prop box-shadow', href:'{{s.webUrl}}', target:'_blank', ->
          div class: 'col-xs-5 no-padding', ->
            img ngClass: 's.blur', ngSrc: '{{thumbnail(s)}}',onerror: "this.src=errImage", class: 'img-responsive'
            div ngClass: 's.blur', class:'listing-prop-selected-sid',-> text '{{s.sid}}'
          div class: 'col-xs-7', ->
            div class: 'listing-prop-selected-detail', ->
              div ngShow: '!s.isProj', ->
                span class: 'listing-prop-price',->
                  text '{{s.priceValStrRed || s.askingPriceStr}}'
                  if s.askingPriceStr
                    span class: 'detail-lp', -> text "#{s.askingPriceStr} #{s.askingPriceStrDesc}"
                div -> text '{{s.ptype2?s.ptype2.join(" "):""}}'
                div -> text '{{s.rmbdrm || s.tbdrms}} ' + _('Bedrm') + ' {{s.rmbthrm || s.bthrms}} ' + _('Bath') + ' {{s.rmgr}} ' + _('Garage')
                div ngShow: 's.dom', -> text _('DOM') + ':{{s.dom}}'
                div ngShow: 's.tax', -> text _('Tax') + ': {{s.tax}}/{{s.taxyr}}'


  js '/web/packs/qrcode.min.js'
  js '/web/packs/commons.js'
  text ckup 'mapboxJs'
  js '/js/map/mapbox.min.js'
  js '/js/propListWeb.min.js'

VIEW 'noResults', ->
  div class: 'listing-no-result', ngShow: 'listings.length == 0 && !loading', ->
    span class: 'fa fa-search'
    h5 class: 'title', -> text _'No result found'
    p class: 'subtitle', -> text _'Try searching for something else'

VIEW 'propCardMap',->
  div class: 'listing-prop box-shadow border-radius-all',->
    div class:'listing-prop-img-container',->
      div class: 'listing-prop-labels',->
        div class: 'listing-prop-label', ngIf: 'filters.src == "rm"',->
          span ngIf: 'l.ltp == "assignment"',-> text _ 'Assignment'
          span ngIf: 'l.ltp == "exlisting"',-> text _ 'Exclusive'
          span ngIf: '(l.ltp == "rent") && (!l.cmstn)',-> text _ 'Landlord Rental'
          span ngIf: '(l.ltp == "rent") && (l.cmstn)',-> text _ 'Exclusive Rental'
      div ngIf: 'l.isToplisting', class: 'listing-prop-img-status toplisting', -> text _ 'TOP'
      # div ngIf: 'thumbnail(l) && !l.blur', class: 'listing-prop-img-status', -> text '{{l.curImgNum}}/{{l.pho}}'
      div ngShow:'l.blur',class:'listing-prop-require-login',->
        i class:'fa fa-lock'
        div -> text _('Login To View More')
      img ngIf: 'thumbnail(l)', ngClass: 'l.blur', class: 'listing-prop-img border-radius-top', ngSrc:'{{l.curImg}}',onerror: "this.src=errImage" #,ngMousemove:'(user || l.hasOwnProperty("login") == false) && showPic(l,$event)'
      div ngIf: '!thumbnail(l)', ngClass: 'l.blur', class: 'listing-prop-no-img', ->
        div class: 'listing-prop-no-img-container', ->
          span class: 'listing-prop-no-img-icon fa fa-home'
          span class: '', -> 'No Image'
    div class: 'listing-prop-detail', ->
      div ngIf:'webRmInfoDisp && l.adrltr',class:'listing-prop-adrltr',ngClass:'l.blur',ngClick:'openRealtor(l.adrltr)',->
        img ngSrc:'{{l.adrltr.avt}}',class:'listing-prop-adrltr-avt'
        div ngIf:'lang == "en"',class:'listing-prop-adrltr-nm',-> text '{{l.adrltr.nm_en || l.adrltr.nm || l.adrltr.nm_zh}}'
        div ngIf:'lang != "en"',class:'listing-prop-adrltr-nm',-> text '{{l.adrltr.nm_zh || l.adrltr.nm_en || l.adrltr.nm}}'
      h3 ngClass: 'l.blur', class: 'listing-prop-price',->
        span ngIf: 'l.priceValStrRed || l.askingPriceStr',->
          # span ngIf: 'l.showSoldPrice && l.sp && l.status_en == "A"', -> text '{{l.sp}}'
          span ngClass: '', -> text '{{l.priceValStrRed || l.askingPriceStr}}'
          # span ngIf: 'l.lp == 0 || l.lpr == 0', -> text _('To Be Negotiated')
          span ngIf: 'l.status_en == "A"', -> text ' {{translates[l.saletp_en[0]]}}'
          span class:'inactive', ngIf: 'l.status_en != "A"', ->
            span ngIf: 'l.lst == "Sld" || l.lst == "Lsd" || l.lst == "Cld" || l.lst == "Pnd"', ->
              span ngIf: 'isSale(l)', -> text ' '+_('Sold')
              span ngIf: '!isSale(l)', -> text ' '+_('Leased')
            span ngIf: 'l.lst != "Sld" && l.lst != "Lsd" && l.lst != "Cld" && l.lst != "Pnd"', -> text _('Inactive')
          span class: 'detail-lp',ngIf: 'l.askingPriceStr',style:'display: block;padding: 0;', ->
            text '{{l.askingPriceStr}} {{l.askingPriceStrDesc}}'

      span ngIf:'!webRmInfoDisp || !l.adrltr', ngClass: 'l.blur', class:'listing-prop-id',-> text 'MLS® # ' + '{{l.sid?l.sid:l.id}}'
      span ngClass: 'l.blur', class: 'listing-prop-address',-> text '{{l.address}}'
      span ngClass: 'l.blur', class: 'listing-prop-address',-> text _('Brokerage') + ': {{l.rltr}}'
      p class:'listing-prop-rooms',ngHide:'modeIsB',->
        span class: 'listing-prop-room', ->
          span class: 'fa fa-rmbed'
          span -> text '{{l.rmbdrm || l.all_bdrms || 0}}'
        span class: 'listing-prop-room', ->
          span class: 'fa fa-rmbath'
          span -> text '{{l.rmbthrm || l.tbthrms || l.bthrms || 0}}'
        span class: 'listing-prop-room', ->
          span class: 'fa fa-rmcar'
          span -> text '{{l.rmgr || l.tgr || l.gr || 0}}'
      # a target: '_blank', class:'listing-prop-link',href:'{{l.webUrl}}',->
      span class:'listing-prop-link', todo:'debug',->
        span -> _ "Detail"
        span class: 'fa fa-angle-right'

VIEW 'searchBar', ->
  section class: 'search-bar box-shadow', ->
    div id: 'filter-section-container', ->
      a class: 'search-by-city filter-button-container prop-button', ngClick: 'closeSelected()',->
        span class: 'fa fa-map-marker search-button-icon'
        span class: 'search-button-label', -> text '{{filters.city ? translates["currentCity"] : translates["City"]}}'
      text ckup 'propertySaleSelector', {ptypeArray:@params.ptypeArray,domFilterValsShortArr:@params.domFilterValsShortArr}

      div class: 'filter-button-container mobile-hide', ->
        a ngShow:'showStyle2()', class: 'search-by-style2 prop-button', ngClick: 'openPopup("ptype2")', ->
          span class: 'search-button-label', ->
            span class: 'search-by-style2-name', -> text _ 'Style'
            span class: 'search-by-style2-number', -> text '({{filters.ptype2?filters.ptype2.length:0}})'
        text ckup 'propertyStyle2PopUp', {}

        a class: 'search-by-price prop-button', ngClick: 'openPopup("price")', ->
          span class: 'search-button-label', -> text _ 'Price'
        div class: 'popup-price-filter popup-filter', ->
          div ngClass: 'price.showMinLp ? "col-xs-6" : "col-xs-6 invisible"', ->
            div class: 'popup-price-label', -> text _ 'Min'
            div class: 'style-selector', ngRepeat: 'p in min_lp', ngClick: 'select_filter(p,"min_lp")', -> text '${{numberReduce(p)}}'
          div class: 'col-xs-6', ngShow: 'price.showMaxLp', ->
            div class: 'popup-price-label', -> text _ 'Max'
            div class: 'style-selector', ngRepeat: 'p in max_lp', ngClick: 'select_filter(p,"max_lp")', -> text '${{numberReduce(p)}}'

        a class: 'search-by-rooms prop-button', ngClick: 'openPopup("room")', ->
          span class: 'search-button-label', -> text _ 'Room'
        div class: 'popup-room-filter popup-filter', ->
          text ckup 'roomSelector', {id: 'bdrms', title: 'Bedrooms', params: @params}
          text ckup 'roomSelector', {id: 'bthrms', title: 'Washrooms', params: @params}
          text ckup 'roomSelector', {id: 'gr', title: 'Garage', params: @params}

      a class: 'search-filter filter-button-container prop-button', ngClick: 'closeSelected()', ->
        span class: 'fa fa-filter search-button-icon icon'
        span class: 'search-button-label', -> text _ "More"
    div id: 'listing-options', ->
      a ngIf: 'url == "list"', id: 'button-map-view', class: 'red prop-button', ngClick: 'changeView("map")', ->
        span class:'icon fa fa-map-marker'
        span class:'text', -> text _ 'Map'
      a ngIf: 'url == "map"', id: 'button-list-view', class: 'red prop-button', ngClick: 'changeView("list")', ->
        span class:'icon fa fa-list'
        span class:'text', -> text _ 'List'
  text ckup 'searchCityFilter', {params: @params}
  text ckup 'searchPropertyFilter', {params: @params}

VIEW 'searchCityFilter', ->
  section class: 'search-options city-list', ->
    span class: 'fa fa-times search-options-close'
    div class: 'container', ->
      div class: 'city-item prop-button current-city', ngShow: 'filters.city', ->
        span class: 'fa fa-map-marker'
        span class: '', -> text _('Current City') + ': '
        span class: 'city-name', -> text '{{translates["currentCity"]}}'
      div class: 'province-section', ->
        h4 class: 'city-title', -> text _ 'Select Province to see All Cities'
        input type: 'hidden', value: @params.prov, id: 'prov-name'
        div class: 'province-select-container', ->
          select class: 'select', id: 'province-dropdown', ngModel: 'filters.prov', ngOptions: 'p.o_ab as p.n for p in provs', ngChange: 'getCities(filters.prov)', ->
      div class: 'city-section', ->
        h4 class: 'city-title', -> text _ 'Popular Cities'
        div class: 'city-item-container', ->
          a class: 'city-item prop-button', ngClick: 'select_filter(c, "city")', ngRepeat: 'c in cities.fc', -> text '{{c.n}}'
      div class: 'city-section all-city-list', ->
        div ngRepeat: '(t,cl) in cities.cl', ->
          h4 class: 'city-title', -> text '{{t}}'
          a class: 'city-item prop-button', ngClick: 'select_filter(c, "city")', ngRepeat: 'c in cl', -> text '{{c.n}}'

VIEW 'searchPropertyFilter', ->
  section class: 'search-options filter-list', ->
    span class: 'fa fa-times search-options-close'
    div class: 'container', ->
      div class: 'search-field', ->
        label class: 'search-field-label', -> text _ 'Property Type'
        select class: 'search-field-input',
        id: 'ptype',
        ngModel: 'filters.ptype',
        ngChange: 'select_filter(filters.ptype,"ptype",false)',->
          for p in @params.ptypeArray
            selected = if @params.ptype is p then 'selected' else ''
            option value: p, selected, -> _ p
      div ngShow:'getSaletpFromPtype(filters.ptype).length > 0', class: 'search-field', ->
        label class: 'search-field-label', -> text _ 'Sale/Lease'
        select class: 'search-field-input',
        id: 'saletp',
        ngModel:'filters.saletp',
        ngOptions:'translates[s] for s in getSaletpFromPtype(filters.ptype)', ->
      div ngShow:'displayDomOption()', class: 'search-field',->
        label class: 'search-field-label',-> text _ 'Days off market'
        select class: 'search-field-input', ngModel:'filters.dom',->
          for domFilter in @params.domFilterValsShortArr
            selected = if @params.dom is domFilter.k then 'selected' else ''
            option value: domFilter.k,selected, -> text domFilter.v
      div class: 'search-field', id: 'ptype2',->
        label class: 'search-field-label', -> text _ 'All Types'
        input class: 'search-field-input', value: '{{getPtype2Translation()}}'
        text ckup 'propertyStyle2PopUp', {noInstantSearch:true}
      div class: 'search-field', ->
        label class: 'search-field-label', -> text _('min price')
        input class: 'search-field-input', id: 'min_lp', ngModel: 'filters.min_lp'
      div class: 'search-field', ->
        label class: 'search-field-label', -> text _('max price')
        input class: 'search-field-input', id: 'max_lp', ngModel: 'filters.max_lp'
      div class: 'search-field', ->
        text ckup 'roomSelector', {id: 'bdrms', title: 'Bedrooms', params: @params,noInstantSearch:true}
      div class: 'search-field', ->
        text ckup 'roomSelector', {id: 'bthrms', title: 'Washrooms', params: @params,noInstantSearch:true}
      div class: 'search-field', ->
        text ckup 'roomSelector', {id: 'gr', title: 'Garage', params: @params,noInstantSearch:true}
    div class: 'container', ->
      div class: 'search-field', ngIf:'showOpenhouseSwtich()', ->
        label class: 'search-field-label', -> text _ 'Open House'
        div class: 'prop-field-switch',
        ngClass: 'filters.oh?"checked":""',
        ngClick: 'select_filter("","oh",false)', ->
          span class: 'prop-field-switch-circle'
      div class: 'search-field', ->
        label class: 'search-field-label', -> text _ 'No Maint Fee'
        div class: 'prop-field-switch',
        ngClass: 'filters.no_mfee?"checked":""',
        ngClick: 'select_filter("","no_mfee",false)', ->
          span class: 'prop-field-switch-circle'
      div class: 'search-field', ngShow: '!filters.no_mfee', ->
        label class: 'search-field-label', -> text _ 'Max Maint Fee'
        input class: 'search-field-input', id: 'max_mfee', ngModel: 'filters.max_mfee'

    div class: 'container search-buttons', ->
      a class: 'red prop-button search-button',
      ngClick: 'select_filter(1, "mls_only")', -> text _ 'Search'
      # a class: 'red prop-button', -> text _ "Search & Save"
      a class: 'prop-button',
      id: 'reset-button',
      ngClick: 'select_filter("", "reset")', -> text _ 'Reset'
  # coffeejs -----
  coffeejs {}, ->
    $(document).ready ->
      $('#ptype2').on 'click', ->
        $(@).find('.property-style2-selector').show()


    null
VIEW 'roomSelector', ->
  label class: 'search-field-label', -> text _ @title
  select class: 'search-field-input', ngModel: 'filters.' + @id, id: @id, ngChange: 'select_filter(filters.'+@id+',"'+@id+'"'+(if @noInstantSearch then ',false' else '')+')', ->
    option value:'',-> _ @title
    for n in [1..5]
      room = if @params[@id] is "#{n}" then 'selected' else ''
      roomPlus = if @params[@id] is "#{n}+" then 'selected' else ''
      option value:n, room,-> text n
      option value:"#{n}+", roomPlus,-> text "#{n}+"

VIEW 'generalJSFunction', ->
  coffeejs {vars:{
    mapboxkey:@mapboxkey,
    user:(if @user then @user.locale else null),
    url:@url,
    lang:@lang,
    webRmInfoDisp:@params.webRmInfoDisp,
    domFilterValsShortArr:@params.domFilterValsShortArr
    filters:{
      cmty:@params.cmty or false,
      hasCenter:(if @params.hasCenter then true else false),
      soldOnly:(if @params.soldOnly then true else false),
      oh:(if @params.oh then true else false),
      src:(@params.src or 'mls'),
      ltp:(@params.ltp or ''),
      bbox:(@params.bbox or []),
      search:@params.search,
      max_mfee:@params.max_mfee,
      no_mfee:@params.no_mfee,
      city:@params.city,
      prov:@params.provid,
      ptype:@params.ptype,
      ptype2:(@params.ptype2 or []),
      saletp:@params.saletp,
      bthrms:@params.bthrms,
      bdrms:@params.bdrms,
      gr:@params.gr,
      min_lp:@params.min_lp,
      max_lp:@params.max_lp,
      sort:(@params.sort or 'auto-ts'),
      page:@params.page or 0,
      dom:@params.dom or -90,
      marketListPage:@params.marketListPage
    },
    sorts:{
      title:_('Sort'),
      values:[
        {value:'auto-ts',title:_('Sort')},
        {value:'ts-desc',title:_('New to Old','date')},
        {value:'ts-asc',title:_('Old to New','date')},
        {value:'lp-desc',title:_('High to Low','price')},
        {value:'lp-asc',title:_('Low to High','price')},
        {value:'mt-desc',title:_('Recently Updated First')},
        {value:'mt-dasc',title:_('Recently Updated Last')}
      ]
    },
    translates:{
      brokerage:_('Brokerage'),
      top:_('TOP'),
      oh:_('Open House Only'),
      soldprice:_('Sold Price','prop'),
      results:_('Results'),
      buyhouse:_('Buy House'),
      renthouse:_('Rent House'),
      sold:_('Sold'),
      leased:_('Leased'),
      Sold:_('Sold'),
      Leased:_('Leased'),
      canada:_('Canada'),
      detail:_('Detail'),
      currentCity:@params.currentCity,
      # currentProv:@params.currentProv,
      City:_('City'),
      saletp:_('Sale/Lease'),
      ptype:_('Property Type'),
      style:_('style'),
      Residential:_('Residential'),
      Assignment:_('Assignment'),
      Exclusive:_('Exclusive'),
      Commercial:_('Commercial'),
      Other:_('Other'),
      Sale:_('Sale'),
      Lease:_('Lease'),
      sale:_('Sale'),
      lease:_('Lease'),
      Bedroom:_('Bedroom'),
      Bathroom:_('Bathroom'),
      Garage:_('Garage'),
      Min:_('Min'),
      Max:_('Max'),
      negotiated:_('To Be Negotiated')
    }
    }}, ->
      null

VIEW 'propertySaleSelector', ->
  div class: 'filter-button-container mobile-hide', ->
    a class: 'search-by-ptype prop-button', ngClick: 'openPopup("ptype")',->
      span class: 'search-button-label', -> text '{{translates[filters.ptype]}}'
    div class: 'property-style-selector popup-filter', ->
      for p in @ptypeArray
        div ngClick: 'select_filter("'+p+'","ptype")',ngClass: 'highLighPtype("'+p+'")', ->
          text _ p
    a ngShow: 'getSaletpFromPtype(filters.ptype).length > 0', class: 'search-by-saletp prop-button', ngClick: 'openPopup("saletp")', ->
      span ngClass: 'filters.saletp', class: 'search-button-circle'
      span class: 'search-button-label', -> text '{{displaySaletp()}}'
    div class: 'property-saletp-selector popup-filter', ->
      div ngRepeat:'s in getSaletpFromPtype(filters.ptype)', ngClick: 'select_filter(s,"saletp")', ngClass: 'filters.saletp == s ? "style-selector selected" : "style-selector"', ->
        text '{{translates[s]}}'
    a ngShow:'displayDomOption()', class:'prop-button', ngClass: 'filters.soldOnly?"selected":""', ngClick:'openPopup("soldOnly")', ->
      span -> text '{{displayDom()}}'
    div class: 'popup-filter', ->
      for d in @domFilterValsShortArr
        domClass = 'filters.dom=="'+d.k+'" ? "style-selector selected" : "style-selector"'
        div ngClick:'select_filter("'+d.k+'", "dom")', ngClass: domClass, -> text d.v

VIEW 'propertyStyle2PopUp', ->
  div class: 'property-style2-selector popup-filter', ->
    div class: 'style-selector', ngRepeat: 'p in ptype2', ngClick: 'select_filter(p.k,"ptype2"'+(if @noInstantSearch then ',false' else '')+')', ->
      span -> text '{{p.v}}'
      input class: 'property-style-checkbox', type: 'checkbox', ngChecked: 'filters.ptype2.indexOf(p.k) > -1'



VIEW 'listingResultFilters', ->
  div class: 'listing-result-filters-container',->
    div class: 'listing-result-filters map', ngShow: 'filters.city || filters.ptype || filters.saletp || filters.bdrms || filters.bthrms || filters.gr || filters.min_lp || filters.max_lp', ->
      showFilter = (filterText, key)->
        span class: 'listing-result-filter', ngShow: "filters.#{key}", ->
          span -> text filterText
          if key not in ['saletp','ptype']
            a class: 'fa fa-times', ngClick: 'removeFilter("'+key+'")'
      showFilter '{{translates[filters.ptype]}}', 'ptype'
      showFilter '{{translates[filters.saletp]}}', 'saletp'
      showFilter '{{translates["currentCity"]}} {{filters.cmty?filters.cmty:""}}, {{filters.prov}}', 'city'
      # showFilter '{{filters.cmty}}', 'cmty'
      showFilter '{{getPtype2Translation()}}', 'ptype2'
      showFilter '{{translates["oh"]}}', 'oh'
      showFilter '{{filters.bdrms}}{{translates["Bedroom"]}}', 'bdrms'
      showFilter '{{filters.bthrms}}{{translates["Bathroom"]}}', 'bthrms'
      showFilter '{{filters.gr}}{{translates["Garage"]}}', 'gr'
      showFilter '{{translates["Min"]}}: {{numberReduce(filters.min_lp)}}', 'min_lp'
      showFilter '{{translates["Max"]}}: {{numberReduce(filters.max_lp)}}', 'max_lp'

VIEW 'pagination', ->
  div class: 'listing-pagination', ngShow: 'listings.length > 0', ->
    button ngClick: 'select_filter("previous","page")', ngDisabled: 'filters.page <= 0', class: 'listing-pagination-link border-radius-all', ->
      span class: 'fa-caret-left fa'
    button ngDisabled: 'listings.length < 20', ngClick: 'select_filter("next","page")', class: 'listing-pagination-link border-radius-all', ->
      span class: 'fa fa-caret-right'
VIEW 'sortDropdown', ->
  showString = 'listings.length > 0'
  if @hideOnMap
    showString += ' && url == "list"'
  select ngIf: showString, class: 'select' + (if @class then " #{@class}" else ''), id: 'sort', ngModel: 'filters.sort', ngChange: 'select_filter(filters.sort, "sort")', ->
    option ngRepeat: 's in sorts.values', ngSelected: '{{filters.sort == s.value}}', value: '{{s.value}}', -> text '{{s.title}}'
