<!DOCTYPE html>
<html lang="{{=it.lang}}">
  <head>
    <meta charset="utf-8">
    <meta name="X-UA-Compatible" content="IE=edge">
    <meta name="HandheldFriendly" content="true">
    <meta name="robots" content="noodp">
    <meta name="viewport" content="width=device-width,height=device-height,initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="RealMaster.com">
    <meta property="og:title" content="RealMaster">
    <meta property="og:image" content="/img/logo_s.png">
    <meta property="twitter:site" content="RealMaster.com">
    <meta property="twitter:title" content="RealMaster">
    <meta property="twitter:creator" content="RealMaster">
    <title>RealMaster</title>
    {{?it.appjs}}
    <script src="{{=it.appjs}}"></script>
    {{?}}
    <script type="text/javascript" async="" src="https://www.google-analytics.com/analytics.js"></script>
    <link rel="stylesheet" type="text/css" href="/css/page.min.css">
    <link rel="stylesheet" type="text/css" href="/css/apps/drawers.css">
    <link rel="stylesheet" type="text/css" href="/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css" href="/css/apps/apphome.css">
    <script src="/js/swiper.min.js"></script>
    <script type="text/javascript">var gShareHostNameCn = '{{=req.shareHostNameCn()}}';</script>
    <script src="/js/rmapp.min.js"></script>
    {{? it.isChinaIP}}
      <script src="https://s.realmaster.cn/logger.lib.min.js"></script>
    {{??}}
      <script src="https://s.realmaster.com/logger.lib.min.js"></script>
    {{?}}
    <style type="text/css">
    .sprite {
      background-image: url('/img/sprite.png');
      background-repeat: no-repeat;
    }
    .swiper-container {
      position: relative;
      overflow: hidden;
      list-style: none;
      padding: 0;
      z-index: 1;
    }
    .swiper-wrapper {
      position: relative;
      width: 100%;
      height: 100%;
      z-index: 1;
      display: flex;
      transition-property: transform;
      box-sizing: content-box;
    }
    .swiper-container-android .swiper-slide, .swiper-wrapper {
      transform: translate3d(0px,0,0);
    }
    .swiper-slide {
      flex-shrink: 0;
      width: 100%;
      height: 100%;
      position: relative;
      transition-property: transform;
    }
    #autocompleteWrapper {
      z-index:1;
    }
    </style>
</head>
<body>
  <div style="display:none" id="rmPageBarColor">:name:mainTheme</div>
  <!-- <div style="display:none" id="rmPageBarColor">#e03131</div> -->
  {{* 'components/loader' }}
  <div class="page-container">
    {{? it.appUpgrade && it.appUpgrade.showAlert }}
    {{* 'home/appUpgrade' it }}
    {{?}}
    {{* 'bottomNav' it}}
    <div id='index-header'>
      <header class="bar bar-nav mls" >
      <a v-cloak href="javascript:;" @click="getCityList('setUserCity')" data-sub="city" v-show="!focused" class="pull-left city">
        <span class="cityName">{{dispVar.userCity.n || dispVar.userCity.o}}
        </span>
        <span class="icon icon-down-nav"></span>
      </a>
      <div class="nativeSearchEntry" data-sub="search" v-show="isNativeSearch" @click="onClickSearchBar"></div>
      <input v-cloak v-model="searchStr" data-sub="search"  @focus="focused=true" :placeholder="$_('Input Address/Listing ID')" :class="{focused:focused}" class="search"/>
      <!-- <a v-cloak id="searchBtn" @click="onClickSearchBar" class="fa fa-rmsearch"></a> -->
      <a v-cloak id="scanBtn" data-sub="camera" @click="openCamera()" class="fa fa-scan"></a>
      <a v-cloak href="javascript:void 0" data-sub="map" @click="goTo({url:'{{=it.mapUrl}}',googleCat:'homeTopBar',googleAction:'openNearby'});" class="chat pull-right"><img src="/img/icon_index_nearby_trans.png"></img><span>{{-, MAP,indexSearch}}</span></a>
      </header>
      <city-select-modal :need-loc='false' :cur-city.sync="dispVar.userCity" :lang="dispVar.lang" :showSubscribe=true></city-select-modal>
      <lang-select-cover :disp-var="dispVar" :lang="dispVar.lang" :old-ver="dispVar.langCoverOldVer"></lang-select-cover>
      <flash-message/>
      <div style="display:none">
        <span v-for="(v,k) of strings">{{ $_(v.key)}}</span>
      </div>
    </div>
    <div class="indexMainFrame mls" id='discover'>
      <div class="content" id="content">
        <div class="content-list">
          {{? it.banners && it.banners.length > 0}}
            <div class="banner">
              {{* 'home/banners' it}}
            </div>
          {{?}}
          <div class="head">
            {{* 'home/drawers' it}}
          </div>
            {{? it.truAssign && it.truAssign.length > 0}}
            {{* 'home/trustedAssignment' it}}
            {{?}}
            {{* 'home/homeSwitch' it}}
            {{? it.indexAd && it.indexAd.length > 0}}
            {{* 'home/projects' it}}
            {{?}}
            {{* 'home/discoverCityProps' it}}
          </div>
        </div>
        <div id="new-feed" :class='{active:hasNewFeed}' data-sub="new feeds" @click="selectTag({k:'feed',from:'alert'})">
          <span class="fa fa-rmalert active"></span>
          <span class="txt">{{- New feeds found}}</span>
        </div>
        <div class="mask" :class="{show:showMask}" @click="clickMask()"></div>
        <match-list-popup :log='currentEdmLogProp'></match-list-popup>
        <watching-popup :watching-type="watchingType" :data-from="'cmty'" :disp-var='dispVar'></watching-popup>
      </div>
    </div>
    <div id="obj" style='display:none'>mls</div>
    <div id="user-roles" style='display:none'>{{= it.userRoles}}</div>
  </div>
  {{?it.osScript}}
  <script src="{{=it.osScript}}"></script>
  {{?}}
  
  <script>
    var TRANSLATES = {
      /* discover translation*/
      noFound: "{{- No results found}}",
      today: "{{- Today}}",
      sale: "{{- sale}}",
      sold: "{{- sold}}",
      seeAllSale: "{{- see all sale}}",
      seeAllSold: "{{- see all sold}}",
      negotiated:"{{- To Be Negotiated}}",
      oh: "{{- Open House}}",
      saved: "{{-, saved,favorite}}",
      savedHomeUpdates: "{{- Updates of saved and watched}}",
      'Avg. Price': "{{- Avg. Price}}",
      MoM: "{{- MoM}}",
      YoY: "{{- YoY}}",
      Trends:"{{- Trends}}",
    };
  </script>
  <script src='/js/vue3.min.js'></script>
  <script src="/js/stdfn.min.js"></script>
  <script src='/js/axios.min.js'></script>
  <script src='/js/home/<USER>'></script>
  <script type="text/javascript">
    loadLazyImg();
    if(window.gtmjs){
      gtmjs({tags:{js:new Date()}})
    }
  </script>
</body>
</html>