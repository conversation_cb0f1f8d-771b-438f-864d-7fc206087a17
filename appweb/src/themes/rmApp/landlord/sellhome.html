{{&
  var d = '/1.5/landlord/sellhome';
}}
  <div class="description-card">
    <div class="title-line">
      <div>
        <div class="title-1">{{-, See how much you could sell for,evaluation}}</div>
      </div>
      <div><span class="btn btn-green" @click="openEvaluatePage()">{{-, Estimate,evaluation}}</span></div>
    </div>
    <div class="type"><span class="fa fa-check-circle"><span>{{-, MLS,evaluation}}</span></span><span
        class="fa fa-check-circle"><span>{{-, Exclusive,evaluation}}</span></span><span
        class="fa fa-check-circle"><span>{{-, Assignment,evaluation}}</span></span></div>
    <div class="warn">{{-, This estimate is to give you a rough estimation of the home value or rent of the subject
      property or potential property to be built.,evaluation}}</div>
  </div>
  <div class="block" id="evaluate-hist" v-if="hists.length >= 1">
    <div class="header first-header">{{getTranslate('History')}}</div>
    <div class="sp-holder">
      <div class="sp-wrapper" :style="{width:spWrapperWidth+'px'}">
        <div class="sp" v-for="hist in hists" v-bind:style="{ width: calculatedSpWidth+'px' }">
          <hist-card v-bind:hist='hist'></hist-card>
        </div>
      </div>
    </div>
  </div>
  <div class="stat block">
    <div class="header">{{- Market Trends}}</div>
    <div class="describe"><span class="fa fa-trend"></span>
      <div class="text">
        <div class="text-1">{{-, Understand local market,evaluation}}</div>
        <div class="text-2">{{-, Based on Big Data Analytics,evaluation}}</div>
      </div>
      <div class="links btn" @click="goStat()">{{- See}}</div>
    </div>
  </div>
  <div class="stat block">
    <div class="header">{{- Renovation & Repair}}</div>
    <div class="describe">
      <img src="/img/service.png" alt="customer service">
      <div class="text">
        <div class="text-1">{{- Get free quote}}</div>
        <div class="text-2">{{- Free on-site measure}}</div>
      </div>
      <a class="phone" href="tel:************">
        <span class="fa fa-phone"></span>
      </a>
      <div class="links btn" @click="goToReno()">{{- Detail}}</div>
    </div>
  </div>
  <div class="stat block">
    <div class="header">{{- Listing Promotion}}</div>
  </div>
<div class="promotion-list">
  {{* 'home/realtor/listingPromotion' it}}
</div>

<script type="text/javascript">
  var TRANSLATES = {
    Message: "{{-, Message,evaluation}}",
    Cancel: "{{- Cancel}}",
    Confirm: "{{- Confirm}}",
    afterDelete:"{{-, After deleting it, You can not receive any notification of evaluation changes.,evaluation}}",
    History: "{{-, Estimate History,evaluation}}",
    sqft: "{{-, sqft,prop}}",
    Saved:"{{-, Saved,favorite}}",
    ReEstimate:"{{-, Re-Estimate,evaluation}}",
    Market:"{{- Market Trends}}",
    localMarket:"{{-, Understand local market,evaluation}}",
    Analytics:"{{-, Based on Big Data Analytics,evaluation}}",
    See:"{{- See}}",
    Detail:"{{-, Detail,evaluation}}",
    Conditions:"{{-, Evaluation Conditions,evaluation}}",
  };
</script>