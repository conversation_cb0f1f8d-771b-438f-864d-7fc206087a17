{{&
  var d = '/1.5/landlord/owners';
}}
<div class="items" v-show="hasWechat">
  <div @click='goTo(item.url)' v-for='item in yellowpageList'><img :src=item.img referrerpolicy="same-origin" />
    <div>{{getTranslate(item.name)}}</div>
  </div>
  <div @click="goToReno()"><img src="/img/staging/reno_estimation.png" />
    <div>{{-, Renovation,yellowpage}}</div>
  </div>
</div>

<div class="tip-card agent">
  <div>
    <div class="row">
        <div class="field-name">{{- Looking to sell?}}
            <div class="explain">{{- Sell better and faster}}</div>
        </div>
        <div class="field-action" onClick="goTo('/1.5/landlord/sellhome?d=/1.5/index')"><span>{{- See}}</span></div>
    </div>
  </div>
</div>
  <div class="block">
    <div class="header first-header">
      {{-, Saved Properties,landlord}}
      <span class="pull-right" v-if='hists.length>=1' @click="goTo('/1.5/evaluation?d={{=d}}')">
        {{-, Add,landlord}}
        <span class="icon icon-right-nav"></span>
      </span>
    </div>
    <div class="sp-holder" v-if="hists.length >= 1">
      <div class="sp-wrapper" :style="{width:spWrapperWidth+'px'}">
        <div class="sp" v-for="hist in hists" v-bind:style="{ width: calculatedSpWidth+'px' }">
          <hist-card v-bind:hist='hist'></hist-card>
        </div>
      </div>
    </div>
    <div class="description-card" v-else>
      <div class="title-line">
        <div>
          <div class="title-1">{{-, What is my home worth?,evaluation}}</div>
          <div class="title-2">{{-, Tracking estimate change anytime,evaluation}}</div>
        </div>
        <div><span class="btn btn-green" @click="openEvaluatePage()">{{-, Estimate,evaluation}}</span></div>
      </div>
      <div class="type"><span class="fa fa-check-circle"><span>{{-, MLS,evaluation}}</span></span><span
          class="fa fa-check-circle"><span>{{-, Non-MLS,evaluation}}</span></span><span
          class="fa fa-check-circle"><span>{{-, Pre-construction,evaluation}}</span></span></div>
      <div class="warn">{{-, This estimate is to give you a rough estimation of the home value or rent of the subject
        property or potential property to be built.,evaluation}}</div>
    </div>
  </div>

  <div class="block">
    <div class="header">{{-, Posted Rentals,landlord}}
      <span class="pull-right" v-if='postsLength>=1' @click="goTo('/1.5/promote/mylisting?d={{=d}}')">
        {{-, All,landlord}}
        <span class="icon icon-right-nav"></span>
      </span>
    </div>
    <div class="post" v-if='postsLength>=1'>
      {{* 'home/props' it}}
    </div>
    <div class="description-card" v-else>
      <div class="title-line">
        <div>
          <div class="title-1">{{-, Post a rental,landlord}}</div>
          <div class="title-2">{{-, Be your own landlord,landlord}}</div>
        </div>
        <div><span class="btn btn-green" @click="goTo('/1.5/promote/mylisting?d={{=d}}')">{{-, Post,evaluation}}</span></div>
      </div>
      <div class="warn font12px" @click="topUp()">
        <span>
          {{-, Get 20X greater exposure,landlord}}
        </span>
        <span class="pull-right">
          {{- Top Listing}}
          <span class="icon icon-right-nav"></span>
        </span>
      </div>
    </div>
  </div>


  <div class="block">
    <div class="header">{{-, Saved Communities,landlord}}
      <span class="pull-right" v-if='cmtys.length>=1' @click="goTo('/1.5/community/filter?d={{=d}}')">
        {{- Customize }}
        <span class="icon icon-right-nav"></span>
      </span>
    </div>

    <div class="cmty-list" v-if='cmtys.length>=1'>
      <div v-for="cmty in cmtys" class="daily-feeds">
        <daily-feeds :cmty=cmty></daily-feeds>
      </div>
    </div>

    <div class="description-card" v-else>
      <div class="title-line">
        <div>
          <div class="title-1">{{- Community Locator}}</div>
          <div class="title-2">{{- Find the right location to buy your home}}</div>
        </div>
        <div><span class="btn btn-green" @click="goTo('/1.5/community/filter?d={{=d}}')">{{-, Start,evaluation}}</span>
        </div>
      </div>
      <div class="warn" @click="goTo('/1.5/community/map?d={{=d}}')">
        <span class="discover-icon fa fa-discover-focused"></span>
        {{-, Community Map,discover}}
        <span class="pull-right">
          <span class="icon icon-right-nav"></span>
        </span>
      </div>
    </div>
  </div>

<script type="text/javascript">
  var TRANSLATES = {
    Message: "{{-, Message,evaluation}}",
    Cancel: "{{- Cancel}}",
    Confirm: "{{- Confirm}}",
    afterDelete:"{{-, After deleting it, You can not receive any notification of evaluation changes.,evaluation}}",
    sqft: "{{-, sqft,prop}}",
    Saved:"{{-, Saved,favorite}}",
    ReEstimate:"{{-, Re-Estimate,evaluation}}",
    See:"{{- See}}",
    Trends:"{{- Trends}}",
    Community:"{{- Community Locator}}",
    location:"{{- Find the right location}}",
    SearchCommunities:"{{- Search communities on demand}}",
    Start:"{{- Start}}",
    Detail:"{{-, Detail,evaluation}}",
    baseOnSubscription: "{{-, Based on your subscription,discover}}",
    topListing:"{{- TOP Listing}}",
    Mortgage:"{{-, Mortgage,yellowpage}}",
    Insurance:"{{-, Insurance,yellowpage}}",
    'B&A Sales':"{{-, B&A Sales,yellowpage}}",
    Maintenance:"{{-, Maintenance,yellowpage}}",
    'Avg. Price': "{{- Avg. Price}}",
    MoM: "{{- MoM}}",
    YoY: "{{- YoY}}"
  };

</script>