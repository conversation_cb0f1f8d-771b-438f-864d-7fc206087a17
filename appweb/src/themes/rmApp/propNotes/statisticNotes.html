<link rel="stylesheet" type="text/css" href="/css/apps/propNotes.css">
<div id="statisticNotes" v-cloak>
  {{* 'components/loader' }}
  <div id="tableTitle"><img src="/img/shareimg/comparetable/logo.jpeg" class="logoPic"><span class="titleFont" v-if="allMonth.length">{{allMonth[0].split("-")[0]}}</span><span class="titleFont">{{- Notes Statistic}}</span></div>
  <table id="tableInfo" v-if="statisticInfo.length">
    <tr v-for="(item,index) in statisticInfo">
      <td>{{item.nm}}</td>
      <td>{{item.total}}</td>
      <td v-for="mon in allMonth">{{item.month[mon]? item.month[mon]:0}}</td>
    </tr>
  </table>
</div>
<script src="/js/vue3.min.js"></script>
<script src="/js/propNotes/statisticNotesPage.js"></script>