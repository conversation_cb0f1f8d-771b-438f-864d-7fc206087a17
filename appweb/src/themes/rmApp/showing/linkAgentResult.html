{{&
  var {isPopup,d,agentAvt,agentNm,linked,agentId,cid,code,self} = it;
}}
<style>
  .icon-close{
    font-size: 28px !important;
  }
  .head{
    font-weight: normal;
    text-align: center;
    margin-top: 30px;
  }
  .agent{
    margin: 44px 30px 0;
    padding: 20px 0;
    color: #999;
    font-size: 15px;
  }
  .agent img{
    width: 30px;
    height: 30px;
    border-radius: 50%;
    vertical-align: middle;
    margin-right: 10px;
  }
  .msg{
    color: #666;
    margin: 20px 30px 50px;
    font-size: 15px;
  }
</style>
<div>
  <header id="header-bar" class="bar bar-nav white">
    <a class="icon icon-close pull-right" onClick="goBack2({isPopup:{{= isPopup}},d:'{{= d}}'})"></a>
  </header>
  <div class="content">
    <h1 class="head">{{- Showing Together}}</h1>
    <div class="agent">
      <img src="{{= agentAvt}}" onerror="this.onerror=null;this.src='/img/logo.png';">
      <span>{{= agentNm}}</span>
    </div>
    <div class="msg">
      {{? linked}}
      <span>{{- You are already my showing partner!}}</span>
      {{?? self}}
      <span>{{- Cannot invite yourself.}}</span>
      {{??}}
      <span>{{- Accept this invite and you can do showings with me together.}}</span>
      {{?}}
    </div>
    {{? !(linked || self)}}
    <div class="btn btn-long btn-block btn-positive" onClick="userAccept('{{= agentId}}','{{= cid}}','{{= code}}')">{{- Accept}}</div>
    {{?}}
  </div>
</div>

<script type="text/javascript">
function userAccept(agentId,cid,code) {
  setLoaderVisibility('block');
  fetchData('/1.5/crm/linkagent',{body:{aid:agentId,cid:cid,code}},(error,res) => {
    if(res.ok) {
      setTimeout(() => {
        location.href = '/home/<USER>'
      }, 1000);
    } else {
      RMSrv.dialogAlert(res.e);
    }
  });
};
</script>
