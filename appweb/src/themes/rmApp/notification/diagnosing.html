<style>
#diagnosingModel .bgCover{
    background: rgba(0,0,0,0.6);
}
#diagnosingModel .row{
  height: 70px;
  padding-left: 10px;
  border-top: 0;
  border-bottom: 0.5px solid rgb(245,245,245);
  display: flex;
  align-items: center;
  position: relative;
}
#diagnosingModel .status{
  font-size: 37px;
  padding-right: 12px;
}
#diagnosingModel .fa{
  display: none;
}
#diagnosingModel .field{
  width: calc(100% - 50px)
}
#diagnosingModel .field-name{
  margin-bottom: 0;
  width: calc(100% - 80px);
  word-wrap: break-word;
  color: #666;
  font-weight: normal;
  vertical-align: middle;
  display: inline-block;
}
#diagnosingModel .explain {
  font-size: 14px;
  font-weight: normal;
  color: #999;
  display: none;
}
#diagnosingModel .onErr{
  position: absolute;
  right: 10px;
  width: 70px;
  height: 26px;
  line-height: 26px;
  text-align: center;
  border-radius: 3px;
  border: 1px solid #999;
  display: none;
}
#diagnosingModel .flash-message-box {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 300px;
  height: 100px;
  margin-top: -50px;
  margin-left: -150px;
  z-index: 300;
  display: none;
  opacity: 0.9;
  transition: all 0.5s;
  -webkit-transition: all 0.5s;
}
#diagnosingModel .flash-message-box span{
  position: absolute;
  top: 6px;
  right: 6px;
  color: #fff;
  display: none;
}
#diagnosingModel .flash-message-box .flash-message-inner {
  background-color: #000;
  padding: 30px 10%;
  text-align: center;
  color: white;
  border-radius: 10px;
}
#diagnosingModel .modal-40pc.active{
  min-height: 368px!important;
  height: 368px!important;
  top: calc(100% - 368px) !important;
}
#diagnosingModel .modal-40pc.active.min {
  min-height: 155px!important;
  height: 155px!important;
  top: calc(100% - 155px) !important;
}

.bar-footer {
  font-size: 15px; 
  line-height: 44px; 
  text-align: center;
}
</style>

<div id="diagnosingModel">
<div class="bgCover modal active"></div>
<div class="field-wrapper modal modal-40pc active">
  <div class="bar" style="padding-right: 0;">
    <h1 style='font-size: 17px;line-height: 44px; margin: 0;'>
      {{-, Diagnosing,notification}}
    </h1>
  </div>
  <div class="fields" style="padding-top: 44px;">
    {{& for(var k in it.diagnosingOpt) { }}
      <div class="row" id="diagnoseOption{{=k}}">
        <span class="status fa fa-check-circle" style='color:#5CB85C'></span>
        <span class="status fa fa-exclamation-circle" style='color:#FFCD00'></span>
        <img src='/img/ajax-loader1.gif' alt="" class="status">
        <div class="field"  >
          <div class="field-name">
              {{=-, it.diagnosingOpt[k].title,notification}}
            <div class="explain success">
                {{=-, it.diagnosingOpt[k].success,notification}}
            </div>
            <div class="explain failure">
                {{=-, it.diagnosingOpt[k].failure,notification}}
            </div>
          </div>
        </div>
        {{? it.diagnosingOpt[k].onFail}}
        <div class="onErr" onClick="onErrClick('{{=k}}','{{= it.diagnosingOpt[k].failSrc}}')">
          <div class="explain" style='display: block;'>
            {{=-, it.diagnosingOpt[k].onFail,notification}}
          </div>
        </div>
        {{?}}
      </div>
    {{& } }}
  </div>
  <div class="bar bar-standard bar-footer"  onClick="closeModel('showDiagnosing')">
    {{- Close}}
  </div>
</div>
<div class="flash-message-box">
  <p class="flash-message-inner">{{-, Thank you for your feed back!,notification}}</p>
</div>
</div>
<script src="/libs/jquery-2.2.3.min.js"></script>
<script src="/js/notification/diagnosing.min.js"></script>