<div class="props" id='service'>
  <div class="header">
    <span class="pull-left header-str">{{- Homeowner Services}}</span>
  </div>
  <div style="padding: 5px 10px 15px 10px">
    {{~it.services :services:index}}
    <div class="quickLinks">
      {{~services :service:index1}}
      <a class="link-wrapper" href={{=service.url}} onclick="trackEventOnGoogle('homeHomeownerService','{{=service.action}}')">
        <div>
          <div class="tl">
            {{=- service.tl}}
          </div>
          <div class="text">
            {{=- service.stl}}
          </div>
        </div>
        <div class="img">
          <img class="lazy" data-src={{=service.img}}>
        </div>
      </a>
      {{~}}
    </div>
    {{~}}
    {{? it.lang != 'en'}}
    <a class="quickLinks" href="/1.5/staging&d=/1.5/index" onclick="trackEventOnGoogle('homeHomeownerService','clickRenovationImage')">
      <img class="lazy" data-src="/img/reco_banner.jpg">
    </a>
    {{?}}
  </div>
  </div>