{{? it.appmode}}
<div id="homeSwitch">
  <div id="{{? it.headlines || it.showRealtorPanel}}news{{?}}" class="swiper-container">
    <div class="swiper-wrapper">
      <div class="swiper-slide flex">
        <div class="switchItem {{= it.appmode}} lazy {{? it.appmode == 'mls' && it.showRealtorPanel}} width-65 {{?}}" data-sub="go {{? it.appmode == 'rm'}}mls{{??}}mls{{?}}" onclick="toggleHome({{? it.appmode == 'rm'}}'mls'{{??}}'marketplace'{{?}})">
          {{? it.appmode == 'mls'}}
          <span class="icon home-sprite market_{{? it.lang == 'zh' || it.lang == 'zh-cn'}}zh{{??}}en{{?}}"></span>
          {{?? it.appmode == 'rm'}}
          <span class="icon home-sprite release_{{? it.lang == 'zh' || it.lang == 'zh-cn'}}zh{{??}}en{{?}}"></span>
          {{?}}
          <div class="switchItemWrapper">
            {{? it.appmode == 'mls'}}
            <div class="switchTl">{{- Marketplace}}</div>
            <div class="switchStl">{{- Exclusive, Landlord Rental, Yellow Page}}</div>
            {{??}}
            <div class="switchTl">{{- Resale}}</div>
            <div class="switchStl">{{- MLS for sale & rent, Trusted Assignment, Estimation, Trend}}</div>
            {{?}}
          </div>
        </div>

        {{? it.appmode == 'mls' && it.showRealtorPanel}}
        <a class="switchItem mls border-red swiper-slide lazy width-35" href="/1.5/showing/?d=/home/<USER>" data-sub="showing">
          <div class="showing">
            <div class="switchTl">{{-, Showing,todo}}</div>
            <div class="switchStl"></div>
          </div>
          <div style="font-size: 12px;">
            <span class="showing-num">{{= it.upcomingShowing || 0}}</span>
            {{- Upcoming}}
          </div>
        </a>
        {{?}}
      </div>

    {{? it.headlines?.length}}
      {{~ it.headlines :headline:index}}
      {{? headline.url}}
        <a onclick='trackEventOnGoogle("homeHeadline","{{= headline.tl}}")' data-sub="news" data-id="{{= headline.url.split('postid=')[1]}}" class="headline {{= index==0?'':'hide'}} swiper-slide lazy news {{= it.appmode}}" href={{=headline.url}}>
          {{? headline.thumb}}
          <div class="nav">
            <img class="lazy" data-src={{= headline.thumb}} src="/img/no-photo.png">
          </div>
          {{?? headline.tp}}
          <div transition="none" class="tp">{{= headline.tp}}</div><span class="split"></span>
          {{?}}
          <div class="headline-content">
            {{? headline.tl}}
            <div transition="none" class="ct">{{= headline.tl}}</div>
            {{?}}
          </div>
        </a>
      {{?}}
      {{~}}
    {{?}}
    </div>
    {{? it.headlines?.length}}
    <div class="newsPagination"></div>
    {{?}}
  </div>
</div>
{{?}}
