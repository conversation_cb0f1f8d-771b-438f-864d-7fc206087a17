<div id="indexAd">
  <div class="header">
    {{- PreCon Coming Soon}}
    {{? it.appmode == 'rm'}}
    <a data-sub="all project" onclick="gotoLink('/1.5/mapSearch?d=/1.5/index&mode=list&mapmode=projects',{'key':'homeRecommendProject','val':'openProjects'})" class="pull-right">{{-View All}}
      <span style="width: 16px;text-align: right;" class="icon icon-right-nav"></span>
    </a>
    {{?}}
  </div>
  <div class="sp-holder swiper-container" id='preCon'>
    <div class="sp-wrapper {{= (it.indexAd.length==1)?'one':(it.indexAd.length==2)?'two':'three'}} swiper-wrapper {{? it.appmode == 'mls'}}swip{{?}}">
      {{~it.indexAd :proj:idx}}
      <!-- OLD: openPopUp -->
      <div onclick="clickedAd({'_id':'{{= proj._id}}','inapp':{{= proj.inapp}},'tgt':'{{= proj.tgt}}',
          'url':'/1.5/prop/projects/detail?inframe=1&lang={{= it.lang}}&id={{= proj.pid}}&mode=list&d=/1.5/index',
          'title':'{{- RealMaster}}'
        },'homeRecommendProject',{{=idx}})" class="sp swiper-slide" data-sub="proj ad" data-id="{{= proj._id}}">
        <div class="img">
          <img src="{{= proj.src ||'/img/noPic.png' }}" onerror="this.src='/img/noPic.png'"/>
          <!-- {{? proj.avt}}
          <div class="on-image">
            <img class="avt lazy" data-src="{{= proj.avt}}">
            <span class="vvip">{{- VVIP}}</span>
          </div>
          {{?}} -->
        </div>
        <div class="tl">
          <span >{{= proj.name || ''}}</span>
        </div>
        <div class="desc">
          <span >{{= proj.desc || ''}}</span>
        </div>
      </div>
      {{~}}
    </div>
    <div class="preConPagination"></div>
  </div>
</div>
