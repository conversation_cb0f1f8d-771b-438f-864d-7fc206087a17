<div id="marketRcmd" class="props" style="padding-left: 5px;">
  <div class="header">
    <span class="discover-title">{{- Trusted Assignment}}</span>
    <a class="pull-right" style="color:#B5B7B8;font-size: 12px;" data-sub="all trusted assign" onclick="gotoLink('/truAssigm?d=/1.5/index',{'key':'homeTrustedAssignment','val':'openTrustedAssignment'})">
      {{-View All}}&nbsp;&nbsp;<span class="icon icon-right-nav" style="color:#B5B7B8;"></span>
    </a>
  </div>
  <div id="trustedAssign" class="mkt-holder swiper-container">
    <div class="mktrcmd top {{= (it.truAssign.length==1)?'one':(it.truAssign.length==2)?'two':'three'}} swiper-wrapper {{? it.appmode == 'mls'}}swip{{?}}">
      {{~it.truAssign :truAss:idx}}
      <div class="mkt rmProp rcmdProp swiper-slide" onclick="gotoLink('/truAssigm?d=/1.5/index&id={{= truAss.id}}',{'key':'homeTrustedAssignment','val':'openTrustedAssignment'})" data-sub="trusted assign" data-id="{{= truAss.id}}">
        <div class="img">
          <img src="{{= truAss.image ||'/img/noPic.png' }}" onerror="e => {e.target.src = '/img/noPic.png'}"/>
          <div class="showOnImg">
            {{? truAss.priceValStrRed}}
            <div class="price">{{= truAss.priceValStrRed}}</div>
            {{?}}
            <div class="bdrms">
              {{? truAss.bdrms}}
              <span>
                <span class="fa fa-rmbed"></span>&nbsp;{{= truAss.bdrms}}{{? truAss.br_plus}}+{{=truAss.br_plus}}{{?}}&nbsp;&nbsp;
              </span>
              {{?}}
              {{? truAss.rmbthrm || truAss.tbthrms || truAss.bthrms}}
              <span><span class="fa fa-rmbath"></span>&nbsp;{{= truAss.rmbthrm || truAss.tbthrms || truAss.bthrms || ''}}&nbsp;&nbsp;</span>
              {{?}}
              {{? truAss.rmgr || truAss.tgr || truAss.gr}}
              <span><span class="fa fa-rmcar"></span>&nbsp;{{= truAss.rmgr || truAss.tgr || truAss.gr ||''}}&nbsp;</span>
              {{?}}
              {{? truAss.sqft}}
              <span><span class="fa fa-area"></span>&nbsp;{{= truAss.sqft}}</span>
              {{?}}
            </div>
          </div>
        </div>
        <div class="detail">
          {{? truAss.addr}}
          <div class="prov">
            <span class="fa fa-map-marker"></span>
            {{? truAss.city}}{{= truAss.city}}{{?}}
            {{? truAss.prov}}, {{= truAss.prov}}{{?}}
          </div>
          {{?}}
        </div>
        {{? truAss.m_zh || truAss.m}}
          <div class="truAssignDesc">
            {{? it.lang == 'zh' || it.lang == 'zh-cn'}}
            <span>{{= truAss.m_zh || truAss.m}}</span>
            {{??}}
            <span>{{= truAss.m}}</span>
            {{?}}
          </div>
        {{?}}
      </div>
      {{~}}
    </div>
    <div class="assignPagination"></div>
  </div>
</div>
