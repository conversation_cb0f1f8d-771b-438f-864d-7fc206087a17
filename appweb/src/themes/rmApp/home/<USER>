<style>
.headline.hide {
  display: none;
}
</style>
<div class="headlines">
{{~it.headlines :headline:index}}
{{?headline.url}}
<a onclick="trackEventOnGoogle('homeHeadline','{{=headline.tl}}')" class="headline {{= index==0?'':'hide'}}" href={{=headline.url}}>
  {{?headline.tp}}
  <div transition="none" class="tp">{{= headline.tp}}</div><span class="split"></span>
  {{?}}
  <div class="headline-content">
    {{?headline.tl}}
    <div transition="none" class="ct">{{= headline.tl}}</div>
    {{?}}
    {{?headline.thumb}}
    <div class="nav">
      <img class="lazy" data-src={{= headline.thumb}} alt={{= headline.tl}}>
    </div>
    {{??}}
    <div class="nav icon icon-right-nav"></div>
    {{?}}
  </div>
</a>
{{?}}
{{~}}
</div>
<script>
var headlines = document.getElementsByClassName('headline');
var headlinesContent = document.getElementsByClassName('headline-content');
var j = 0;
setInterval(function() {
  j = (j+1)%headlinesContent.length;
  var headline = headlinesContent[j];
  for (var i = 0; i < headlinesContent.length; i++) {
    headlines[i].classList.add('hide');
    headlinesContent[i].classList.remove('infi-fade');
    headlinesContent[i].classList.add('infi-fade');
  }
  headlines[j].classList.remove('hide');
  headline.classList.remove('infi-fade');
  headline.classList.add('infi-fade');
}, 4500);
</script>