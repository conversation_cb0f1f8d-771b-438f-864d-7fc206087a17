<section id="alert-window-bg">
  <input type="hidden" value="{{= it.os}}" id="currentOS"/>
  <section id="alert-window">
    <img src="/img/download/app_upgrade.png" alt="App Upgrade" />
    <div class="alert-detail">
      <h4 class="heading">{{- New Core Version Found!}}</h4>
      {{?it.appUpgrade.upgradeVer}}
      <div class="version-container">
        <span class="version">{{= it.appUpgrade.upgradeVer}}</span>
      </div>
      {{?}}
      {{?it.appUpgrade['feature_'+it.lang]}}
        <p class="feature">{{= it.appUpgrade['feature_'+it.lang]}}</p>
      {{?}}
      <!-- <div class="version-container">
        <span class="version">{{- Current Version}}: {{= it.appVerNum || 'web'}}</span>
      </div> -->
      <a class="upgrade-link" onclick="openDownloadLink('{{= it.appUpgrade.downloadLink}}')" data-sub="upgrade app">{{- Upgrade Now}}</a>
      {{? it.appUpgrade.forceUpgrade}}
      <a class="nexttime" onclick="closeApp()" data-sub="quit upgrade">{{- Quit}}</a>
      {{??}}
      <a class="nexttime" onclick="hideAlert()" data-sub="next time">{{- Next Time}}</a>
      {{?}}
    </div>
  </section>
</section>
