{{~it.promotions :promotion:idx}}
<div class="promotion">
  <!-- <img class="realtorIcon" src="/img/category-png/rmcat-top.svg"/> -->
  <!-- <div class="sprite realtorIcon {{= (promotion.promotionTp == 'listing')?'top':'listing'}}"></div> -->
  <div class="promotionInfo">
    <div class="promotionDetail">
      <div class="promotionContent">
        <div class="promotionTl">{{=-, promotion.tl,todo}}</div>
        <div class="promotionStl">{{=-, promotion.stl,todo}}</div>
      </div>
      {{? promotion.promotionTp == 'exlisting'}}
      <a class="todoReply btn btn-positive" data-sub="promotion post {{=promotion.promotionTp}}" href="/1.5/promote/mylisting?d=/1.5/index" onclick="trackEventOnGoogle('realtorPromo','openMylisting')">{{-, Post,todo}}</a>
      {{?}}
      {{? promotion.promotionTp == 'listing'}}
        <a class="todoReply btn btn-positive" data-sub="promotion {{=promotion.promotionTp}}" onclick="openPopup('/1.5/prop/topup/charge?d=/1.5/index','{{-, Top Listing,todo}}');trackEventOnGoogle('realtorPromo','openPropTopup')">{{- Detail}}</a>
      {{?}}
      {{? promotion.promotionTp == 'topExclusives'}}
        <a class="todoReply btn btn-positive" data-sub="promotion {{=promotion.promotionTp}}" onclick="openPopup('/1.5/prop/topup/charge?d=/1.5/index','{{-, Top Listing,todo}}');trackEventOnGoogle('realtorPromo','openPropTopup')">{{- Detail}}</a>
      {{?}}
      {{? promotion.promotionTp == 'project'}}
      <a class="todoReply btn btn-positive" data-sub="promotion {{=promotion.promotionTp}}" onclick="openPopup('/1.5/prop/topup/charge?tp=project&d=/1.5/index','{{-, Top Pre-Construction,todo}}');trackEventOnGoogle('realtorPromo','openProjTopup')">{{- Detail}}</a>
      {{?}}
    </div>
    {{? promotion.prop && promotion.prop.adrltr}}
    {{? promotion.promotionTp=='project'}}
    <div class="promotionAdrltr" data-sub="promotion last toped project" data-id="{{= promotion.prop.pid}}" onclick="openPopup('/1.5/prop/projects/detail?id={{= promotion.prop.pid}}&inframe=1&d=/1.5/index','RealMaster');trackEventOnGoogle('realtorPromo','openPromotedProj')">
    {{??}}
    <div class="promotionAdrltr" data-sub="promotion last {{=promotion.promotionTp}}" data-id="{{= (/^RM/.test(promotion.prop.id))?promotion.prop.id:promotion.prop._id}}" onclick="openPopup('/1.5/prop/detail/inapp?lang={{=it.lang}}&id={{= (/^RM/.test(promotion.prop.id))?promotion.prop.id:promotion.prop._id}}&mode=map&d=/1.5/index','RealMaster');trackEventOnGoogle('realtorPromo','openPromotedProp')">
    {{?}}
      <div class="promotionAdrltrWrapper">
        {{?promotion.prop.adrltr.avt}}
        <img class="promotionAvt lazy" data-src="{{=promotion.prop.adrltr.avt}}" alt="{{=promotion.prop.adrltr.nm}}" />
        {{?}}
        <div class="promotionAvtName">
          <span class="promotionAvtNameValue">{{= promotion.prop.adrltr.nm}}</span>
          {{? promotion.promotionTp == 'listing'}}
          <span class="promotionAvtNameAction">{{-, topped a,todo }}<span class="promotionAvtTp">{{-, listing,todo}}</span></span>
          {{?}}
          {{? promotion.promotionTp == 'topExclusives'}}
          <span class="promotionAvtNameAction">{{-, topped a,todo }}<span class="promotionAvtTp">{{-, exclusive listing,todo}}</span></span>
          {{?}}
          {{? promotion.promotionTp == 'exlisting'}}
            {{? promotion.prop.ltp == 'assignment'}}
            <span class="promotionAvtNameAction">{{-, posted an,todo }}<span class="promotionAvtTp">{{-, assignment,todo}}</span>
            {{?}}
            {{? promotion.prop.ltp == 'exlisting'}}
            <span class="promotionAvtNameAction">{{-, posted an,todo }}<span class="promotionAvtTp">{{-, exclusive listing,todo}}</span></span>
            {{?}}
            {{? promotion.prop.ltp == 'rent'}}
            <span class="promotionAvtNameAction">{{-, posted an,todo }}<span class="promotionAvtTp">{{-, exclusive rental,todo}}</span></span>
            {{?}}
          {{?}}
          {{? promotion.promotionTp == 'project'}}
          <span class="promotionAvtNameAction">{{-, topped,todo}}<span class="promotionAvtTp">{{= (it.lang !='en')?(promotion.prop.name):(promotion.prop.name_en||promotion.prop.name)}}</span></span>
          {{?}}
        </div>
      </div>
      <!-- <span class="fa fa-angle-right"></span> -->
    </div>
    {{??}}
    <div class="promotionAdrltr"></div>
    {{?}}
  </div>
</div>
{{~}}