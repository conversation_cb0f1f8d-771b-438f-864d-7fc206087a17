<!DOCTYPE html>
<html lang="{{=it.lang}}">
  <head>
    <meta charset="utf-8">
    <meta name="X-UA-Compatible" content="IE=edge">
    <meta name="HandheldFriendly" content="true">
    <meta name="robots" content="noodp">
    <meta name="viewport" content="width=device-width,height=device-height,initial-scale=1.0,maximum-scale=1.0,user-scalable=0">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="RealMaster.com">
    <meta property="og:title" content="RealMaster">
    <meta property="og:image" content="/img/logo_s.png">
    <meta property="twitter:site" content="RealMaster.com">
    <meta property="twitter:title" content="RealMaster">
    <meta property="twitter:creator" content="RealMaster">
    <meta name="referrer" content="same-origin">
    <title>RealMaster</title>
    {{?it.appjs}}
    <script src="{{=it.appjs}}"></script>
    {{?}}
    <script type="text/javascript">var gShareHostNameCn = '{{=req.shareHostNameCn()}}';</script>
    <script src="/js/rmapp.min.js"></script>
    <script type="text/javascript" async="" src="https://www.google-analytics.com/analytics.js"></script>
    <link rel="stylesheet" type="text/css" href="/css/ratchet.min.css">
    <link rel="stylesheet" type="text/css" href="/css/apps/drawers.css">
    <link rel="stylesheet" type="text/css" href="/css/font-awesome.min.css">
    <link rel="stylesheet" type="text/css" href="/css/smb.min.css">
    <link rel="stylesheet" type="text/css" href="/css/page.min.css">
    <link rel="stylesheet" type="text/css" href="/css/apps/apphome.css">
    <script src="/js/swiper.min.js"></script>
    {{? it.isChinaIP}}
      <script src="https://s.realmaster.cn/logger.lib.min.js"></script>
    {{??}}
      <script src="https://s.realmaster.com/logger.lib.min.js"></script>
    {{?}}
    <style type="text/css">
    .sprite {
      background-image: url('/img/sprite.png');
      background-repeat: no-repeat;
    }
    .swiper-container {
      position: relative;
      overflow: hidden;
      list-style: none;
      padding: 0;
      z-index: 1;
    }
    .swiper-wrapper {
      position: relative;
      width: 100%;
      height: 100%;
      z-index: 1;
      display: flex;
      transition-property: transform;
      box-sizing: content-box;
    }
    .swiper-container-android .swiper-slide, .swiper-wrapper {
      transform: translate3d(0px,0,0);
    }
    .swiper-slide {
      flex-shrink: 0;
      width: 100%;
      height: 100%;
      position: relative;
      transition-property: transform;
    }
    #autocompleteWrapper {
      z-index:1;
    }
    </style>
</head>
<body>
  {{* 'components/loader' }}
  <div class="page-container">
    <div style="display:none" id="rmPageBarColor">:name:mainTheme</div>
    {{? it.appUpgrade && it.appUpgrade.showAlert }}
    {{* 'home/appUpgrade' it }}
    {{?}}
    {{* 'bottomNav' it}}
    <div id='index-header'>
      <header class="bar bar-nav rm">
      <img src="/img/marketplace.png"></img>
      <!-- <a v-cloak href="javascript:;" @click="getCityList('setUserCity')" class="city">
        <span class="cityName">{{dispVar.userCity.n || dispVar.userCity.o}}
        </span>
        <span class="icon icon-down-nav"></span>
      </a> -->
      <a v-cloak data-sub="map" href="javascript:void 0" @click="goTo({url:'/1.5/mapSearch?d=/home/<USER>',googleCat:'homeTopBar',googleAction:'openNearby'});" class="chat pull-right"><span>{{-, MAP,indexSearch}}</span></a>
      <a v-cloak data-sub="search" id="searchBtn" @click="onClickSearchBar()" class="fa fa-rmsearch pull-right"></a>
      <!-- <city-select-modal :need-loc='false' :cur-city="{}" :lang="dispVar.lang"></city-select-modal> -->
      </header>
      <lang-select-cover :disp-var="dispVar" :lang="dispVar.lang" :old-ver="dispVar.langCoverOldVer"></lang-select-cover>
      <div id="SignupModal" class="modal modal-fade" v-if="!hasWechat">
        <header class="bar bar-nav">
          <a class="icon icon-close pull-right" href='javascript:;' onclick="toggleModal('SignupModal')"></a>
        </header>
        <div class="content">
          <sign-up-form :feedurl="feedurl" :owner="{vip:true}" :user-form="userForm" :title="signupTitle" ></sign-up-form>
        </div>
      </div>
      <flash-message/>
      <div style="display:none">
        <span v-for="(v,k) of strings">{{ $_(v.key)}}</span>
      </div>
    </div>
    <div class="indexMainFrame" id="forum">
      <div class="content" id="content">
        <div class="content-list">
          {{? it.banners && it.banners.length > 0}}
            <div class="banner">
              {{* 'home/banners' it}}
            </div>
          {{?}}
          <div class="head">
            {{* 'home/drawers' it}}
          </div>
          {{* 'home/homeSwitch' it}}
          {{?it.showRealtorPanel}}
            {{* 'home/realtor/todos' it}}
            {{* 'home/realtor/promotions' it}}
          {{?}}
          {{? it.indexAd && it.indexAd.length > 0}}
          {{* 'home/projects' it}}
          {{?}}
          {{? it.recos && it.recos.length > 0}}
          {{* 'home/propsRm' it}}
          {{?}}
          {{* 'home/forum' it}}
          </div>
        </div>
        <div class="mask" :class="{show:showMask}" @click="clickMask()"></div>
        <block-popup :flag-post='flagPost'></block-popup>
        <report-forum-form v-if="showReportForm" :owner="{vip:true}" :feedurl="reportForm.feedurl" :title="reportForm.title" :user-form="reportForm.userForm"></report-forum-form>
      </div>
    </div>
    <div id="obj" style='display:none'>rm</div>
    <div id="user-roles" style='display:none'>{{= it.userRoles}}</div>
  </div>
  {{?it.osScript}}
  <script src="{{=it.osScript}}"></script>
  {{?}}
  <script src='/js/vue3.min.js'></script>
  <script src='/js/axios.min.js'></script>
  <script src="/js/stdfn.min.js"></script>
  <script src='/js/home/<USER>'></script>
  <script type="text/javascript">
    loadLazyImg();
    if(window.gtmjs){
      gtmjs({tags:{js:new Date()}})
    }
  </script>
</body>
</html>




