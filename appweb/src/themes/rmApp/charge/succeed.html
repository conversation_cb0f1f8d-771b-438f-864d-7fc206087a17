{{&
  var {body,instructions,msg} = it;
}}
<style>
.content.success{
  text-align: center;
  background: white;
  padding: 80px 20px 0 20px;
}
.content.success .msg{
  font-size: 21px;
  color: black;
  padding-bottom: 15px;
  font-weight: 400;
}
.content.success .desc{
  font-size: 13px;
  color: #666;
  text-align: left;
}
.content.success img{
  width: 100px;
}
.fail{
  display: none;
}
  </style>
<div class="content success fail">
  <header class="bar bar-nav">
    <a class="icon icon-close pull-right" href="javascript:;" onclick="goBack()"> </a>
    <h1 class="title">{{- TOP Listing}}</h1>
  </header>
  <img src="/img/paytop/check.png" />
  <div class="msg">{{= msg}}</div>
  <div class="desc">{{= instructions}}</div>
</div>
<script type="text/javascript">
var vars = window.location.search;
function goBack(){
  window.rmCall(':ctx::cancel');
};
if(/cancel=/.test(vars)){
  goBack()
} else {
  var content = document.querySelector(".content");
  content.classList.remove("fail");
}
// var body = JSON.parse('{{=JSON.stringify(body)}}');
// function afterSucceed() {
//   if(!(body.id && body.days && body.originalAmount && body.chargeAmount && body.addr && body.city && body.prov && body.cnty)){
//     return goBack();
//   };
//   var content = document.querySelector(".content");
//   content.classList.remove("fail");
//   /*TODO: if not window.fetchData use window.fetch instead*/
//   fetchData('/1.5/prop/topup/charge',{body},(error,res) => {
//     console.log(res);
//   });
//   }
// window.onload = (event) => {
//   afterSucceed();
// };
</script>