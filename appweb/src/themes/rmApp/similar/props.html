{{&
  var {css,js,tabs,id,d} = it;
}}
<link rel="stylesheet" href="{{= css}}">
<link rel="stylesheet" type="text/css" href="/css/sprite.min.css">
<div id="similar" v-cloak>
  <header id="header-bar" class="bar bar-nav" style="z-index: 18;">
    <h1 class="title" v-if='isBuilding'>{{- Same Building }}</h1>
    <h1 class="title" v-else>{{- Similar Listings }}</h1>
    <a class="icon icon-close pull-right" style="font-size: 28px;" href="javascript:;" @click="closeSimilar()"></a>
  </header>
  {{* 'components/loader' }}
  <div class="fix-bottom" v-if='curProp && curProp._id'>
    <div style="position:relative;">
      <img class="img" :src="curProp.thumbUrl || '/img/noPic.png'" referrerpolicy="same-origin"></img>
      <span id="CMAPosition" @click.stop.prevent='popupSelect("cma")' v-if='dispVar.isDevGroup || dispVar.isRealGroup'>
        <span class="sprite16-21 sprite16-7-3"></span>
      </span>
    </div>
    <div class="cur-prop-info">
      <div class="addr">
        <span v-show='curProp.addr'>{{curProp.origUnt || curProp.unt}}  {{curProp.addr}}, </span>
        <span>{{curProp.city ? (curProp.origCity || curProp.city) + ', ' : ''}}{{curProp.zip}}</span>
      </div>
      <div class="info">
        <span>{{curProp.priceValStrRed}} </span>
        <span v-show="curProp.bdrms != null || curProp.tbdrms != null">{{rmbdrmStr(curProp)}}{{ $_('bd')}}, </span>
        <span v-show="curProp.rmbthrm != null || curProp.tbthrms != null || curProp.bthrms != null">{{rmbthrmStr(curProp)}}{{ $_('ba')}}, </span>
        <span>{{curProp.ptype2?curProp.ptype2.join(','):curProp.pstyl}}</span>
      </div>
      <div v-show="curProp.sqft || (curProp.sqft1&&curProp.sqft2) || curProp.rmSqft">
        {{ $_('Size')}}&nbsp;:&nbsp;
        <span class='val'>
          <span v-if="curProp.sqft">{{parseSqft(curProp.sqft)}}</span>
          <span v-else-if="curProp.sqft1&&curProp.sqft2">{{curProp.sqft1}}-{{curProp.sqft2}}</span>
          <span v-show="curProp.rmSqft && dispVar.isLoggedIn">
            <span v-show="curProp.sqft && /\-/.test(curProp.sqft) && (curProp.sqft!=curProp.rmSqft)">&nbsp;({{parseSqft(curProp.rmSqft)}})</span>
            <span v-show='!curProp.sqft'>{{parseSqft(curProp.rmSqft)}} ({{ $_('Estimated')}})</span>
          </span>
          <span>&nbsp;{{ $_('ft&sup2;')}}</span>
        </span>
      </div>
      <div v-show="curProp.front_ft">
        {{ $_('Lot Size')}} : {{curProp.front_ft}} * {{curProp.depth}} {{curProp.lotsz_code}} {{curProp.irreg}}
        </span>
      </div>
    </div>
  </div>
  <div class="tabs" style="top:44px;">
    <a class="tabs-container">
      <div class="tab pagination" v-for='(tag,idx) in saleTypes' @click='selectType(tag)' :key='idx' :class="curSaleType == tag.key?'selected':''">
        <div>{{ $_(tag.val)}}</div>
        <div class="red-border" :class='calcDirection(tag.key,idx)'></div>
      </div>
    </a>
  </div>
  <div class="tagsPosition">
    <div class="tags" :style="{'width':(dispVar.isDevGroup || dispVar.isRealGroup)? 'calc(100% - 68px)': 'calc(100% - 40px)'}">
      <span class="tag" v-for='bd in filterCondit' @click='selecteBd(bd)' :class='{active:bd.k == selectedCondit}'>
        <span v-if='bd.v == "Any"'>{{ $_(bd.v)}}</span>
        <span v-else>
          <span v-if='isBuilding'>{{bd.v}} {{ $_('bd')}}</span>
          <span v-else>{{bd.v}} {{ $_('mon')}}</span>
        </span>
      </span>
    </div>
    <div>
      <span v-if='dispVar.isDevGroup || dispVar.isRealGroup' class="icon sprite16-21 sprite16-7-2" style="margin: 7px 5px 0 5px;" @click="propCMAOrShareSel('cma')"></span>
      <span class="icon sprite16-21 sprite16-6-1" style="margin: 0px 5px 0 5px;" @click="propCMAOrShareSel('share')"></span>
    </div>
  </div>
  <div v-if="similarProps.length" class='prop-list-container'>
    <div class="prop-list">
      <prop-item v-for="prop in similarProps" v-bind:prop='prop' v-bind:rcmd-height="rcmdHeight" v-bind:disp-var='dispVar' :key="prop._id" :form-page='fromPage'>
    </div>
  </div>
  <div v-else class="no-results-found">
    {{- No listings found}}
  </div>
  <prop-fav-actions v-bind:disp-var='dispVar'></prop-fav-actions>
  <div class="backdrop" :class='{show:showPropTable}' @click='reset()'></div>
  <div :class='{"prop-part": showPropTable}'>
    <div v-if="favProps.length" class='prop-list-container'>
      <prop-sel-table :fav-props="favProps" :from='"similar"' :show-prop-table='showPropTable' :prop-img-height="propImgHeight" :action='action' :current-grp="currentGrp" :grps="grps"></prop-sel-table>
    </div>
  </div>
  <flash-message></flash-message>
  <share-dialog :w-dl.sync="wDl" :w-sign.sync="wSign" :disp-var="dispVar" :prop="prop"></share-dialog>
  <div style="display:none" class="WSBridge">
    <listing-share-desc v-bind:prop='prop' v-bind:is-app='dispVar.isApp'></listing-share-desc>
    <div id="wx-title">{{- RealMaster}} {{favProps.length}}{{favProps.length>=99?'+':''}} {{- Results}}</div>
    <div id="wx-desc">
      <span v-if="dispVar.sessionUser._id">{{- Shared By}} {{dispVar.sessionUser.fnm?dispVar.sessionUser.fnm:dispVar.sessionUser.nm}},{{dispVar.sessionUser.mbl}},{{dispVar.sessionUser.eml}}</span>
      <span v-else>{{- RealMaster Multiple Properties Sharing}}</span>
    </div>
    <div id="wx-image"><span v-if="dispVar.sessionUser">{{dispVar.sessionUser.avt || 'https://realmaster.cn/img/logo.png'}}</span><span v-else>https://realmaster.cn/img/logo.png</span></div>
    <div id="share-image">{{shareImage}}</div>
    <div id="share-data">{{shareData}}</div>
    <div id="m-share-title-en">RealMaster {{shareList.length}} Properties</div>
    <div id="m-share-desc-en">{{sessionUserContact?'Shared by':''}} {{sessionUserContact}}</div>
    <div id="m-share-title">{{- Results}} {{shareList.length}} {{- Properties}}</div>
    <div id="m-share-desc">{{sessionUserContact?$_('Shared By'):''}} {{sessionUserContact}}</div>
    <div id="m-share-data">{{mShareData}}</div>
    <div id="m-share-image">{{mShareImage}}</div>
    <div id="m-share-url">http://{{dispVar.reqHost}}/1.5/search/prop?{{mShareData}}</div>
  </div>
  <div style="display:none">
    <span v-for="(v,k) of strings"> {{ $_(v)}}</span>
  </div>
</div>
{{~ js :j:idx}}
<script type="text/javascript" src="{{= j}}"></script>
{{~}}
