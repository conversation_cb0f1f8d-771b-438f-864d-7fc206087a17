{{&
  var {post,relatedPosts} = it;
  var ratio = post.ratio || '16:9';
  var l = ratio.split(':');
  var percent = parseInt(l[1])/parseInt(l[0]);
}}
<div class="forumDetail">
  {{? post.youtubeID && post.src == 'video'}}
  <div class="video-wrapper parent web">
    <iframe class="video-wrapper child" data-src="realmaster" width="375" height="211" src="https://www.youtube.com/embed/{{= post.youtubeID}}?playsinline=1&rel=0" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; " sandbox="allow-same-origin allow-presentation allow-scripts allow-forms allow-modals" allowfullscreen></iframe>
  </div>
  <script type="text/javascript">
  document.addEventListener('DOMContentLoaded',function() {
    var containerWidth = document.querySelector('.forumDetail').offsetWidth;
    var wrapper = document.querySelectorAll('.video-wrapper');
    var width = containerWidth+'px';
    var height = containerWidth*{{=percent}}+'px';
    if(wrapper.length){
      var index = 0, length = wrapper.length;
      for ( ; index < length; index++) {
        wrapper[index].height = height;
        wrapper[index].width = width;
        wrapper[index].style.width = width;
        wrapper[index].style.height = height;
      }
    }
    var wrapper2 = document.querySelector('#postViewContainer');
    if(wrapper2){
      wrapper2.style.marginTop = (containerWidth*{{=percent}} + 70)+'px';
    }
  });
    </script>
  {{?}}
  <div id="postViewContainer">
    <h1 class="post-title">{{= post.tl}}</h1>
    <div class="post-content">
      {{= post.m}}
    </div>
  </div>
  <div id="forumDetail">
    {{? !post.del}}
    <div class="post-tags">
      {{? post.city||post.prov||post.cnty }}
      <span class="btn btn-default">{{=- post.city||post.prov||post.cnty}}</span>
      {{?}}
      {{~ post.tags :tag}}
      <span class="btn btn-default">
        {{? tag == 'HOT'}}
        {{-, HOT,forum}}
        {{??}}
        {{= tag}}
        {{?}}
      </span>
      {{~}}
    </div>
    {{?}}

    {{? relatedPosts && relatedPosts.length}}
    <div class="post-topics">
      <div class="tp-title">{{-, Topic,forum}}: {{- RealMaster Hot Forum Articles}}</div>
      {{~ relatedPosts :p}}
      <a class="topic-content {{= p.tp ? 'mainTopic' :'' }}" href="/forum/{{=p._id}}">
        {{?p.thumb}}
        <img src="{{=p.thumb}}" />
        <div class="line-wrap line-wrap-2 content-right">{{=p.tl}}</div>
        {{??}}
        <div class="line-wrap line-wrap-2 full-width">{{=p.tl}}</div>
        {{?}}
      </a>
      {{~}}
    </div>
    {{?}}

    <a href="/app-download" class="download-button">{{- Read More Posts}}</a>

    {{? (!post.discmnt || it.forumAdmin) && post.cmnts.length}}
    <div class="comments-header">
      <span class="comment-title">{{-, Comments,forum}}</span>
      <span class="comment-cnt">{{= post.cmnts.length}}</span>
      {{? post.discmnt}}
      <span style="font-size:14px;color:#e03131;margin-left:10px;">{{- Not visible to the public}}</span>
      {{?}}
    </div>
    <div id="recent_cmnts_container">
      {{~ post.cmnts :c}}
      {{? c.del == false}}
      <div class="post-comments">
        <div class="post-img" style="background-image: url({{=c.avt}});"></div>
        <div class="comments-right">
          <div class="pull-left full-width">
            <div class="name-wrapper">
              <div class="name">{{=c.fornm}}</div>
              {{? c.tupcnt}}
              <div class="pull-right">
                <span class="pointer icon fa fa-rmlike"></span>
                <span class="pull-right2 tupcnt">{{=c.tupcnt}}</span>
              </div>
              {{?}}
            </div>
            <div class="ts">
              {{?c.r}}
              <div class="pull-left my-rating my-rating-{{=c.r}}"></div>
              {{?}}
              {{?c.sticky}}
              <span class="red-button pull-left">{{- TOP}}</span>
              {{?}}
              <span>{{=c.ts}}</span>
            </div>
          </div>
          <div class="comment-content">
            <span>{{=c.m}}</span>
          </div>
        </div>
      </div>
      {{?}}
      {{~}}
    </div>
    {{?}}

  </div>
</div>