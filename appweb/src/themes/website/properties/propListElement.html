
<div class="col-xs-12 col-sm-6 col-md-4 ">
  {{? !it.prop.login}}
  <span 
    class='fa heart-icons{{? it.prop.fav}} fa-heart{{??}} fa-heart-o{{?}}' 
    role="button" 
    data-property-id="{{= it.prop._id}}"
    data-favorite-group="{{= it.prop.favGrp}}"
    data-top-ts="{{= it.prop.topTs}}">
  </span>
  {{?}}
  <a 
    class="listing-prop box-shadow border-radius-all" 
    href="{{= it.prop.webUrl}}" 
    {{? !it.prop.login||it.prop.user}}target="_blank"{{?}}
  >
    {{? it.prop.login&&!it.prop.user}}
    <span class="listing-prop-require-login">
      <i class="fa fa-lock"></i>
      <div>{{- Login To View More}}</div>
    </span>
    {{?}}
    <div class="listing-prop-img-container">
      {{? it.prop.isTop}}
      <div class="listing-prop-img-status toplisting">{{- TOP}}</div>
      {{?}}
      <div class="listing-prop-labels">
        {{? it.prop.ltp == 'assignment'}}
        <span class="listing-prop-label">{{- Assignment}}</span>
        {{?}}
        {{? it.prop.ltp == 'exlisting'}}
        <span class="listing-prop-label">{{- Exclusive}}</span>
        {{?}}
        {{? (it.prop.ltp == 'rent') && (!it.prop.cmstn)}}
        <span class="listing-prop-label">{{- Landlord Rental}}</span>
        {{?}}
        {{? (it.prop.ltp == 'rent') && (it.prop.cmstn)}}
        <div class="listing-prop-label">{{- Exclusive Rental}}</div>
        {{?}}
      </div>
      {{? it.prop.thumbUrl || it.prop.sid}}
      <img src="{{= it.prop.thumbUrl}}"
        onerror="this.onerror=null;this.src='/img/no-photo.png';" alt=""
        class="listing-prop-img border-radius-top {{? it.prop.login&&!it.prop.user}}blur{{?}}">
      {{??}}
      <div class="listing-prop-no-img">
        <div class="listing-prop-no-img-container">
          <span class="listing-prop-no-img-icon fa fa-home"></span>
          <span>{{- No Image}}</span>
        </div>
      </div>
      {{?}}
    </div>
    <div class="listing-prop-detail {{? it.prop.login&&!it.prop.user}}blur{{?}}">
      <h3 class="listing-prop-price">
        {{? it.prop.priceValStrRed}}
        <span class="{{? it.prop.status_en == 'U'}}detail-lp{{?}}">
          {{= it.prop.priceValStrRed}}
        </span>
        {{?}}
        {{? it.prop.saleTpTag}}
        <span>{{= it.prop.saleTpTag}}</span>
        {{?}}
        {{? it.prop.dist}}
        <span class="listing-prop-dist">{{=it.prop.dist}}</span>
        {{?}}
      </h3>

      {{? it.webRmInfoDisp && it.prop.adrltr}}
      <span class="listing-prop-adrltr" onclick="window.open('/1.5/wesite/{{= it.prop.adrltr._id}}?inFrame=1&isWeb=1','_blank');event.preventDefault()">
        <img src="{{? it.prop.adrltr.avt}}{{= it.prop.adrltr.avt}}{{??}}/img/logo.png{{?}}" alt="{{=it.prop.addr}}" class="listing-prop-adrltr-avt">
        <div class="listing-prop-adrltr-nm">{{= it.prop.adrltr.nm}}</div>
      </span>
      {{??}}
      <span class="listing-prop-id {{? it.prop.login&&!it.prop.user}}blur{{?}}">
        {{? ((it.prop.ltp !== 'exlisting') && (it.prop.ltp !== 'assignment'))}}MLS® # {{?}}{{= it.prop.sid?it.prop.sid:it.prop.id}}
      </span>
      {{?}}
      <span class="listing-prop-address {{? it.prop.login&&!it.prop.user}}blur{{?}}">
        {{=it.prop.fullAddress}} {{= it.prop.city}}
      </span>
      {{? it.prop.rltr}}
      <span class="listing-prop-address {{? it.prop.login&&!it.prop.user}}blur{{?}}">
        {{- Brokerage}}: {{=it.prop.rltr}}
      </span>
      {{?}}
      <p class="listing-prop-rooms">
        <span class="listing-prop-room">
          <span class="fa fa-rmbed"></span>
          <span>{{= it.prop.rmbdrm || it.prop.bdrms || 0}}</span>
        </span>
        <span class="listing-prop-room">
          <span class="fa fa-rmbath"></span>
          <span>{{= it.prop.rmbthrm || it.prop.tbthrms || it.prop.bthrms || 0}}</span>
        </span>
        <span class="listing-prop-room">
          <span class="fa fa-rmcar"></span>
          <span>{{= it.prop.rmgr || it.prop.tgr || it.prop.gr || 0}}</span>
        </span>
        {{? it.prop.marketRmProp && it.prop.sqft}}
        <span class="listing-prop-room">
          <span class="fa fa-area"></span>
          {{? it.prop.sqft.toString().split('-').length > 1}}
          <span>{{= it.prop.sqft.split('-')[1]}}</span>
          {{??}}
          <span>{{= it.prop.sqft}}</span>
          {{?}}
        </span>
        {{?}}
      </p>
      <span class="listing-prop-link">
        <span>{{- Detail}}</span>
        <span class="fa fa-angle-right"></span>
      </span>
    </div>
  </a>
</div>
