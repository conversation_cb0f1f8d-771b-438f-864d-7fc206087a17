<style>
#logout-page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}
.container {
  text-align: center;
}
#title {
  margin: 30px 0 20px 0;
}
#logout-second {
  margin-left: 5px;
}
</style>
<section id="logout-page">
  <div class="container">
    <img id="logo" src="{{=req.setting.logoPath}}" alt="{{=req.setting.meta.name}}" />
    <h2 id="title">{{=- 'You are now signed out.'}}</h2>
    <p>{{=- 'You will be redirected to home in:'}} <span id="logout-second">4</span>s</p>
  </div>
</section>
<script type="text/javascript">
var secHolder = document.getElementById('logout-second');
var seconds = 4;
var timerInterval = setInterval(function() {
  seconds -= 1;
  if(seconds <= 0){
    clearInterval(timerInterval);
  }
  secHolder.innerText = seconds;
},1000);
setTimeout(function() {
  window.location.href = "/";
},4000);
</script>