{{&
  var html = it.html || {};
}}
<link rel="stylesheet" href="/web/css/common.css">
<link rel="stylesheet" href="/web/css/wecards.css">

<div id="wecardDetails">
  <div class="qrcode-container">
    <div id="qrcode"></div>
    <div class="qrcode-text">{{- Scan QR-Code Open in Mobile }}</div>
  </div>
  <div class="container detail-container">
    <div class="detail-left col-sm-8 col-xs-12">
      <h1 style="font-size:30px">{{=html.title}}</h1>
      <p>
        <span>{{=html.author}}</span>
        <span class="margin-left-5">{{=html.time}}</span>
      </p>
      {{?html.isAdmin}}
      <p class="delete">{{- Delete}}</p>
      {{?}}
      <div class="detail-content">{{=html.content}}</div>
    </div>
    <div class="col-sm-4 col-xs-12">
      {{?(html.recommendCardsPart1 && html.recommendCardsPart1.length)}}
      {{? html.userInfo}}
      <div class="recommend-user-info">
        <div class="center">
          <img class="recommend-avtar" src="{{=html.userInfo.avt}}" />
        </div>
        <div class="recommend-author">
          <span>{{=html.author}}</span>
        </div>
        {{?html.isVipUser}}

        {{?html.userInfo.tel}}
        <div class="center">
          <a class="phone" href="tel:+1{{=html.userInfo.tel}}">{{=html.userInfo.tel}}</a>
        </div>
        {{?}}

        {{?html.userInfo.eml}}
        <div class="center">
          <a class="phone" href="mailto:{{=html.userInfo.eml}}">{{=html.userInfo.eml}}</a>
        </div>
        {{?}}

        {{?html.userInfo.cpny}}
        <div class="recommend-desc">{{=html.userInfo.cpny}}</div>
        {{?}}

        {{?}}

        {{?html.userInfo.desc}}
        <div class="recommend-desc">{{=html.userInfo.desc}}</div>
        {{?}}

      </div>
      {{?}}
      {{* 'wecard/recommendList' {recommendCardsPart:html.recommendCardsPart1,host:html.host} }}

      {{?it.ad}}
      <div class="margin-top-20 pull-left">
        <a href="/adJump/{{=it.ad._id}}" target="_blank" style="display:inline-block !important;">
          <img src="{{=it.ad.src}}" width="100%" height="100%"/>
          {{?it.ad.impImgUrl}}
          <img src="{{=it.ad.impImgUrl}}" width="1" height="1" style="width:1px; height:1px; display:inline"/>
          {{?}}
        </a>
      </div>
      {{?}}

      <div class="margin-top-20 margin-bottom-20 pull-left">
        <a href="/app-download?lang={{=it.req.locale()}}">
          <img src="/web/imgs/download.png" width="100%" height="100%"/>
        </a>
      </div>

      {{?html.recommendCardsPart2 && html.recommendCardsPart2.length}}
      {{* 'wecard/recommendList' {recommendCardsPart:html.recommendCardsPart2,host:html.host} }}
      {{?}}

      {{?html.hasFb}}
      <div class="feedback-wrapper">
        <signupform :owner="owner" :user-form="userForm" :is-web="true" :title="title"></signupform>
      </div>
      {{?}}

      {{?}}
    </div>
  </div>
</div>
<script>
  vars = {
    wpid:"{{=html.wpid}}",
    img:"{{=html.img}}",
    fbtl:"{{=html.fbtl}}"
  };
  $('.delete').on('click',function(){
    var data = {cmd:'del',id:vars.wpid};
    $.post('/wpnews/update',data,(ret)=>{
      alert(JSON.stringify(ret));
    });
  });
</script>
<script src="/web/packs/commons.js"></script>
<script src="/web/packs/wecardDetails.js"></script>