<meta charset="UTF-8">
<meta content="yes" name="apple-mobile-web-app-capable" />
<meta name="viewport" content="width=device-width,height=device-height,initial-scale=1.0,maximum-scale=1.0,user-scalable=no">
<meta http-equiv="x-ua-compatible" content="ie=edge"/>
<meta name="referrer" content="same-origin">
{{? req.setting && req.setting.meta}}
<link rel="icon" type="image/png" href="{{=req.setting.meta.favicon}}">
{{?}}
{{?it.noSEOIndex}}
<meta name="robots" content="noindex">
{{?}}
{{?it.canonicalUrl}}
<link rel="canonical" href="{{=it.canonicalUrl}}"/>
{{?}}
{{?it.alternateUrls}}
{{~ it.alternateUrls :item:idx}}
<link rel="alternate" hreflang="{{=item.lang}}" href="{{=item.url}}" />
{{~}}
{{?}}
<title>{{=it.title}}</title>
<meta name="description" content="{{=it.description}}">
{{?it.keywords}}
<meta name="keywords" content="{{=it.keywords}}">
{{?}}
{{?it.author}}
<meta name="author" content="{{=it.author}}">
{{?}}
<meta property="og:title" content="{{=it.title}}">
<meta property="og:description" content="{{=it.description}}">
{{?it.image}}
<meta property="og:image" content="{{=it.image}}">
<meta name="twitter:image" content="{{=it.image}}" />
{{?}}
<meta property="og:site_name" content="{{=it.host}}">  
<meta property="og:url" content="{{=it.host+it.url}}">
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="{{=it.title}}">
<meta name="twitter:description" content="{{=it.description}}">
{{?it.geoRegion}}
<meta name="geo:region" content="{{=it.geoRegion}}">
{{?}}
{{?it.geoPlaceName}}
<meta name="geo:placename" content="{{=it.geoPlaceName}}">
{{?}}
{{?it.geoPosition}}
<meta name="geo:position" content="{{=it.geoPosition}}">
{{?}}