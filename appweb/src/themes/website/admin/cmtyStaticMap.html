<style>
  #mapbox-container {
    width: 640px;
    height: 425px;
  }
  .selected {
    border: 1px solid red;
  }
</style>
<h1>Generate Community Static Image for dev only</h1>
<p>After generate images, use imgstock.sh to compress/uncompress imgstocks/cmtys imagets</p>
<form action="/1.5/prop/cmtyStaticMap" method="GET">
  <input placeholder="search by community name" name="nm" value="{{=it.id||it.cmty._id}}" />
  <button>Search</button>
</form>
<button id="gCurrentImage">Generate Current Cmty Image</button>
<div>City:<input id="city" value="{{=it.cmty.city}}"/> Prov:<input id="prov" value="{{=it.cmty.prov}}"/></div>
<div>
  <button id="gAllImages">Generate all Cmty Images</button>
  <span>From</span>
  <span class="fromCol selected" id="stat_uname">stat_uname</span>
  <span class="fromCol" id="boundary">boundary</span>
</div>
<p id="bbox"></p>
<p id="zoom"></p>
<div id="mapbox-container">

</div>

{{?it.cmty}}
<h3>Boundary Image</h3>
{{?it.cmty.b}}
<div class="statcImage">
  <div>Image with boundary</div>
  <p>Bbox: {{=it.cmty.b.bbox}}</p>
  <p>Zoom: {{=it.cmty.b.zoom}}</p>
  <img src="/img/imgstocks/cmtys/{{=it.cmty.b.fn}}.jpg" />
</div>
{{?}}
{{?it.cmty.z}}
{{?it.cmty.o}}
<div class="statcImage">
  <div>Image without boundary</div>
  <p>Bbox: {{=it.cmty.o.bbox}}</p>
  <p>Zoom: {{=it.cmty.o.zoom}}</p>
  <img src="/img/imgstocks/cmtys/{{=it.cmty.o.fn}}.jpg" />
</div>
{{?}}
<div class="statcImage">
  <div>Image with larger zoom</div>
  <p>Bbox: {{=it.cmty.z.bbox}}</p>
  <p>Zoom: {{=it.cmty.z.zoom}}</p>
  <img src="/img/imgstocks/cmtys/{{=it.cmty.z.fn}}.jpg" />
</div>
{{?}}
{{??}}
<p>No Community Found</p>
{{?}}

<div class="data" style="display: none;">
  <pre id="cmty" data-role="module-args">{{= it.jsonCmty}}</pre>
</div>
<script src="https://api.tiles.mapbox.com/mapbox-gl-js/v3.5.1/mapbox-gl.js"></script>
<link href='https://api.mapbox.com/mapbox-gl-js/v3.5.1/mapbox-gl.css' rel='stylesheet' />
<script src="/js/map/mapbox.min.js"></script>
<script src="/js/admin/cmtyStaticMap.min.js"></script>
