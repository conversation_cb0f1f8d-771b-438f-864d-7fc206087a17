<div class="container">
{{* 'components/breadcrumbs' it.crumbs}}
<div class="cityList">
    <div class="data">{{= it.data ||""}}</div>
    <div class="cityArea">
        <div class="cityAreaLeft">
            <span class="fa fa-map-marker"></span>
            <p class="nopadding">{{= it.cp||""}}</p>
        </div>
        <span class="fa fa-caret-left caretBottom"></span>
        <div class="cityOptions">
            {{& for(var k in it.globalCityList) { }}
            {{~ it.globalCityList[k].city :detail:i}}
            <li><a href="{{uri /new-condos/}}{{=detail.url}}">{{= detail.url}}</a></li>
            {{~}}
            {{&} }}
        </div>
    </div>
</div>
<div class="titleDescribe ">
  <div class="">
    <h1 class="bigTitle">
      {{=it.bigTitle}}
    </h1>
    <p class="condos-discript">
      {{=it.condoDesc}}
    </p>
  </div>
  <div class="form">
    <div class="selectTypeArea">
      <p> {{=- it.tp||'Type'}} <span class="fa fa-caret-left caretBottom"></span></p>
      <ul class="selectTypeOptions">
        <li><a href="{{uri /new-condos/}}{{= it.cp}}">{{- All}}</a></li>
        <li><a href="{{uri /new-condos/}}{{= it.cp}}?tp=Condo">{{- Condo}}</a></li>
        <li><a href="{{uri /new-condos/}}{{= it.cp}}?tp=Townhouse">{{- Townhouse}}</a></li>
        <li><a href="{{uri /new-condos/}}{{= it.cp}}?tp=Detached">{{- Detached}}</a></li>
        <li><a href="{{uri /new-condos/}}{{= it.cp}}?tp=Semi-Detached">{{- Semi-Detached}}</a></li>
        <li><a href="{{uri /new-condos/}}{{= it.cp}}?tp=Office">{{- Office}}</a></li>
        <li><a href="{{uri /new-condos/}}{{= it.cp}}?tp=Retail">{{- Retail}}</a></li>
      </ul>
    </div>
    <div class="selectModeArea">
      <p> {{= it.thisCityInfo.mode.v}} <span class="fa fa-caret-left caretBottom"></span></p>
      <ul class="selectModeOptions">
        {{& for(var k in it.selectModeOption) { }}
        <li><a href="{{uri /new-condos/}}{{= it.cp}}?mode={{= k}}">{{= it.selectModeOption[k]}}</a></li>
        {{& } }}
      </ul>
    </div>
  </div>
</div>
<div class="condosList row">
    {{~ it.condoList :value:index}}
    <div class="condo  col-sm-4 col-xs-12">
        <a href="{{= value.url}}">
            <div class="image">
                <img src="{{? value.img&&value.img.l}}{{= value.img.l[0]}}{{??}}/img/no-photo.png{{?}}" alt="Failed to load" style='background: url(/img/no-photo.png) no-repeat;background-size: 100% 100%;'>
                {{? value.topWeb}}
                <p class="onTop">{{- TOP}}</p>
                {{?}}
                {{? it.thisCityInfo.mode.k == 'soon' && (value.closingDate.y || value.closingDate.m)}} 
                <p class="closing-date">{{= value.closingDate.y}}.{{= value.closingDate.m}}</p>
                {{?}}
            </div>
            <div class="information {{? value.spuidsWeb && value.spuidsWeb.length>0}}hasAgent{{?}}">
                <div class="bigTit" style="height:40px">
                    <a href="{{= value.url}}"><b>{{= value.nm||""}}</b></a>
                    {{?value.spuidsWeb && value.spuidsWeb.length}}
                    <div class="agent">
                        <div class="agentPhoto"><a href="{{? value.spuidsWeb}}/1.5/wesite/{{= value.spuidsWeb[0]}}?inFrame=1&isWeb=1{{?}}"><img src="{{= value.spuAvt ||'/img/no-photo.png'}}" alt=""></a></div>
                        <p class="agentName">{{= value.spuNm||""}}</p>
                    </div>
                    {{?}}
                </div>
                <div class="describe borderBottom">
                    <p>{{= value.desc||""}}</p>
                </div>
                <div class="location">
                    <p>{{=- value.city}},{{= it.pr}}</p>
                    <p>{{- Developer}}：{{= value.builder ||""}}</p>
                    {{? value.saleStatus}}
                    <div class="registration">
                        <a href="{{= value.url}}">{{=- value.saleStatus}}</a>
                    </div>
                    {{?}}
                </div>
            </div>
        </a>
    </div>
    {{~}}
    {{? it.thisCityInfo.noCondos}}
    <p style="padding: 50px 3%;">{{- Sorry , We can not find more condo list}}</p>
    {{?}}
</div>
{{? it.thisCityInfo.noCondos}}{{??}}
<div class="selectButton">
    <li class="pagingStep first">{{? it.paging.prev}}<a
            href="{{= it.paging.pageUrl}}{{= it.paging.prev}}">{{?}}<span
                class="fa fa-caret-left"></span>{{? it.paging.prev}}</a>{{?}}</li>
    <li class="pagingStep">{{? it.paging.next}}<a
            href="{{= it.paging.pageUrl}}{{= it.paging.next}}">{{?}}<span
                class="fa fa-caret-right"></span>{{? it.paging.next}}</a>{{?}}</li>
</div>
{{?}}
</div>
<script src="/web/packs/commons.js"></script>
<script src="/web/packs/autoCompleteSearch.js"></script>
{{* 'newcondos/common.js' it}}