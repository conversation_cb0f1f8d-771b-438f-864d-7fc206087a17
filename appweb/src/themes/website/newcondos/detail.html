{{&
	var {ldmode,highlight,data,crumbs,proj} = it;
	var isHighLight = highlight ? 'isHighLight' : '';
}}
{{?highlight}}
<style>
.condoBottomContent.isHighLight {
	max-height: {{=287 * highlight}}px;
}
</style>
{{?}}
{{? ldmode }}
{{* 'newcondos/components/imageSlider' {proj,ldmode} }}
{{?}}
<div class="detail container">
	{{? !ldmode}}
	{{* 'components/breadcrumbs' crumbs}}
	{{?}}
	<div class="data">{{= data}}</div>
	{{? !ldmode }}
	<div class="condoDetailTop row">
		<div class="col-md-7 col-sm-7 col-xs-12 condoImgsContainer">
			{{* 'newcondos/components/imageSlider' {proj} }}
		</div>
		<div class="col-md-5 col-sm-5 col-xs-12">
			{{* 'newcondos/components/detailInfoBox' {proj} }}
		</div>
	</div>
	{{?}}
  <div class="condoDetailBottom row">
    <div class="condoBottomLeft col-sm-7 col-xs-12">
      {{?ldmode}}
        <div class="highlight infobox">
          {{* 'newcondos/components/detailInfoBox' {proj} }}
        </div>
      {{?}}
      <div class="highlight borderNoBg">
        <div class="condoBottomTit">
          <h2>{{- Highlight}}</h2>
          <div class="condoStatus">{{- STATUS}}：{{= proj.saleStatus||" "}}</div>
          {{? proj.html}}
          <span class="glyphicon glyphicon-minus"></span>
          <span class="glyphicon glyphicon-plus"></span>
          {{?}}
        </div>
        <div class="buildingInfo">
          <div class="buildingInfomation col-md-4 col-sm-6 col-xs-6">
            <img src="/img/newCondoIcons/Icon-condo-type.png" alt="Faild">
            <div class="buildingTxt">
              <h4 class="littleTit">{{- Type}}</h4>
              <b>{{? proj.tp1}}{{=- proj.tp1}}{{??}}-{{?}}</b>
            </div>
          </div>
          <div class="buildingInfomation col-md-4 col-sm-6 col-xs-6">
            <img src="/img/newCondoIcons/Icon-condo-close.png" alt="Faild">
            <div class="buildingTxt">
              <h4 class="littleTit">{{- EST.Closing}}</h4>
              <b>{{? proj.closingDate.y}}{{= proj.closingDate.y}}
                {{? proj.closingDate.m}}.{{= proj.closingDate.m}}{{??}} {{?}}
                {{? proj.closingDate.d}}.{{= proj.closingDate.d}}{{??}} {{?}}
                {{??}}-{{?}}</b>
            </div>
          </div>
          <div class="buildingInfomation col-md-4 col-sm-6 col-xs-6">
            <img src="/img/newCondoIcons/Icon-condo-sale.png" alt="Faild">
            <div class="buildingTxt">
              <h4 class="littleTit">{{- Sales Start}}</h4>
              <b>{{? proj.saleStartDate.y}}{{= proj.saleStartDate.y}}
                  {{? proj.saleStartDate.m}}.{{= proj.saleStartDate.m}}{{??}} {{?}}
                  {{? proj.saleStartDate.d}}.{{= proj.saleStartDate.d}}{{??}} {{?}}
                  {{??}}-{{?}}</b>
            </div>
          </div>
          <div class="buildingInfomation col-md-4 col-sm-6 col-xs-6">
            <img src="/img/newCondoIcons/Icon-condo-unit.png" alt="Faild">
            <div class="buildingTxt">
              <h4 class="littleTit">{{- Units}}/{{- Stories}}</h4>
              <b>{{? proj.units}}{{= proj.units}}{{??}} {{?}}/{{? proj.stories}}{{= proj.stories}}{{??}} {{?}}</b>
            </div>
          </div>
          <div class="buildingInfomation col-md-4 col-sm-6 col-xs-6">
            <img src="/img/newCondoIcons/Icon-condo-size.png" alt="Faild">
            <div class="buildingTxt">
              <h4 class="littleTit">{{- Size Range}}</h4>
              <b>{{? proj.srange}}{{= proj.srange}}{{??}}-{{?}}</b>
            </div>
          </div>
          <div class="buildingInfomation col-md-4 col-sm-6 col-xs-6">
            <img src="/img/newCondoIcons/Icon-condo-price.png" alt="Faild">
            <div class="buildingTxt">
              <h4 class="littleTit">{{- Price Range}}</h4>
              <b>{{? proj.lpf}} $ {{= proj.lpf}} <span style="font-size: 12px;">/SpFt</span>{{??}}-{{?}}</b>
            </div>
          </div>
        </div>
        {{? proj.html}}
        <hr style="border-top: 0.5px solid #eee;">
        <div class="condoBottomContent seeMoreInfo {{=isHighLight}}">
          <div>{{= proj.html}}</div>
          <div class="coverMask"></div>
        </div>
        <div class="details">
          <span>{{-, More Detail,condo}}</span><span>{{-, Less Details,condo}}</span>
        </div>
        {{?}}
      </div>
      <div class="highlight borderNoBg">
        <div class="condoBottomTit">
          <h2>{{- Factors & Price}}</h2>
          <span class="glyphicon glyphicon-minus"></span>
          <span class="glyphicon glyphicon-plus"></span>
        </div>
        <div class="condoBottomContent seeMoreInfo {{=isHighLight}}">
          <ul class="nopadding">
            <li> {{? proj.builder}}
              <h5 class="condoBottomSmallTit">{{- Builder}}:</h5>
              <p class="condoBottomText">{{= proj.builder}}</p>{{?}}
            </li>
            <li>{{? proj.tp1}}
              <h5 class="condoBottomSmallTit">{{- Type}}:</h5>
              <p class="condoBottomText">{{=- proj.tp1}}</p>{{?}}
            </li>
            <li>{{? proj.ownership}}
              <h5 class="condoBottomSmallTit">{{- Ownership}}:</h5>
              <p class="condoBottomText">{{= proj.ownership}}</p>{{?}}
            </li>
            <li>{{? proj.units}}
              <h5 class="condoBottomSmallTit">{{- Units}}:</h5>
              <p class="condoBottomText">{{= proj.units}}</p>{{?}}
            </li>
            <li>{{? proj.storise}}
              <h5 class="condoBottomSmallTit">{{- Stories}}:</h5>
              <p class="condoBottomText">{{= proj.storise}}</p>{{?}}
            </li>
            <li>{{? proj.srange}}
              <h5 class="condoBottomSmallTit">{{- Size Range}}:</h5>
              <p class="condoBottomText">{{= proj.srange}}</p>{{?}}
            </li>
            <li> {{? proj.amen}}
              <h5 class="condoBottomSmallTit">{{- Amenities}}:</h5>
              <p class="condoBottomText">{{~ proj.amenS :value}}
                <em>{{= value}}</em>
                {{~}}
              </p>
              {{?}}
            </li>
            <li>{{? proj.desc}}
              <h5 class="condoBottomSmallTit">{{- Description}}:</h5>
              <p class="condoBottomText">{{= proj.desc}}</p>{{?}}
            </li>
            <li>{{? proj.gr_m}}
              <h5 class="condoBottomSmallTit">{{- Parking}}:</h5>
              <p class="condoBottomText">{{= proj.gr_m}}</p>{{?}}
            </li>
            <li>{{? proj.lp_lckr}}
              <h5 class="condoBottomSmallTit">{{- Locker}}:</h5>
              <p class="condoBottomText">{{= proj.lp_lckr}}</p>{{?}}
            </li>
            <li>{{? proj.mfee_m}}
              <h5 class="condoBottomSmallTit">{{- Maint Fee}}:</h5>
              <p class="condoBottomText">{{= proj.mfee_m}}</p>{{?}}
            </li>
            <li>{{? proj.deposit_m}}
              <h5 class="condoBottomSmallTit">{{- Deposit}}:</h5>
              <p class="condoBottomText">{{= proj.deposit_m}}</p>{{?}}
            </li>
          </ul>
          <div class="coverMask"></div>
        </div>
        <div class="details">
          <span>{{-, More Detail,condo}}</span><span>{{-, Less Details,condo}}</span>
        </div>
      </div>
      {{? proj.floorPlanList}}
      {{? proj.floorPlanList.length && proj.floorPlanList[0].name}}
      <div class="highlight borderNoBg">
        <div class="condoBottomTit">
          <h2>{{- Floor Plans & Pricing}}</h2>
        </div>
        <div class="floorPlan">
          <ul class="nopadding">
            {{~ proj.floorPlanList :value :index}}
            {{? value.name}}
            <li class="borderTop">
              <div class="plan">
                <img src="{{? value.images&&value.images[0]}}{{= value.images[0]}}{{??}}/img/noPic.png{{?}}" alt="">
              </div>
              <div class="pricing">
                <h5 class="condoBottomSmallTit">{{= value.name}}</h5>
                <div class="condoBottomText">
                  <p>{{= value.rmbdrm || value.tbdrms || value.bdrms}} {{- Bedroom}} | {{= value.rmbthrm || value.tbthrms || value.bthrms}} {{- Bathroom}} </p>
                  <p>{{- Plan Price}}: {{- From}} ${{= value.planpr}}</p>
                  <p>{{- Interior Size}}: {{- From}} {{= value.inters}} Sqft</p>
                </div>
              </div>
            </li>
            {{?}}
            {{~}}
          </ul>
        </div>
      </div>
      {{?}}
      {{?}}
    </div>
    <div class="condoBottomRight col-sm-5 col-xs-12" style="position: sticky;top: 70px;">
      <div class="highlight borderNoBg">
        <div class="condoBottomTit">
          <h2>{{- Request Floor Plan & More Info}}</h2>
        </div>
        <div>
          {{? proj.agentsWeb}}
          {{~ proj.agentsWeb :value:index}}
          <div class="condoAgent">
            {{? value.fnm}}
            <div class="condoAgentImg">
              <a href="/1.5/wesite/{{=value.uid}}?inFrame=1&isWeb=1">
                <img src="{{? value.avt}}{{= value.avt}}{{??}}/img/noPic.png{{?}}" alt="">
              </a>
            </div>
            {{?}}
            <div class="condoAgentName pricing">
              <h5 class="condoBottomSmallTit" style="margin-bottom:2px;"><a href="/1.5/wesite/{{=value.uid}}?inFrame=1&isWeb=1">{{= value.fnm}}</a> <span class="hasAuthenticated">{{- Verified}}</span></h5>
              <div class="condoBottomText" style="margin-bottom: 4px;">
                {{?value.itr}}
                <p>{{= value.itr}}</p>
                {{?}}
                <p>{{? value.cpny}}{{= value.cpny}}{{??}}{{= value.cpny_zh}}{{?}}</p>
              </div>
              <!-- <div class="condoAgentPhone">
                <a class="agentPhone" href="tel:{{value.mbl}}"><span class="fa fa-volume-control-phone"></span>{{= value.mbl||"NA"}}</a>
                <div class="hasRedBg"><a href="/1.5/wesite/{{= value.uid}}?inFrame=1&isWeb=1">{{- VIEW PROFILE}}</a></div>
              </div> -->
            </div>
          </div>
          {{~}}
          {{?}}
          <div class="condoSubmit" id="condoSubmit">
            <div class="input">
                <span class="require">*</span><input type="text" placeholder="{{- Your Full Name}}" name="nm" id="nm" maxlength="20"></div>
            <div class="input">
                <span class="require">*</span><input type="text" placeholder="{{- Email}}" name="eml" id="eml"></div>
            <div class="input">
                <span class="require">*</span><input type="text" placeholder="{{- Phone Number}}" name="mbl" id="mbl" maxlength="11" oninput="value=value.replace(/[^\d]/g,'')"></div>
            {{?['zh-cn','zh'].includes(it.lang)}}
            <div class="input">
                {{?proj.isChinaIP}}
                <span class="require">*</span>
                {{?}}
                <input type="text" placeholder="{{- WeChat ID}}" id="wxid" name="wx" maxlength="30"></div>
            {{?}}
            <div class="input">
                <textarea style="resize: none;overflow: hidden;" id="m" placeholder="{{- I would like more information regarding a property.}}"></textarea>
            </div>
            <button class="send hasRedBg" id="submit">{{- SEND}}</button>
          </div>
          <div class="succeed-message-box">
            <p class="succeed-message-inner">{{- Your feedback has been submitted.}}</p>
          </div>
        </div>
      </div>
    </div>
	</div>
  <div id="goformInput">
    <span>{{- Register Now}}</span>
  </div>
	<div class="flash-message-box">
		<p class="flash-message-inner"></p>
	</div>
</div>
<script>
  var isChinaIP = '{{=proj.isChinaIP?'1':''}}';
</script>
{{* 'newcondos/common.js' it}}
