{{?it.swiper}}
<link rel="stylesheet" type="text/css" href="/css/swiper.min.css">
<script src="/js/swiper.min.js"></script>
{{?}}
{{?it.page == 'home'}}
<link rel="stylesheet" href="/fonts/home/<USER>" type="text/css" media="all">
{{??}}
<link rel="stylesheet" href="/css/font-awesome.min.css" type="text/css" media="all">
{{?}}
<link rel="stylesheet" href="/css/web/bootstrap.css?v={{=it.assetVersion}}" type="text/css" media="all">
<link rel="stylesheet" href="/css/web/style-common.css?v={{=it.assetVersion}}" type="text/css" media="all">
{{?it.page}}
<link rel="stylesheet" href="/css/web/{{=it.page}}.css?v={{=it.assetVersion}}" type="text/css" media="all">
{{?}}
<script type="text/javascript">lang = "{{=it.lang}}"</script>
{{?it.mapboxkey}}
<script src="https://api.tiles.mapbox.com/mapbox-gl-js/v3.5.1/mapbox-gl.js"></script>
<link rel="stylesheet" href="https://api.tiles.mapbox.com/mapbox-gl-js/v3.5.1/mapbox-gl.css" type="text/css" media="all">
<script type="text/javascript">mapboxgl.accessToken = "{{=it.mapboxkey}}";</script>
{{?}}
{{?it.ratchet}}
<script src="/js/ratchet.min.js"></script>
<link rel="stylesheet" type="text/css" href="/css/ratchet.min.css">
{{?}}
{{?it.angular}}
<script src="/js/angular.min.js"></script>
{{?}}
{{?it.hasQrcode}}
<script src="/web/packs/qrcode.min.js"></script>
{{?}}
<script src="/js/rmapp.min.js"></script>
<script src="/js/srv.min.js"></script>
<script src="/js/jquery-2.1.1.min.js"></script>
{{? it.isSafari}}
  {{* 'json-ld/safariIndex' it}}
{{??}}
  {{* 'json-ld/index' it}}
{{?}}