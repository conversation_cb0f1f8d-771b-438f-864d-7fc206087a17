<link rel="stylesheet" href="/css/apps/showing.min.css">
<link rel="stylesheet" href="/css/sprite.min.css">
<div onload="onLoad">
<div>
  <header class="bar bar-nav">
    {{? it.d}}
    <a class="icon fa fa-back pull-left" onClick="goBack2('{{= it.d}}',{{= it.isPopup}})"></a>
    {{?}}
    <h1 class="title">{{- Showing}} {{= it.showing.dt}}
     </h1>
  </header>
</div>
<div class="showing-share-content">
  {{? it.agent && it.agent.fnm}}
  <div class="condoAgent">
    <div class="condoAgentImg">
      <a href="/1.5/wesite/{{=it.agent._id}}">
        <img src="{{? it.agent.avt}}{{= it.agent.avt}}{{??}}/img/logo.png{{?}}" onerror="e => {e.target.src = '/img/logo.png'}" alt="">
      </a>
    </div>
    <div class="condoAgentName pricing">
      <div class="condoBottomSmallTit">
        <span>{{= it.agent.fnm}}</span>
        {{? it.agent.vip}}<span class="hasAuthenticated">{{-, Verified,condo}}</span>{{?}}
      </div>
      <div class="condoBottomText">
        <p>{{? it.agent.cpny}}{{= it.agent.cpny}}{{?}}</p>
      </div>
    </div>
    <div class="condoAgentPhone">
      <div><a href="tel:{{= it.agent.mbl}}">{{- Contact}}</a></div>
    </div>
  </div>
  {{??}}
  <div class="condoAgent">
    {{-, Agent is not found ,showing}}
  </div>
  {{?}}
  {{? it.showing.m}}
    <div class="memos shareM">
    <!-- {{? it.showing.stPt.faddr}}
      <div class="stPt">
        <div class="start-label-wrapper">
          <span class="label"> {{-, Start Location ,showing}}</span>
          <a href="{{= it.showing.startPointMapLink}}" target="_blank" class="start-direction drive pull-right" onclick="trackEventOnGoogle('showing','directionInShare')">
            <span class="sprite16-18 sprite16-3-9 margin-right-3"></span>
            {{- Direction}}
          </a>
        </div>
        <div>
          {{= it.showing.stPt.addr}}, {{= it.showing.stPt.city}},{{= it.showing.stPt.prov}}
        </div>
      </div>
    {{?}} -->
    {{? it.showing.m}}
      <div class="shareM"> {{-, Memo ,showing}} : {{= it.showing.m}} </div>
    {{?}}
    </div>
  {{?}}
  {{~ it.showing.props :prop:index}}
  {{? prop}}
  <div class="propCard">
    {{? prop.stT}}
    <div class="sort">{{= index+1}}</div>
    {{?}}
    {{? prop.id||prop.sid}}
    <div onclick="openDetail({{=it.isApp}},'{{=it.lang}}','{{=prop.id||prop._id}}','{{? it.agent && it.agent._id}}{{=it.agent._id}}{{?}}')" class="prop-detail-wrapper">
      <div class="img-wrapper">
        <img class='card-la-avt' src="{{? prop.thumbUrl}}{{= prop.thumbUrl}}{{??}}/img/noPic.png{{?}}" onerror="e => {e.target.src = '/img/noPic.png'}">
        <span class="id">
          <span class="sid">
            {{? prop.id && /^RM/.test(prop.id)}}
            {{= prop.id }}
            {{? prop.ml_num || prop.sid}}
            <span class="sid">
              {{= prop.ml_num || prop.sid}}
            </span>
            {{?}}
            {{??}}
            {{= prop.sid || prop.id }}
            {{?}}
          </span>
        </span>
      </div>
      <div class="prop">
        <div class="addr">
          {{? prop.unt}}
          <span>{{= prop.unt}}</span>
          {{?}}
          {{= prop.addr}}
        </div>
        <div class="price-wrapper">
          {{? prop.priceValStrRed}}
          <span class="price">{{= prop.priceValStrRed}}</span>
          {{?? prop.askingPriceStr}}
          <span class="price">{{= prop.askingPriceStr}}</span>
          {{?}}

          {{? prop.priceValStrRedDesc}}
          <span class="price {{? (prop.lst == 'Sld' || prop.lst == 'Lsd')}}through{{?}}">
            {{? (prop.lst == 'Sld' || prop.lst == 'Lsd')}}{{= prop.askingPriceStr}}{{??}}{{= prop.priceValStrRedDesc}}{{?}}
          </span>
          {{?}}
          {{? prop.saletp && prop.status == 'A'}}
          <span class="stp">
            {{=- prop.saletp || prop.lpunt}}
          </span>
          {{?}}
          {{? prop.saletp && prop.status !== 'A'}}
          <span class="stp sold">
            {{? prop.lst == 'Sld' || prop.lst == 'Lsd'}}
            <span>
              {{? prop.saletp == 'Sale'}}
              <span>{{- Sold}}</span>
              {{??}}
              <span>{{- Leased}}</span>
              {{?}}
            </span>
            {{??}}
            <span>{{- Inactive}}</span>
            {{?}}
          </span>
          {{?}}
        </div>
        <div class="bdrms">
          {{? prop.bdrms}}
          <span class="fa fa-rmbed"></span>
          <span>{{= prop.bdrms}}</span>
          {{?}}
          {{? prop.rmbthrm || prop.tbthrms || prop.bthrms}}
          <span class="fa fa-rmbath"></span>
          <span>{{= prop.rmbthrm || prop.tbthrms || prop.bthrms}}</span>
          {{?}}
          {{? prop.rmgr || prop.gr || prop.tgr}}
          <span class="fa fa-rmcar"></span>
          <span>{{= prop.rmgr || prop.tgr || prop.gr}}</span>
          {{?}}
        </div>
      </div>
    </div>
    {{??}}
    <div class="prop-detail-wrapper-map">
      <p>{{= prop.addr}} , {{= prop.city}}</p>
      <p>{{= prop.prov}}
      </p>
    </div>
    {{?}}
    <div class="memo-wrapper">
      <div class="showing-time-wrapper">
        <div class="showing-time-start-time">
          {{? prop.stT}}
          <span class="sprite16-18 sprite16-1-6 margin-right-3"></span>
          <span>{{= prop.stT}} - {{= prop.endT}}</span>
          {{??}}
            {{? prop.failed}}
            <span class="fa fa-circle margin-right-3 failed"></span>
            <span>{{- Booking Failed}}</span>
            {{??}}
            <span class="fa fa-circle margin-right-3"></span>
            <span>{{-, Unscheduled,showing}}</span>
            {{?}}
          {{?}}
        </div>
        <span class="carAndDist">
          {{? prop.drvMin > 0 && prop.drvDis > 0}}
          <span class="sprite16-18 sprite16-3-2 margin-right-3" style='vertical-align: top;margin-right: 3px;'></span>
          <span class="dist">{{= prop.drvDis}}{{-, km,distance}} {{= prop.drvMin}}{{-, min,time}} </span>
          {{?}}
        </span>
        <a href="/1.5/map/selectMap?id={{= prop._id}}" class="drive pull-right" onclick="trackEventOnGoogle('showing','directionInShare')">
          <span class="sprite16-18 sprite16-3-9 margin-right-3"></span>
          {{- Direction}}
        </a>
      </div>
      {{? prop.m}}
      <div class="share-memo">
        <p>{{-, Memo ,showing}} : {{= prop.m}} </p>
      </div>
      {{?}}
    </div>
  </div>
  {{?}}
  {{~}}
</div>
{{? it.showing.lbl != true}}

  <div id='add-to-calendar' {{?it.showing.isWeChat}} class="add-to-calendar-from-WeChat" {{?}}>
    <label class="add-to-calendar-label">{{- Add to Calendar}}</label>
    <div class="add-to-calendar-button-wrapper google">
      <div class="add-to-calendar-button" onclick="addToCalendar({{=it.showing.isWeChat}},true)" >
        <img src="/img/google-icon.png"></img>
        <span class="text">Google</span>
      </div>
    </div>
    {{?it.isSafari || it.showing.isWeChat}}
    <div class="add-to-calendar-button-wrapper apple">
      <div class="add-to-calendar-button" onclick="addToCalendar({{=it.showing.isWeChat}},false)" >
        <img src="/img/apple-icon.png"></img>
        <span class="text">Apple</span>
      </div>
    </div>
    {{?}}
  </div>
{{?}}
<div class="flash-message-box">
  <span class="icon icon-close" onclick="alertMessage()"></span>
  <p class="flash-message-inner">{{- Click up-right corner WeChat menu and select "Open in Browser".}}</p>
</div>
<div class="select-time-box modal modal-60pc">
  <p>{{- Alarm}}</p>
  <div id="selectRemindTime">
    <div class="selectOpt" value="0" onclick="calenderSelected(0)">{{- None}}</div>
    <div class="selectOpt" value="5" onclick="calenderSelected(1)">{{- 5 minutes before}}</div>
    <div class="selectOpt selected" value="10" onclick="calenderSelected(2)">{{- 10 minutes before}}</div>
    <div class="selectOpt" value="15" onclick="calenderSelected(3)">{{- 15 minutes before}}</div>
    <div class="selectOpt" value="30" onclick="calenderSelected(4)">{{- 30 minutes before}}</div>
    <div class="selectOpt" value="60" onclick="calenderSelected(5)">{{- 1 hour before}}</div>
    <div class="selectOpt" value="120" onclick="calenderSelected(6)">{{- 2 hour before}}</div>
    <div class="selectOpt" value="1440" onclick="calenderSelected(7)">{{- 1 day before}}</div>
  </div>
  <div class="btns ">
    <div class="btn btn-half btn-sharp btn-fill" onclick="alertOk()">{{-, OK ,showing}}</div>
    <div class="btn btn-half btn-sharp btn-fill" onclick="toggleAlert()">{{-, Cancel ,showing}}</div>
    <div id="data" style="display: none;">{{= it.showing._id}}</div>
  </div>
</div>
<div class="WSBridge" style="display:none">
  <span id="share-title-en"> RealMaster Showing {{= it.showing.dt}} {{= it.showing.stT||""}} {{= it.showing.cNm || ''}}</span>
  <span id="share-title">{{- RealMaster}} {{-, Showing,showing}} {{= it.showing.dt}} {{= it.showing.stT||""}} {{= it.showing.cNm || ''}}</span>
  <span id="share-url">{{= it.showing.reqHost}}/1.5/showing/detail/share?showingId={{= it.showing._id }}&lang={{= it.lang}}</span>
  <span id="share-desc">{{= it.showing.scheLength}} {{-, Scheduled,showing}}</span>
  <span id="share-desc-en">{{= it.showing.scheLength}} Scheduled</span>
  <span id="share-image">{{= it.showing.reqHost}}/img/showing/shareImg.png</div>
</div>
</div>
<script src="/libs/jquery-2.2.3.min.js"></script>
<script>
  function alertOk() {
    toggleAlert();
    remindTime();
  };
  function alertMessage() {
    $('.flash-message-box').toggle();
  };
  function calenderSelected(num) {
    $('#selectRemindTime .selectOpt').eq(num).addClass('selected').siblings().removeClass('selected');
  };
  function toggleAlert() {
    $('.select-time-box').toggleClass("active");
  };
  function remindTime(isGoogle) {
    var data = $("#data").html();
    let a = document.createElement('a');
    if (!isGoogle) {
      a.href = '/1.5/showing/calendar_' + data + '.ics';
    } else {
      a.href = "{{=it.gUrl}}";
    }
    a.click();
    trackEventOnGoogle('showing', 'addToCalendarInShare');
  };
  function addToCalendar(isWeChat, isGoogle) {
    if (isWeChat) {
      return alertMessage();
    } else {
      remindTime(isGoogle);
    }
  };
  function goBack2(d,isPopup) {
    if(isPopup){
      return window.rmCall(':ctx::cancel');
    }
    if(d){
      return window.location.href = d;
    }
    else{
      window.history.back();
    }
  };
  function openDetail(isApp,lang,id,aid) {
    var url = '/1.5/prop/detail';
    if(isApp){
      url += '/inapp';
    }
    url += '?&wDl=1&share=1&';
    if(lang){
      url += `lang=${lang}&`;
    }
    if(aid){
      url += `aid=${aid}&`;
    }
    if(id){
      url += `id=${id}&`;
    }
    if (isApp) {
      RMSrv.getPageContent(url,'#callBackString', {hide:false, title: 'RealMaster'}, function(val){
        if (val == ':cancel') {
          console.log('canceled');
          return;
        }
      });
    } else {
      window.location.href = url;
    }
  };
  function showCalendar(){
    if(RMSrv.hasGoogleService){
      RMSrv.hasGoogleService((hasGoogle) => {
        if (!hasGoogle) {
          $("#add-to-calendar").hide();
        }
      });
    }
  };
  window.onload = (event) => {
    showCalendar();
  };
</script>
