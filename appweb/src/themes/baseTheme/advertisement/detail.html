<link rel="stylesheet" href="/css/apps/advertisementList.css" type="text/css"/>
<div id="adDetail" v-cloak="v-cloak">
  <flash-message></flash-message>
  <div class="bar bar-standard bar-footer"><a class="icon pull-right fa fa-save" @click="save()"></a></div>
  <div class="content content-padded">
    <div class="row">
      <div class="label">_id</div>
      <div>{{curBanner._id}}</div>
    </div>
    <div class="row">
      <div class="label">ts</div>
      <div>{{curBanner.ts}}</div>
    </div>
    <div class="row">
      <div class="label">type(page):</div>
      <div class="input">
        <select v-model="curBanner.type" @change='deleteFieldsByType()'>
          <option v-for="s in bannerTypes" v-bind:value="s.k">{{s.v}}</option>
        </select>
      </div>
    </div>
    <div class="row">
      <div class="label">Start time:</div>
      <div class="input">
        <input type="date" v-model="curBanner.st"/>
      </div>
    </div>
    <div class="row">
      <div class="label">End time:</div>
      <div class="input">
        <input type="date" v-model="curBanner.et"/>
      </div>
    </div>
    <div class="row" v-show="curBanner.st&& curBanner.et">
      <div>{{curBanner.st}} - {{curBanner.et}}</div>
      <div>| {{activeTime}} {{- Days}}</div>
    </div>
    <div class="row" v-show="isAdActive">
      <div class="btn btn-positive" @click="pause(false)" v-if='curBanner.pause == "A"'>Start</div>
      <div class="btn btn-positive" @click="pause(true)" v-else>Pause</div>
    </div>
    <div class="row" v-show="curBanner.pauseHist">
      <div class="label">History:</div>
      <div class="input history">
        <div v-for='h in curBanner.pauseHist'>{{h.pause}} {{h.ts}}</div>
      </div>
    </div>
    <div class="row" :class="{disable:(curBanner.type =='edm') || (curBanner.type == 'splash')}">
      <div class="label">order(for sort, high-low):</div>
      <div class="input">
        <input type="number" v-model="curBanner.order" :disabled="(curBanner.type =='edm') || (curBanner.type == 'splash')"/>
      </div>
    </div>
    <div class="row">
      <div class="label">lang:</div>
      <div class="input">
        <label v-for='l in language' v-show="(curBanner.type != 'edm' || (l != 'zh-cn' && l != 'kr'))">
          <input type="checkbox" v-model="curBanner[l]"/><span class="padding">{{l}}</span>
        </label>
      </div>
    </div>
    <div class="row">
      <div class="label">Area(China IP or not)</div>
      <div class="input">
        <label>
          <input type="checkbox" v-model="curBanner.china"/><span class="padding">China</span>
        </label>
        <label>
          <input type="checkbox" v-model="curBanner.nochina"/><span class="padding">Outside of China</span>
        </label>
      </div>
    </div>
    <div class="row">
      <div class="label">prov:</div>
      <div class="input">
        <div class="prov-field" v-for="p in provs" @click="selectProv(p.o_ab)" :class="{selected:isProvSelected(p.o_ab)}">{{p.n}}</div>
      </div>
    </div>
    <div class="row">
      <div class="label">grp (who can see):</div>
      <div class="input">
        <label>
          <input type="radio" name='grp' v-model="curBanner.grp" value="all"/><span class="padding">All</span>
        </label>
        <label>
          <input type="radio" name='grp' v-model="curBanner.grp" value="realtor"/><span class="padding">Realtor</span>
        </label>
        <label>
          <input type="radio" name='grp' v-model="curBanner.grp" value="notRealtor"/><span class="padding">Not realtor</span>
        </label>
        <label>
          <input type="radio" name='grp' v-model="curBanner.grp" value="noFollow"/><span class="padding">Not following</span>
        </label>
      </div>
    </div>
    <div class="row">
      <div class="label">noend(event no end day)</div>
      <div class="input">
        <input type="checkbox" v-model="curBanner.noend"/><span class="padding">no end</span>
      </div>
    </div>
    <div class="row" :class="{disable:curBanner.type != 'banner'}">
      <div class="label">show homepage</div>
      <div class="input">
        <input type="checkbox" v-model="curBanner.rm" :disabled="curBanner.type != 'banner'"/><span class="padding">market place</span>
        <input type="checkbox" v-model="curBanner.mls" :disabled="curBanner.type != 'banner'"/><span class="padding">MLS</span>
      </div>
    </div>
    <div class="info"> </div>
    <div class="row" v-show="curBanner.type == 'banner'">
      <div class="label">提示：</div>
      <div>用户必须安装了wechat才能显示banner！！！</div>
    </div>
    <div class="row" v-show="curBanner.type == 'project'">
      <div class="desc">project 列表 URL: /1.5/mapSearch?mode=list&mapmode=projects&id={PROP_ID}</div>
      <div class="desc">project 详细 URL: /1.5/prop/projects/detail?id={PROP_ID}</div>
      <div class="desc">不要填错了，如果是详细并且勾选inapp会导致app页面无法关闭</div>
    </div>
    <div class="row">
      <div class="label">redirect url:</div>
      <div class="input">
        <input type="text" v-model="curBanner.tgt"/>
      </div>
    </div>
    <div class="row" :class="{disable:(curBanner.type =='edm') || (curBanner.type == 'splash')}">
      <div class="label">inApp(点击时window .location =url)</div>
      <div class="input">
        <input type="checkbox" v-model="curBanner.inapp" @change="checkInappUrl" :disabled="(curBanner.type =='edm') || (curBanner.type == 'splash')"/><span class="padding">inapp</span>
      </div>
    </div>
    <div class="row">
      <div class="desc">image size: banner 640x180, splash 1080x2340,edm width:height=4:3</div>
      <div class="label">img src:</div>
      <div class="btn-wrapper">
        <div class="btn btn-positive" @click="openImageModel()">Add</div>
      </div>
      <div class="img-wrapper" v-if="curBanner.src"><img :src="curBanner.src" referrerpolicy="same-origin"/></div>
    </div>
    <div class="row">
      <div class="label">img src_en:</div>
      <div class="btn-wrapper">
        <div class="btn btn-positive" @click="openImageModelEn()">Add</div>
      </div>
      <div class="img-wrapper" v-if="curBanner.src_en"><img :src="curBanner.src_en" referrerpolicy="same-origin"/></div>
    </div>
    <div class="row" :class="{disable:curBanner.type != 'project'}">
      <div class="label">tl(projectl title)</div>
      <div class="input">
        <input type="text" v-model="curBanner.tl" :disabled="curBanner.type != 'project'"/>
      </div>
    </div>
    <div class="row" :class="{disable:curBanner.type != 'project'}">
      <div class="label">tl_en(projectl title english version)</div>
      <div class="input">
        <input type="text" v-model="curBanner.tl_en" :disabled="curBanner.type != 'project'"/>
      </div>
    </div>
    <div class="row" :class="{disable:(curBanner.type != 'banner')&&(curBanner.type != 'project')}">
      <div class="label">name(banner/proj name)</div>
      <div class="input">
        <input type="text" v-model="curBanner.name" :disabled="(curBanner.type != 'banner')&&(curBanner.type != 'project')"/>
      </div>
    </div>
    <div class="row" :class="{disable:(curBanner.type != 'banner')&&(curBanner.type != 'project')}">
      <div class="label">name_en(banner/proj name)</div>
      <div class="input">
        <input type="text" v-model="curBanner.name_en" :disabled="(curBanner.type != 'banner')&&(curBanner.type != 'project')"/>
      </div>
    </div>
    <div class="row" :class="{disable:curBanner.type != 'project'}">
      <div class="label">builder(project builder)</div>
      <div class="input">
        <input type="text" v-model="curBanner.builder" :disabled="curBanner.type != 'project'"/>
      </div>
    </div>
    <div class="row" :class="{disable:curBanner.type != 'project'}">
      <div class="label">city(project city)</div>
      <div class="input">
        <input type="text" v-model="curBanner.city" :disabled="curBanner.type != 'project'"/>
      </div>
    </div>
    <div class="row" :class="{disable:(curBanner.type != 'edm')&&(curBanner.type != 'project')}">
      <div class="label">desc(project/edm desc chinese version)</div>
      <div class="input">
        <input type="text" v-model="curBanner.desc" maxlength="20" :disabled="(curBanner.type != 'edm')&&(curBanner.type != 'project')"/>
      </div>
    </div>
    <div class="row" :class="{disable:(curBanner.type != 'edm')&&(curBanner.type != 'project')}">
      <div class="label">desc_en(project/edm desc english version)</div>
      <div class="input">
        <input type="text" v-model="curBanner.desc_en" maxlength="32" :disabled="(curBanner.type != 'edm')&&(curBanner.type != 'project')"/>
      </div>
    </div>
    <div class="row" :class="{disable:curBanner.type != 'edm'}">
      <div class="label">dsclsr(edm disclosure chinese version)</div>
      <div class="input">
        <input type="text" v-model="curBanner.dsclsr" maxlength="96" :disabled="curBanner.type != 'edm'"/>
      </div>
    </div>
    <div class="row" :class="{disable:curBanner.type != 'edm'}">
      <div class="label">dsclsr_en(edm disclosure english version)</div>
      <div class="input">
        <input type="text" v-model="curBanner.dsclsr_en" maxlength="160" :disabled="curBanner.type != 'edm'"/>
      </div>
    </div>
    <div class="row" :class="{disable:curBanner.type != 'edm'}">
      <div class="label">Position(edm ad load position)</div>
      <div class="input">
        <label v-for="posn in position">
          <input type="checkbox" v-model="curBanner[posn]" :disabled="curBanner.type != 'edm'"/>AD on {{posn}}
        </label>
      </div>
    </div>
    <div class="row" :class="{disable:curBanner.type != 'edm'}">
      <div class="label">Send email type(Which batch to send)</div>
      <div class="input">
        <label v-for="tp in batch">
          <input type="checkbox" v-model="curBanner[tp]" :disabled="curBanner.type != 'edm'"/>{{tp}}
          <span class="batch-info">{{batchRunTime[tp]}}</span>
        </label>
      </div>
    </div>
  </div>
</div>

<script>
  var ADDetail = "{{=it.detailString}}";
  ADDetail = JSON.parse(decodeURIComponent(ADDetail))
</script>
<script src="/js/vue3.min.js"></script>
<script src="/js/Chart-3.6.2.min.js"></script>
<script src="/js/advertisement/addetailPage.js"></script>