{{&
  var {index,id,cmty,untAddr,isApp,condoNm,city,items} = it;
}}
<style>
  body{
    margin:0;
  }
  .propScroll{
    position: relative;
    overflow: auto;
    height: 100vh;
  }
  .info{
    position: fixed;
    width: 100%;
    padding: 0 10px;
    height: 44px;
    line-height: 44px;
    color: white;
    opacity: 0.75;
    background:black;
    font-size:14px;
  }
  .top{
    top: 0;
    left: 0;
  }
  .bottom{
    bottom: 0;
    left: 0;
  }
  .images{
    height:calc(100% - 88px);
    margin: 44px 0;
    overflow: auto;
  }
  .images img{
    width: 100%;
    min-height: 200px;
    background: #333;
  }
  .bg-cover{
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    overflow: hidden;
    background: black;
  }
  .currentPic{
    display:none;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    overflow: auto;
    background: black;
    color:white;
  }
  .currentPic.block{
    display:block;
  }
  .currentPic .icon{
    padding:10px;
    position: fixed;
    width: 100%;
    text-align: right;
    background: rgba(0,0,0,0.7);
  }
  .currentImage{
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
  }
  .fa-change-scroll{
    padding: 12px;
    position: absolute;
    width: 42px;
    right: 0;
    top: 0;
    font-size: 18px;
  }
</style>
<div class="bg-cover"></div>
<div class="propScroll">
  <div class="info top">
    {{? untAddr}}
    <span>{{= untAddr}} </span>
    {{?}}
    {{? id}}
    <span>({{= id}})</span>
    {{?}}
    <span class="fa fa-change-scroll" onclick='goToPhotoSwipe()'></span>
  </div>
  <div class="images">
    {{~ items :item:idx}}
      <img class="lazy" data-src={{=item.src}} onclick="showImg('{{= item.src}}','{{= item.w}}','{{= item.h}}')" src='/img/noPic.png'/>
    {{~}}
  </div>
  {{? cmty}}
  <div class="info bottom">
    <span>{{= cmty}}</span>
  </div>
  {{?}}
</div>
<div class="currentPic">
  <img class="currentImage" class="lazy" />
  <span class="icon icon-close pull-right" onclick="hideImg()"></span>
</div>
{{? !isApp}}
<link rel="stylesheet" type="text/css" href="/css/font-awesome.min.css">
<script type="text/javascript" src="/js/lazyImg.min.js"></script>
{{?}}
<script>
var index = "{{= index}}";
var currentPic = window.document.getElementsByClassName('currentPic')[0];
var img = window.document.getElementsByClassName('currentImage')[0];
var imgList = window.document.getElementsByClassName('images')[0];
var imgs = imgList.getElementsByTagName('img');

if(index){
  imgs[index].scrollIntoView();
}
function showImg(src,width,height){
  currentPic.classList.add('block');
  img.src = src;
  img.height = height;
  img.width = width;
};
function hideImg (){
  currentPic.classList.remove('block');
};
function goToPhotoSwipe(){
  var href = window.location.href;
  href = href.replace('photoScroll','photoSwipe');
  window.location.href = href;
};
if(window.loadLazyImg){
  loadLazyImg()
}
</script>
