<link rel="stylesheet" type="text/css" href="/css/apps/equifax.css">
{{&
  var {tkCreditRpt} = it;
  var InfoField = [
    {title: 'First Name',abbr:'firstName',required:true,type:'text'},
    {title: 'Last Name',abbr:'lastName',required:true,type:'text'},
    {title: 'Date of Birth',abbr: 'dateOfBirth',required:true,type:'date'},
    {title: 'SIN',abbr:'socialInsuranceNumber',required:false,type:'text'},
    {title: 'Occupation',abbr: 'occupation',type:'text', required:false}
  ];
  var AddressField = [{
    title: 'Unit #',abbr:'unit'},{
    title: 'Street #',abbr:'civicNumber',required:false},{
    title: 'Street Name',abbr:'streetName',required:true},{
    title: 'City',abbr: 'city',required:true},{
    title: 'Province',abbr: 'provinceCode',required:true},{
    title: 'Postal Code',abbr: 'postalCode',required:true
  }];
}}
<div id="allContent">
  <header id="header-bar" class="bar bar-nav">
    <h1 class="title">{{- Screening Tenant}}</h1>
    <a class="icon fa fa-back pull-left" href="javascript:;" id="exitEdit"></a>
  </header>
  <div id="loader" class="loader-wrapper"><div class="loader"></div></div>
  <div id="editDetail">
    <div class="positionTop">
      <div class="equifaxTitle">
        <span id="stp1Title">{{- Applicant\'s Details}}</span>
        <span id="stp2Title" style="display:none;">{{- Confirmation}}</span>
        <span class="titleStp"><span id="curentStp">1</span>/2</span>
      </div>
      <div class="stepLine">
        <span id="stp1Hr" class="active"></span>
        <span id="stp2Hr"></span>
      </div>
      <div class="padding10-15px errorMessage" {{? !it.errmsg}}style="display: none;"{{?}}>
        <div style="font-size:17px;display:flex;align-items:center;">
          <span class="fa fa-exclamation-circle" style="color:#FFCD00;margin-right:5px;"></span>
          <span>{{- Error}}</span>
        </div>
        <p id="message">{{= it.errmsg || ''}}</p>
      </div>
    </div>
    <div class="stepContent">
      <div id="stp1Content" class="stpPagePadding">
        <form id="editBaseInfo">
          <div class="h3Title">{{- Information}}</div>
          {{~InfoField :infoField}}
            <div class="displayDiv">
              <label for="{{= infoField.abbr}}">{{='{{-'+infoField.title+'}}'}}{{? infoField.required}}<span class="required">*</span>{{?}}</label>
              <input id="{{= infoField.abbr}}" type="{{= infoField.type}}" name="{{= infoField.abbr}}" value="{{= it[infoField.abbr] || ''}}" {{? infoField.required}}required{{?}}>
            </div>
            <label class="error" generated="true" for="{{= infoField.abbr}}" style="display:block;"></label>
            <hr>
          {{~}}
          <div style="margin-top: 10px;">
            <label for="memo">{{- Memo}}</label>
            <textarea id="memo" style="font-size:14px;margin-top:10px;" name="memo" rows="3" placeholder="{{- Enter a memo with the current application etc.}}">{{= it.memo || ''}}</textarea>
          </div>
          <div class="h3Title">{{- Address}}</div>
          <div class="titleStp" style="margin-bottom:10px;">{{- Provide previous address if changed in the last six months}}</div>
          <div style="margin-bottom:10px;">
            <input type="text" id="address" name="address" readonly style="width:100%;border:1px solid #ddd;text-align:left;" placeholder="{{- Input address}}" value="">
          </div>
          {{~AddressField :addrField}}
            <div class="displayDiv">
              <label for="{{= addrField.abbr}}">{{='{{-'+addrField.title+'}}'}}{{? addrField.required}}<span class="required">*</span>{{?}}</label>
              <input id="{{= addrField.abbr}}" type="text" name="{{= addrField.abbr}}" value="{{= it[addrField.abbr] || ''}}">
            </div>
            <label class="error" generated="true" for="{{= addrField.abbr}}" style="display:block;"></label>
            <hr>
          {{~}}
          <div class="h3Title" style="margin-top:15px;">{{- Applicant Consent Agreement}}</div>
          <div class="titleStp" style="margin-bottom:10px;">{{- Click here to upload the tenant authorization form (.JPEG/PNG)}}</div>
          <div class="displayDiv" style="justify-content:normal;align-items:flex-start;">
            <div id="previewImg" class="pic-table">
              <div>
                <img id="poaImg" class="image" src="{{= it.pic || ''}}" {{? !it.pic}}style="display: none;"{{?}} alt="{{= it.pic || ''}}">
              </div>
              <span class="image new-img" id="selectPicModel" {{? it.pic}}style="display: none;"{{?}}>
                <div>
                  <i class="icon icon-plus"></i>
                </div>
              </span>
            </div>
            <div class="displayColDiv">
              <span class="required">*</span>
              <a class="downloadBtn pull-right" href="/html/pdf/template.pdf" target="downloadFile" download style="display: none;">
                <span class="fa fa-download" style="vertical-align:middle;padding:0px 2px;font-size:14px;"></span>
                <span>{{- Template}}</span>
              </a>
            </div>
          </div>
        </form>
        <div class="bar bar-standard bar-footer footer-tab" style="padding: 0;white-space:nowrap;">
          <button class="btnWhite" id="saveDraftBtn">{{- Save Draft}}</button>
          <button class="btnGreen" id="nextBtn">{{- Next}}</button>
        </div>
      </div>
      <div id="stp2Content" style="display:none;" class="stpPagePadding">
        <div>
          <div class="h4Title">
            <span class="fa fa-circle-thin circleStyle" id="agrmntThin"></span><span id="agrmntCircle" class="fa fa-check-circle circleStyle" style="display: none;"></span>
            <span>{{- Consent and Agreement}}</span>
          </div>
          <p class="titleStp">
            {{- I confirm I have written consent from all individuals before searching their records to view their credit report, background check, and/or tenant records, as applicable, and am using such for tenancy purposes.}} 
            {{- I understand that my searches will be recorded on the records and visible.}} 
            {{- I acknowledge that it is possible that multple people may have the same name and same date of birth, so I should confirm which addresses the individual lived at previously.}} 
            {{- I acknowledge that if the consumer requests, I will inform the consumer of the name and address of the Credit Bureau supplying the report.}} 
            {{- I agree to the Consumer Reports Access Agreement.}}
          </p>
        </div>
        <div>
          <div class="h4Title">
            <span class="fa fa-circle-thin circleStyle" id="acurtThin"></span><span id="acurtCircle" class="fa fa-check-circle circleStyle" style="display: none;"></span>
            <span>{{- Accurate Information}}</span>
          </div>
          <p class="titleStp">
            {{- I confirm I have entered the applicant\'s details correctly and acknowledge that I will be charged for searches even if no credit file is found.}} 
            {{- For best results ensure the applicant\'s name is spelled exactly as written on the government issude ID and always enter a SIN or SSN when available.}}
          </p>
        </div>
        <div>
          <div class="h4Title">
            <span class="fa fa-circle-thin circleStyle" id="tokenThin"></span><span id="tokenCircle" class="fa fa-check-circle circleStyle" style="display: none;"></span>
            <span>{{- Payment}}</span>
          </div>
          <div class="displayDiv h4Title" style="padding:0 0 10px 0;"><span>{{- Token}}</span><span><span style="font-weight:bold;">{{= tkCreditRpt}}</span>/{{-, report, token}}</span></div>
          <p class="titleStp">
            {{- The cost of credit reports will be paid in advance by the company on a collective basis and settled with the real estate agent on a regular basis.}} 1{{-, Token, token}}=1$
          </p>
        </div>
        <div class="h3Title">{{- Information}}</div>
        {{~InfoField :infoField}}
          <div class="displayDiv" style="line-height:30px;">
            <label>{{='{{-'+infoField.title+'}}'}}{{? infoField.required}}<span class="required">*</span>{{?}}</label>
            <span id="{{= infoField.abbr}}Text"></span>
          </div>
          <hr>
        {{~}}
        <div>
          <label>{{- Memo}}</label>
          <p id="memoText" style="color: #000;"></p>
        </div>
        <div class="h3Title">{{- Address}}</div>
        {{~AddressField :addrField}}
          <div class="displayDiv" style="line-height:30px;">
            <label for="{{= addrField.abbr}}">{{='{{-'+addrField.title+'}}'}}{{? addrField.required}}<span class="required">*</span>{{?}}</label>
            <span id="{{= addrField.abbr}}Text"></span>
          </div>
          <hr>
        {{~}}
        <div class="bar bar-standard bar-footer footer-tab" style="padding: 0;white-space: nowrap;">
          <button class="btnWhite" id="btnBack">{{- Back}}</button>
          <button class="btnGreen" id="btnPull">{{- Pull Credit Report}}</button>
        </div>
      </div>  
    </div>
  </div>
  <div class="imageDisplay" style="display: none;">
    <img id="bigImg" src="">
    <div class="trashIcon">
      <span class="fa fa-trash"></span>
    </div>
    <span class="deleteSpan" style="display: none;">
      <button class="pull-right deleteImgBtn">{{- Yes}}</button>
      <button class="pull-right cancelImgBtn">{{- Cancel}}</button>
    </span>
  </div>
  <div id="backAlert" style="display: none;">
    <div class="backdrop" id="editBackDrop"></div>
    <div class="modal modal-60pc active" style="z-index:20;padding:0 15px;">
      <header class="alertTitle">{{- Alert}}</header>
      <div style="color:#777;text-align:center;font-size:14px;">{{- Screening tenant not saved, do you want to save and exit?}}</div>
      <div class="bar bar-standard bar-footer footer-tab" style="padding:0;white-space: nowrap;">
        <button class="btnGreen" style="background-color:#e03131;" id="saveExitBtn">{{- Save & Exit}}</button>
        <button class="btnWhite" id="cancelEditBtn">{{- Exit}}</button>
      </div>
    </div>
  </div>
  <div class="flash-message-box" style="display: none;">
    <p class="flash-message-inner"></p>
  </div>
  <div class="data" style="display: none;">
    <pre id="recordId" data-role="module-args">{{= it.recordId || ''}}</pre>
    <pre id="tkCreditRpt" data-role="module-args">{{= it.tkCreditRpt || ''}}</pre>
  </div>
  <span id="selAddrTitle" style="display: none;">{{- Select Address}}</span>
</div>
<script src="/libs/jquery-3.7.1.min.js"></script>
<script src="/libs/jquery.validate.min.js"></script>
<script src="/js/equifax/equifaxEdit.min.js"></script>
