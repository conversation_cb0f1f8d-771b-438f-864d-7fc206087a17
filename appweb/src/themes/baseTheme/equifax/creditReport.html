<link rel="stylesheet" type="text/css" href="/css/bootstrap.3.3.6.min.css">
<link rel="stylesheet" type="text/css" href="/css/apps/equifax.css">
{{&
  var {isApp,report,ScoresMark} = it;
  var titleField = [{
    title: 'Unique #',abbr:'uniqueNumber'},{
    title: 'File Since Date',abbr:'fileSinceDate'},{
    title: 'Date of Last Activity',abbr:'dateOfLastActivity'},{
    title: 'Date of Request',abbr: 'dateOfRequest'
  }];
  var scoreTitle = [
    { title: 'Range', abbr: 'poor' },
    { title: 'Population', abbr: 'popu'}
  ];
  var baseTitle = [
    {title: 'Current Name', abbr: 'name'},
    {title: 'Date of Birth', abbr: 'dateOfBirth'},
    {title: 'Personal File Number', abbr: 'persNumber'},
    {title: 'Social Insurance Number', abbr: 'soclInsrnNum'}
  ];
  var addressTitle = [
    { title: 'Type', abbr: 'type' },
    { title: 'Address', abbr: 'addr' },
    { title: 'City', abbr: 'city' },
    { title: 'Province', abbr: 'prov' },
    { title: 'Postal Code', abbr: 'postalCode' }
  ];
  var accountsTitle = [
    {title: 'Revolving', desc: 'Revolving accounts are those that generally include a credit limit and require a minimum monthly payment, such as credit cards or lines of credit.', abbr: 'revolvingCnt', field: 'revolving'},
    {title: 'Mortgage', desc: 'Mortgage accounts are real estate loans that require payment on a monthly basis until the loan is paid off.', abbr: 'mortgageCnt', field: 'mortgage'},
    {title: 'Installment', desc: 'Installment accounts are loans that require payment on a monthly basis until the loan is paid off, such as auto or student loans.', abbr: 'installmentCnt', field: 'installment'},
    {title: 'Open', desc: 'Open accounts are those that are not already identified as revolving, mortgage, or installment accounts such as charge cards or telco accounts.', abbr: 'openCnt', field: 'open'}
  ];
  var publicRecordsTitle = [
    {title: 'Bankruptcy', desc: 'Bankruptcies are a legal status granted by a federal court that indicates you are unable to pay off your outstanding debts.', abbr: 'bnkrptcyActsCnt'},
    {title: 'Collections', desc: 'When a debt is in collections, a creditor transfers it to a collections agent to attempt to recover past-due payments.', abbr: 'collectionCnt'},
    {title: 'Judgments', desc: 'A judgment is a court order where a person in debt pays a specific amount of money by law to a plaintiff.', abbr: 'judgmntCnt'}
  ];
  var bnkrptcdTitle = [
    {title: 'Date Filed', abbr: 'date'},
    {title: 'Account', abbr: 'type'},
    {title: 'Customer #', abbr: 'custNumber'},
    {title: 'Asset Amt.', abbr: 'assetAmt'},
    {title: 'Case # & Trustee', abbr: 'caseNumTrus'},
    {title: 'Filer', abbr: 'filer'},
    {title: 'Liability Amt.', abbr: 'liabilityAmnt'},
    {title: 'Intent Or Disposition', abbr: 'inteOrDisposi'}
  ];
  var collectionTitle = [
    {title: 'Status', abbr: 'type'},
    {title: "Creditor's Name", abbr: 'creditorName'},
    {title: 'Date Assigned', abbr: 'assignedDate'},
    {title: 'Original Amt.', abbr: 'originalAmt'},
    {title: 'Balance Amt.', abbr: 'balanceAmt'}
  ];
  var judgementsTitle = [
    {title: 'Date Filed', abbr: 'date'},
    {title: 'Courtld Name', abbr: 'custName'},
    {title: 'Case #', abbr: 'caseNum'},
    {title: 'Status', abbr: 'statusDes'},
    {title: "Date Satisfied", abbr: 'dateSatis'},
    {title: "Amt.", abbr: 'amount'}
  ];
  var tradeOverviewTitle = [
    {title: 'Account Number', abbr: 'accountNum'},
    {title: 'Phone', abbr: 'tel'},
    {title: 'Highest Balance', abbr: 'highAmunt'},
    {title: 'Notes', abbr: 'notes'},
    {title: 'Member Number', abbr: 'custNum'},
    {title: 'Rating Code', abbr: 'paymtRateCode'},
    {title: 'Rating Code Description', abbr: 'paymtRateDesc'}
  ];
  var tradeBalanceTitle = [
    {title: 'Balance', abbr: 'balncAmt'},
    {title: 'Credit Limit', abbr: 'creditLimit'},
    {title: 'Payment Due', abbr: 'pymtTerm'},
    {title: 'Actual Payment', abbr: 'actualPayment'},
    {title: 'Amount Past Due', abbr: 'pastDueAmt'},
    {title: 'Amount Written Oﬀ', abbr: 'writtenOﬀ'}
  ];
  var tradeAccDateTitle = [
    {title: 'Opened', abbr: 'dateOpened'},
    {title: 'Last Reported', abbr: 'lastRept'},
    {title: 'Last Payment', abbr: 'lastActvtyPay'},
    {title: 'Date Closed', abbr: 'dateClosed'}
  ];
  var tradePaymDetailTitle = [
    {title: 'Months Reviewed', abbr: 'monthsRviwd'},
    {title: 'Payment Responsibility', abbr: 'asscition'},
  ];
  var localInqTitle = [
    {title: 'Date', abbr: 'date'},
    {title: 'Member #', abbr: 'memberNum'},
    {title: 'Member Name', abbr: 'memberName'},
    {title: 'Tel AC', abbr: 'tel'},
    {title: 'May Affect Scores', abbr: 'affctScores'}
  ];
  var spclSrvicTitle = [
    {title: 'Date Reported', abbr: 'date'},
    {title: 'Description', abbr: 'desc'},
    {title: 'Customer Name', abbr: 'custName'},
    {title: 'Customer #', abbr: 'custNum'},
    {title: 'Tel AC', abbr: 'telNum'},
  ];
}}
<div {{? !isApp}}class="webContent"{{?}}>
  {{? isApp}}
  <header id="header-bar" class="bar bar-nav">
    <h1 class="title">Credit Report</h1>
    <a class="icon icon-close pull-right" style="font-size:28px;text-decoration:none;" href="/equifax/history"></a>
  </header>
  {{??}}
  <div class="webTitle">
    <span {{? !isApp}}style="margin-top:20px;"{{?}}>CONSUMER CREDIT REPORT</span>
  </div>
  {{?}}
  <div class="{{? isApp}}rprtCntntApp{{??}}rprtCntntWeb{{?}}">
    <div class="row titleInfoDiv" {{? isApp}}style="padding:15px 15px 5px;margin:0;"{{?}}>
      {{~ titleField :title}}
      <div class="col-xs-12 col-sm-12 col-md-3 col-lg-3" {{? isApp}}style="padding: 0 0 10px;"{{??}}style="padding:15px 10px;white-space:nowrap;"{{?}}><span>{{= title.title}}: {{= report[title.abbr] || '-'}}</span></div>
      {{~}}
    </div>
    <div class="scoreContent">
      <div class="displayDiv">
        <span class="{{? isApp}}ftSize17Bold padding15{{??}}ftSize20Bold padding30-15{{?}}">Credit Score</span>
        {{? isApp}}
        <div class="squareDiv displayColCenter"><span class="fa fa-rmminus" style="font-size:18px;"></span></div>
        {{?}}
      </div>
      <div {{? !isApp}}class="row"{{?}} style="margin-left:0px;">
        <div {{? isApp}}style="margin: 0 0 30px;margin-left:calc((100% - 200px) / 2);" class="scoreDiv"{{??}}class="col-xs-12 col-sm-12 col-md-2 col-lg-2 scoreDiv"{{?}}>
          <div class="scoreLabel">Equifax Credit Score</div>
          <div class="scoreText">{{= report.score || '-'}}</div>
        </div>
        <div {{? isApp}}style="margin: 25px 15px 15px 15px;"{{??}}class="col-xs-12 col-sm-12 col-md-9 col-lg-9"{{?}}>
          <div class="score-bar-container">
            <div class="score-bar" {{? isApp}}style="width: 100%;"{{?}}></div>
            <div class="score-pointer" {{? report.leftPos}}style="{{=report.leftPos}}"{{?}}>
              <div class="pointer-text">{{= report.score || '-'}}</div>
              <div class="pointer-triangle"></div>
            </div>
          </div>
          <div class="score-ranges" {{? isApp}}style="width: 100%;"{{?}}>
            {{~ ScoresMark :scoreM}}
            <div class="range-item">
              <span class="range-label">{{= scoreM.poor || '-'}}</span>
              <span class="range-value">{{= scoreM.value || 'N/A'}}</span>
            </div>
            {{~}}
          </div>
        </div>
      </div>
      <div class="scoreDesc">
      You don't just have one credit score. There are many different credit scores provided by different companies that are used to help predict how likely you are to pay your bills on time. The Equifax Credit Score shown above is created using a model developed by Equifax and is intended for your own educational use. It is also commercially available to third parties. Please keep in mind third parties may use a different score when evaluating your creditworthiness. Also, third parties will consider items other than your credit score, such as information on your credit file and information you provide to vendors/creditors for credit.
      </div>
    </div>
    <div class="applicationInfo">
      <div class="displayDiv">
        <span class="{{? isApp}}ftSize17Bold padding15{{??}}ftSize20Bold padding30-15{{?}}">Identification</span>
        {{? isApp}}
        <div class="squareDiv displayColCenter"><span class="fa fa-rmminus" style="font-size:18px;"></span></div>
        {{?}}
      </div>
      {{? isApp}}
        <div>
          {{~ baseTitle :title}}
          <div class="displayDiv padding5-15px">
            <span style="font-weight:bold;">{{= title.title}}</span>
            <span>{{= report[title.abbr] || '-'}}</span>
          </div>
          {{~}}
        </div>
        {{? report.addressData}}
        <div>
          <div class="h4ReportTitle padding15-15-10px">Addresses</div>
          {{~ report.addressData :address}}
          <div class="appContentTitle">{{= address.type}}</div>
          <div class="padding10-15px">{{= address.value || '-'}}</div>
          {{~}}
        </div>
        {{?}}
        {{? report.emplymntData}}
        <div>
          <div class="h4ReportTitle padding15-15-10px">Employment</div>
          {{~ report.emplymntData :emply}}
          <div class="appContentTitle">{{= emply.title || '-'}}</div>
          <div class="padding10-15px">{{= emply.value || '-'}}</div>
          {{~}}
        </div>
        {{?}}
      {{??}}
        <div class="margin0-15px">
          <table class="addressTable">
            <tr>
              {{~ baseTitle :title}}
              <td>{{= title.title}}</td>
              {{~}}
            </tr>
            <tr>
              {{~ baseTitle :title}}
              <td>{{= report[title.abbr] || '-'}}</td>
              {{~}}
            </tr>
          </table>
        </div>
        <div class="h3TitlePadding20">Addresses</div>
        <div class="margin0-15px">
          <table class="addressTable">
            <tr>
              {{~ addressTitle :title}}
              <td>{{= title.title}}</td>
              {{~}}
            </tr>
            {{~ report.addressData :address}}
            <tr>
              {{~ addressTitle :title}}
              <td>{{= address[title.abbr] || '-'}}</td>
              {{~}}
            </tr>
            {{~}}
          </table>
        </div>
        <div class="h3TitlePadding20">Employment</div>
        <div class="margin0-15px">
          <table class="addressTable">
            <tr>
            <td>Status</td>
            <td>Employer</td>
            </tr>
            {{~ report.emplymntData :emply}}
            <tr>
              <td>{{= emply.title || '-'}}</td>
              <td>{{= emply.value || '-'}}</td>
            </tr>
              {{~}}
          </table>
        </div>
      {{?}}
    </div>
    <div class="accounts">
      <div class="displayDiv {{? isApp}}posStick{{?}}">
        <span class="displayDiv {{? isApp}}ftSize17Bold padding15{{??}}ftSize20Bold padding30-15{{?}}">
          <span>Accounts</span>
        </span>
        {{? isApp}}
        <div class="squareDiv displayColCenter"><span class="fa fa-rmminus" style="font-size:18px;"></span></div>
        {{?}}
      </div>
      <div {{? !isApp}}class="margin0-15px borderNotBottom"{{?}}>
        {{~ accountsTitle :account}}
        <div>
          <div class="account-header {{?!isApp}}border-bottom{{?}}">
            <span>{{= account.title}}</span>
            <span>&nbsp;({{= report[account.abbr] || 0}})</span>
          </div>
          <div class="account-description {{?!isApp}}border-bottom{{?}}">
            {{= account.desc}}
          </div>
        </div>
        {{~}}
      </div>
    </div>
    {{~ accountsTitle :account}}
    {{? report[account.field] && report[account.field].length}}
    <div>
      <div class="displayDiv {{? isApp}}posStick{{?}}">
        <span class="displayDiv {{? isApp}}ftSize17Bold padding15{{??}}ftSize20Bold padding30-15{{?}}">
          <span>Accounts - {{= account.title}}</span>
        </span>
        {{? isApp}}
        <div class="squareDiv displayColCenter"><span class="fa fa-rmminus" style="font-size:18px;"></span></div>
        {{?}}
      </div>
      {{~ report[account.field] :revolv}}
      <div class="{{? !isApp}}margin0-15px-20px borderNotBottom{{?}}">
        <div class="account-header {{? !isApp}}border-bottom{{?}}">{{= revolv.custName}}</div>
        <div class="account-header">Overview</div>
        <div class="overview">
          {{? isApp}}
            <div>
              {{~ tradeOverviewTitle :title}}
              <div class="displayDiv padding10-15px">
                <span style="font-weight:bold;margin-right: 10px;">{{= title.title}}</span>
                <span>{{= revolv[title.abbr] || '-'}}</span>
              </div>
              {{~}}
            </div>
          {{??}}
            <table class="tradeTable">
              <tr>
                {{~ tradeOverviewTitle :title}}
                <td>{{= title.title}}</td>
                {{~}}
              </tr>
              <tr>
                {{~ tradeOverviewTitle :title}}
                <td>{{= revolv[title.abbr] || '-'}}</td>
                {{~}}
            </table>
          {{?}}
        </div>
        <div class="displayFlex" {{? isApp}}style="flex-wrap: wrap;"{{?}}>
          <div class="width100">
            <div class="account-header {{?!isApp}}border-bottom{{?}}">Balance And Amounts</div>
            <div>
              {{~ tradeBalanceTitle :title}}
              <div class="displayDiv padding10-15px {{? !isApp}}border-bottom{{?}}">
                <span style="font-weight:bold;margin-right: 10px;">{{= title.title}}</span>
                <span {{? !isApp}}class="marginRight100"{{?}}>{{= revolv[title.abbr] || 'N/A'}}</span>
              </div>
              {{~}}
            </div>
          </div>
          <div class="width100">
            <div class="account-header {{?!isApp}}border-bottom{{?}}">Account Dates</div>
            <div>
              {{~ tradeAccDateTitle :title}}
              <div class="displayDiv padding10-15px {{? !isApp}}border-bottom{{?}}">
                <span style="font-weight:bold;margin-right: 10px;">{{= title.title}}</span>
                <span {{? !isApp}}class="marginRight100"{{?}}>{{= revolv[title.abbr] || '-'}}</span>
              </div>
              {{~}}
              {{? !isApp}}
              <div class="border-bottom" style="height: 41px;"></div>
              <div class="border-bottom" style="height: 41px;"></div>
              {{?}}
            </div>
          </div>
        </div>
        <div>
          <div class="account-header {{?!isApp}}border-bottom{{?}}">Payment Details</div>
          <div>
            {{~ tradePaymDetailTitle :title}}
            <div class="displayDiv padding10-15px {{? !isApp}}border-bottom{{?}}">
              <span style="font-weight:bold;margin-right: 10px;">{{= title.title}}</span>
              <span>{{= revolv[title.abbr] || '-'}}</span>
            </div>
            {{~}}
          </div>
        </div>
      </div>
      <div class="{{? isApp}}marginBottom25{{??}}margin0-15px marginBottom30{{?}}">
        <div {{? isApp}}class="account-header"{{??}}style="font-weight:bold;margin-bottom: 10px;"{{?}}>Delinquencies</div>
        <div {{? isApp}}class="account-description"{{?}}>
          {{= revolv.delinquencies || 'You currently have no delinquencies on your credit file.'}}
        </div>
      </div>
      {{~}}
    </div>
    {{?}}
    {{~}}
    <div class="publicRecords">
      <div class="displayDiv {{? isApp}}posStick{{?}}">
        <span class="displayDiv {{? isApp}}ftSize17Bold padding15{{??}}ftSize20Bold padding30-15{{?}}">
          <span>Public Records</span>
        </span>
        {{? isApp}}
        <div class="squareDiv displayColCenter"><span class="fa fa-rmminus" style="font-size:18px;"></span></div>
        {{?}}
      </div>
      <div {{? !isApp}}class="margin0-15px borderNotBottom"{{?}}>
        {{~ publicRecordsTitle :record}}
        <div>
          <div class="account-header {{?!isApp}}border-bottom{{?}}">
            <span>{{= record.title}}</span>
            <span>&nbsp;({{= report[record.abbr] || 0}})</span>
          </div>
          <div class="account-description {{?!isApp}}border-bottom{{?}}">
            {{= record.desc}}
          </div>
        </div>
        {{~}}
      </div>
    </div>
    <div class="bankruptcies">
      <div class="displayDiv {{? isApp}}posStick{{?}}">
        <span class="{{? isApp}}ftSize17Bold padding15{{??}}ftSize20Bold padding30-15{{?}}">
          <span>Bankruptcy</span>
        </span>
        {{? isApp}}
        <div class="squareDiv displayColCenter"><span class="fa fa-rmminus" style="font-size:18px;"></span></div>
        {{?}}
      </div>
      {{? !report.bankruptcyData}}
        <p style="margin:0 15px;">You currently have no bankruptcies on your credit file</p>
      {{??}}
        {{? isApp}}
          <div>
            <div class="displayDiv timeTagsDiv posStickTime">
              {{~ report.bankruptcyData :bankrpt:index}}
              <div class="timeTag {{?index==0}}active{{?}}">{{= bankrpt.date || '-'}}&nbsp;({{= bankrpt.detail?.length || 0}})</div>
              {{~}}
            </div>
            {{~ report.bankruptcyData :bankrpt:interior}}
            <div class="appContent {{= bankrpt.date}}" {{? interior != 0}}style="display:none"{{?}}>
              {{~ bankrpt.detail :info}}
              <div class="marginBottom15 bgcFcebeb">
                {{~ bnkrptcdTitle :title}}
                <div class="displayDiv padding10-15px">
                  <span style="font-weight:bold;">{{= title.title}}</span>
                  <span>{{= info[title.abbr] || '-'}}</span>
                </div>
                {{~}}
              </div>
              {{~}}
            </div>
            {{~}}
          </div>
        {{??}}
          <div class="margin0-15px">
            <table class="addressTable">
              <tr>
                {{~ bnkrptcdTitle :title}}
                <td>{{= title.title}}</td>
                {{~}}
              </tr>
              {{~ report.bankruptcyData :bankrpt}}
              <tr>
                {{~ bnkrptcdTitle :title}}
                <td>{{= bankrpt[title.abbr] || '-'}}</td>
                {{~}}
              </tr>
              {{~}}
            </table>
          </div>
        {{?}}
      {{?}}
    </div>
    <div class="collection">
      <div class="displayDiv {{? isApp}}posStick{{?}}">
        <span class="{{? isApp}}ftSize17Bold padding15{{??}}ftSize20Bold padding30-15{{?}}">
          <span>Collections</span>
        </span>
        {{? isApp}}
        <div class="squareDiv displayColCenter"><span class="fa fa-rmminus" style="font-size:18px;"></span></div>
        {{?}}
      </div>
      {{? report.collectionData}}
        {{? isApp}}
          <div>
            <div class="displayDiv timeTagsDiv posStickTime">
              {{~ report.collectionData :colct:index}}
              <div class="timeTag {{?index==0}}active{{?}}">{{= colct.type.split(' ')[0]}}</div>
              {{~}}
            </div>
            {{~ report.collectionData :colct:interior}}
            <div class="appContent {{= colct.type.split(' ')[0]}}" {{? interior != 0}}style="display:none"{{?}}>
              <div class="marginBottom15 bgcFcebeb">
                {{~ collectionTitle :title}}
                <div class="displayDiv padding10-15px">
                  <span style="font-weight:bold;">{{= title.title}}</span>
                  <span>{{= colct[title.abbr] || '-'}}</span>
                </div>
                {{~}}
              </div>
            </div>
            {{~}}
          </div>
        {{??}}
          <div class="margin0-15px">
            <table class="addressTable">
              <tr>
                {{~ collectionTitle :title}}
                <td>{{= title.title}}</td>
                {{~}}
              </tr>
              {{~ report.collectionData :colct}}
              <tr>
                {{~ collectionTitle :title}}
                <td>{{= colct[title.abbr] || '-'}}</td>
                {{~}}
              </tr>
              {{~}}
            </table>
          </div>
        {{?}}
      {{??}}
        <p style="margin:0 15px;">You currently have no collections on your credit file</p>
      {{?}}
    </div>
    <div class="judgement">
      <div class="displayDiv {{? isApp}}posStick{{?}}">
        <span class="{{? isApp}}ftSize17Bold padding15{{??}}ftSize20Bold padding30-15{{?}}">
          <span>Judgements</span>
        </span>
        {{? isApp}}
        <div class="squareDiv displayColCenter"><span class="fa fa-rmminus" style="font-size:18px;"></span></div>
        {{?}}
      </div>
      {{? report.judgementsData}}
        {{? isApp}}
          <div>
            <div class="displayDiv timeTagsDiv posStickTime">
              {{~ report.judgementsData :judgmts:index}}
              <div class="timeTag {{?index==0}}active{{?}}">{{= judgmts.date || '-'}}&nbsp;({{= judgmts.detail?.length || 0}})</div>
              {{~}}
            </div>
            {{~ report.judgementsData :judgmts:interior}}
            <div class="appContent {{= judgmts.date}}" {{? interior != 0}}style="display:none"{{?}}>
              {{~ judgmts.detail :info}}
              <div class="marginBottom15 bgcFcebeb">
                {{~ judgementsTitle :title}}
                <div class="displayDiv padding10-15px">
                  <span style="font-weight:bold;">{{= title.title}}</span>
                  <span>{{= info[title.abbr] || '-'}}</span>
                </div>
                {{~}}
              </div>
              {{~}}
            </div>
            {{~}}
          </div>
        {{??}}
          <div class="margin0-15px">
            <table class="addressTable">
              <tr>
                {{~ judgementsTitle :title}}
                <td>{{= title.title}}</td>
                {{~}}
              </tr>
              {{~ report.judgementsData :judgmts}}
              <tr>
                {{~ judgementsTitle :title}}
                <td>{{= judgmts[title.abbr] || '-'}}</td>
                {{~}}
              </tr>
              {{~}}
            </table>
          </div>
        {{?}}
      {{??}}
        <p style="margin:0 15px;">You currently have no judgments on your credit file</p>
      {{?}}
    </div>
    <div class="inquiries">
      <div class="displayDiv {{? isApp}}posStick{{?}}">
        <span class="{{? isApp}}ftSize17Bold padding15{{??}}ftSize20Bold padding30-15{{?}}">
          <span>Inquiries</span>
        </span>
        {{? isApp}}
        <div class="squareDiv displayColCenter"><span class="fa fa-rmminus" style="font-size:18px;"></span></div>
        {{?}}
      </div>
      <div>
        <div class="displayDiv h4ReportTitle {{? isApp}}padding15 posStickTime{{??}}margin0-15px padding10-15px bgcFcebeb borderNotBottom{{?}}" style="justify-content: normal;">
          <span>Local Inquiries</span>
          <div class="cntCircle">{{= report.localInquCnt}}</div>
        </div>
        {{? report.localInquData}}
          {{? isApp}}
            <div>
              <div class="displayDiv timeTagsDiv posStickSquire">
                {{~ report.localInquData :localInq:index}}
                <div class="timeTag {{?index==0}}active{{?}}">{{= localInq.date|| '-'}}&nbsp;({{= localInq.detail?.length || 0}})</div>
                {{~}}
              </div>
              {{~ report.localInquData :localInq:interior}}
              <div class="appContent {{= localInq.date}}" {{? interior != 0}}style="display:none"{{?}}>
                {{~ localInq.detail :info}}
                <div class="marginBottom15 bgcFcebeb">
                  {{~ localInqTitle :title}}
                  <div class="displayDiv padding10-15px">
                    <span style="font-weight:bold;">{{= title.title}}</span>
                    <span>{{= info[title.abbr] || '-'}}</span>
                  </div>
                  {{~}}
                </div>
                {{~}}
              </div>
              {{~}}
            </div>
          {{??}}
            <div class="margin0-15px">
              <table class="addressTable">
                <tr>
                  {{~ localInqTitle :title}}
                  <td>{{= title.title}}</td>
                  {{~}}
                </tr>
                {{~ report.localInquData :localInq}}
                <tr>
                  {{~ localInqTitle :title}}
                  <td>{{= localInq[title.abbr] || '-'}}</td>
                  {{~}}
                </tr>
                {{~}}
              </table>
            </div>
          {{?}}
        {{??}}
          <p style="margin:0 15px;">You currently have no local inquiries on your credit file</p>
        {{?}}
      </div>
    </div>
    <div class="specialServices">
      <div class="displayDiv {{? isApp}}posStick{{?}}">
        <span class="displayDiv {{? isApp}}ftSize17Bold padding15{{??}}ftSize20Bold padding30-15{{?}}">
          <span>Special Services</span>
          <div class="cntCircle">{{= report.specialServiceCnt}}</div>
        </span>
        {{? isApp}}
        <div class="squareDiv displayColCenter"><span class="fa fa-rmminus" style="font-size:18px;"></span></div>
        {{?}}
      </div>
      {{? report.spcilServData}}
        {{? isApp}}
          <div>
            <div class="displayDiv timeTagsDiv">
              {{~ report.spcilServData :spcilServ:index}}
              <div class="timeTag {{?index==0}}active{{?}}">{{= spcilServ.date}}&nbsp;({{= spcilServ.detail?.length || 0}})</div>
              {{~}}
            </div>
            {{~ report.spcilServData :spcilServ:interior}}
            <div class="appContent {{= spcilServ.date}}" {{? interior != 0}}style="display:none"{{?}}>
              {{~ spcilServ.detail :info}}
              <div class="marginBottom15 bgcFcebeb">
                {{~ spclSrvicTitle :title}}
                <div class="displayDiv padding10-15px">
                  <span style="font-weight:bold;">{{= title.title}}</span>
                  <span>{{= info[title.abbr] || '-'}}</span>
                </div>
                {{~}}
              </div>
              {{~}}
            </div>
            {{~}}
          </div>
        {{??}}
          <div class="margin0-15px">
            <table class="addressTable">
              <tr>
                {{~ spclSrvicTitle :title}}
                <td>{{= title.title}}</td>
                {{~}}
              </tr>
              {{~ report.spcilServData :spcilServ}}
              <tr>
                {{~ spclSrvicTitle :title}}
                <td>{{= spcilServ[title.abbr] || '-'}}</td>
                {{~}}
              </tr>
              {{~}}
            </table>
          </div>
        {{?}}
      {{??}}
      <p style="margin: 0 15px;">You currently have no special services on your credit file</p>
      {{?}}
    </div>
    <div>
      <div class="displayDiv">
        <span class="{{? isApp}}ftSize17Bold padding15{{??}}ftSize20Bold padding30-15{{?}}">Legal</span>
        {{? isApp}}
        <div class="squareDiv displayColCenter"><span class="fa fa-rmminus" style="font-size:18px;"></span></div>
        {{?}}
      </div>
      <p style="padding: 0px 15px 10px;;color:#000" id="legalInfo">
        This report is intended exclusively for Avion Realty Inc. and may only be accessed with the individual's consent and in relation to the initiation or renewal of a tenancy agreement or other purposes authorized by the individual or applicable laws. Distribution of this report outside Avion Realty Inc. is strictly prohibited.
      </p>
    </div>
    <div class="displayDiv" style="padding:15px 15px 55px;justify-content: start;">
      <div class="displayColDiv" style="align-items: flex-start;">
        <img src="/img/home/<USER>" style="width: 23px;">
        <span>Provided for Avion Realty Inc.</span>
      </div>
      <div class="displayColDiv" style="margin-left: 10px;align-items: flex-start;">
        <img src="/img/equifax.jpg" style="width: 90px;">
        <span>Powered by Equifax.</span>
      </div>
    </div>
  </div>
  {{? isApp}}
  <div class="bar bar-standard bar-footer footer-tab" style="padding: 0;white-space:nowrap;">
    <button class="btnGreen" onclick="toggleAllInfo(false)">{{- Collapse All}}</button>
    <button class="btnWhite" onclick="toggleAllInfo(true)">{{- Expand all}}</button>
  </div>
  {{??}}
  <div class="printBtn" onclick="openPrint()">
    <span class="fa fa-print" style="font-size: 23px;color: #e03131;"></span>
    <span>{{- Print}}</span>
  </div>
  {{?}}
</div>
<script src="/libs/jquery-3.7.1.min.js"></script>
<script src="/js/equifax/equifaxReport.min.js"></script>
