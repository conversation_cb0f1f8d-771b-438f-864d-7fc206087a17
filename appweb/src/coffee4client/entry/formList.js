import Vue from 'vue'
import AppFormList from '../components/form/appFormList.vue'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import UrlVars from '../components/url-vars'

UrlVars.init();

Vue.use(VueResource);
Vue.use(L10N);
window.bus = new Vue();
Vue.http.interceptors.push(window.onhttpError);

new Vue({
  el: '#list',
  mounted: function() {
    this.$getTranslate(this);
  },
  components: { AppFormList }
})
