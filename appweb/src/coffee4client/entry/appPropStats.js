// require('babel-polyfill');

import Vue from 'vue'
// import AppIndex from '../components/appIndex'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'

import AppPropStats from '../components/appPropStats.vue'
import PageSpinner from '../components/frac/PageSpinner.vue'
import FlashMessage from '../components/frac/FlashMessage.vue'

import pagedata_mixins from '../components/pagedata_mixins'
import prop_mixins from '../components/prop_mixins'
import rmsrv_mixins from '../components/rmsrv_mixins'
import filters from '../components/filters'
import UrlVars from '../components/url-vars'

UrlVars.init();
Vue.use(VueResource);
Vue.use(L10N);
Vue.filter('propPrice', filters.propPrice);
Vue.filter('percentage', filters.percentage);
Vue.filter('dotdate', filters.dotdate);
Vue.filter('yearMonth', filters.yearMonth);
// Vue.config.debug = true;
// Vue.config.devtools = true;
/* eslint-disable no-new */
window.bus = new Vue();
new Vue({
  mixins:[prop_mixins,rmsrv_mixins],
  el: '#vueBody',
  mounted: function() {
    this.$getTranslate(this);
  },
  components: { AppPropStats }
})
