// require('babel-polyfill');

import Vue from 'vue'
import appPropServices from '../components/appPropServices'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import UrlVars from '../components/url-vars'
import filters from '../components/filters'

UrlVars.init();

Vue.use(VueResource);
Vue.use(L10N);
Vue.filter('time', filters.time);
// Vue.config.debug = true;
// Vue.config.devtools = true;
/* eslint-disable no-new */
window.bus = new Vue();
new Vue({
  el: '#vueBody',
  mounted: function() {
    this.$getTranslate(this);
  },
  components: { appPropServices }
})
