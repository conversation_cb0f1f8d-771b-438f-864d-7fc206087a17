// require('babel-polyfill');

import Vue from 'vue'
import IndexRealtorMiddlePage from '../components/frac/IndexRealtorMiddlePage'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import UrlVars from '../components/url-vars'
import pagedata_mixins from '../components/pagedata_mixins'
import rmsrv_mixins from '../components/rmsrv_mixins'

UrlVars.init();
Vue.use(VueResource);
Vue.use(L10N);
window.bus = new Vue();
/* eslint-disable no-new */
new Vue({
  el: '#vueBody',
  mixins:[pagedata_mixins,rmsrv_mixins],
  components: {
    IndexRealtorMiddlePage
  },
  data () {
    return {
      dispVar:{
        defaultEmail:'<EMAIL>',
        realtorPageAd:[],
        isVipRealtor:false,
        isVipPlus:false,
        isRealtor:true,
        isOntarioRealtor:true,
      },
      datas:[
        'defaultEmail',
        'isOntarioRealtor',
        'isRealtor',
        'isVipPlus',
        'lang',
        'userCity',
        'hasAid',
        'realtorPageAd',
        'isVipRealtor',
        'realmasterHelpUrl',
      ]
    }
  },
  mounted(){
    var self = this;
    this.$getTranslate(this);
    bus.$on('index-redirect-goto', function (dr) {
      self.goTo(dr);
    });
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
    });
    setTimeout(function () {
      self.getPageData(self.datas,{},true);
    }, 0);
  },
  methods: {
    confirmTreb: function () {
      var self = this;
      // Sign Treb Data Agreement. Once agreements are signed, you can directly search all your own listings and your company's listings.
      var optTip = "Please contact RealMaster at "+self.dispVar.defaultEmail;
      var fn = this._?this._:this.$parent._;
      var tl = fn('To active this feature');
      // Need to sign data agreement online
      var tip = fn(optTip);
      var later = fn('Later');
      var seemore = fn('See More');
      function _doShowTreb(idx) {
        // http://www.realmaster.cn/mp/17c7b2bb1984ae8b3d6f5dc295861557a211e18666b1da62494c59059054c263cd1c6cdc880858aa82d3b04aa24a936a434b9e942d8cae9c07ac1ee5c4d8228d0994fd?lang=zh-cn
        var url = 'mailto:'+self.dispVar.defaultEmail;
        if (idx+'' == '2') {
          window.location.href = url;
          // RMSrv.showInBrowser(url);
        }
      }
      return RMSrv.dialogConfirm(tip, _doShowTreb, tl, [later, seemore]);
    }
  }
})
