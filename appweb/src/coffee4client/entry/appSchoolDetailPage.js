// require('babel-polyfill');

import Vue from 'vue'
import appSchoolDetailPage from '../components/appSchoolDetailPage'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import rmsrv_mixins from '../components/rmsrv_mixins'
import UrlVars from '../components/url-vars'
import schoolDetailFooter from '../components/school/schoolDetailFooter'
// import commonComments from '../components/forum/commonComments'

UrlVars.init();

Vue.use(VueResource);
Vue.use(L10N);
// Vue.config.debug = true;
// Vue.config.devtools = true;
/* eslint-disable no-new */
window.bus = new Vue();
new Vue({
  mixins:[rmsrv_mixins],
  el: '#vueBody',
  mounted: function() {
    this.$getTranslate(this);
  },
  components: { appSchoolDetailPage }
})

// window.comments = new Vue({
//   mixins:[rmsrv_mixins,forum_mixins,forum_detail_mixins],
//   el: '#comment',
//   data () {
//     return {
//     };
//   },
//   methods: {
//   },
//   mounted () {
//     this.$getTranslate(this);
//     var self = this;
//     this.post = vars.post;
//   },
//   components: {
//     commonComments,
//     FlashMessage
//   }
// })
window.footer = new Vue({
  mixins:[rmsrv_mixins],
  el: '#footer',
  data () {
    return {
    };
  },
  methods: {
  },
  mounted () {
    this.$getTranslate(this);
    var self = this;
    this.post = vars.post;
  },
  components: {
    schoolDetailFooter
  }
})
