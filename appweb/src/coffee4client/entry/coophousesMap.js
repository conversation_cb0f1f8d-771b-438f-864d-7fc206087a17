// require('babel-polyfill');

import Vue from 'vue'
import CoophousesMap from '../components/coophousesMap'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import UrlVars from '../components/url-vars'
import map_mixins from '../components/map_mixins'
UrlVars.init();

Vue.use(VueResource);
Vue.use(L10N);
window.bus = new Vue();
/* eslint-disable no-new */
new Vue({
  mixins:[map_mixins],
  el: '#vueBody',
  mounted: function() {
    this.$getTranslate(this);
  },
  components: { CoophousesMap }
})
