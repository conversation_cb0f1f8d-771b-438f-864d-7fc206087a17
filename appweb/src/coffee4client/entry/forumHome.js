import Vue from 'vue'
import ForumHome from '../components/forum/forumHome'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import UrlVars from '../components/url-vars'

UrlVars.init();

Vue.use(VueResource);
Vue.use(L10N);
window.bus = new Vue();
Vue.http.interceptors.push(window.onhttpError);

/* eslint-disable no-new */
new Vue({
  el: '#forumHome',
  mounted: function() {
    this.$getTranslate(this);
  },
  components: {
    ForumHome  }
})
