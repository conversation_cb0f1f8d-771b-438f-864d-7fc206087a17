// require('babel-polyfill');

import Vue from 'vue'
import imageInsert from '../components/imageInsert'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
// import filters from '../components/filters'
// import rmsrv_mixins from '../components/rmsrv_mixins'
import UrlVars from '../components/url-vars'
UrlVars.init();

Vue.use(VueResource);
Vue.use(L10N);
// Vue.filter('time', filters.time);
// Vue.filter('number', filters.number);
// Vue.filter('dotdate', filters.dotdate);
// Vue.filter('datetime', filters.datetime);
/* eslint-disable no-new */
window.bus = new Vue();
new Vue({
  mixins:[],
  el: '#vueBody',
  mounted: function() {
    this.$getTranslate(this);
  },
  components: { imageInsert }
})
