// require('babel-polyfill');

import Vue from 'vue'
import appVerify from '../components/appVerify'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import rmsrv_mixins from '../components/rmsrv_mixins'
import UrlVars from '../components/url-vars'
// import VueValidator from 'vue-validator'
import VeeValidate from 'vee-validate';
// window.bag = new VeeValidate.ErrorBag();

UrlVars.init();

Vue.use(VueResource);
Vue.use(VeeValidate);
Vue.use(L10N);
// Vue.config.debug = true;
// Vue.config.devtools = true;
/* eslint-disable no-new */
window.bus = new Vue();
new Vue({
  mixins:[rmsrv_mixins],
  el: '#vueBody',
  mounted: function() {
    this.$getTranslate(this);
  },
  components: { appVerify }
})
