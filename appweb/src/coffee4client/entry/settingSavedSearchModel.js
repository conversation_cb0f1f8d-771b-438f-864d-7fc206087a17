// require('babel-polyfill');

import Vue from 'vue'
import settingSavedSearchModel from '../components/frac/SettingSavedSearchModel'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import UrlVars from '../components/url-vars'
import filters from '../components/filters'

UrlVars.init();

Vue.use(VueResource);
Vue.use(L10N);
Vue.filter('time', filters.time);

window.bus = new Vue();
new Vue({
  el: '#vueBody',
  mounted: function() {
    this.$getTranslate(this);
  },
  components: { settingSavedSearchModel }
})
