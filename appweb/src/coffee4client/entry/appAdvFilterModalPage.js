// require('babel-polyfill');

import Vue from 'vue'
import MapAdvFilterModal from '../components/frac/MapAdvFilterModal.vue'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import UrlVars from '../components/url-vars'
import filters from '../components/filters'

UrlVars.init();

Vue.filter('dotdate', filters.dotdate);
Vue.use(VueResource);
Vue.use(L10N);
window.bus = new Vue();
/* eslint-disable no-new */
new Vue({
  el: '#vueBody',
  mounted: function() {
    this.$getTranslate(this);
  },
  components: { MapAdvFilterModal }
})
