// require('babel-polyfill');

import Vue from 'vue'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import UrlVars from '../components/url-vars'
import schoolDetailFooter from '../components/school/schoolDetailFooter'
import rmsrv_mixins from '../components/rmsrv_mixins'

UrlVars.init();

Vue.use(VueResource);
Vue.use(L10N);

Vue.http.interceptors.push(window.onhttpError);
window.bus = new Vue();
/* eslint-disable no-new */
// window.comments = new Vue({
//   mixins:[rmsrv_mixins,forum_mixins,forum_detail_mixins],
//   el: '#comment',
//   data () {
//     return {
//     };
//   },
//   methods: {
//   },
//   mounted () {
//     this.$getTranslate(this);
//     var self = this;
//     this.post = vars.post;
//   },
//   components: {
//     commonComments,
//     FlashMessage
//   }
// })
window.footer = new Vue({
  mixins:[rmsrv_mixins],
  el: '#footer',
  data () {
    return {
    };
  },
  methods: {
  },
  mounted () {
    this.$getTranslate(this);
  },
  components: {
    schoolDetailFooter,
  }
})
