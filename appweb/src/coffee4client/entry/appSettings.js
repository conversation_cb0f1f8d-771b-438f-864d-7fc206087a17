// require('babel-polyfill');

import Vue from 'vue'
import appSettings from '../components/appSettings'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'

Vue.use(VueResource);
Vue.use(L10N);

/* eslint-disable no-new */
window.bus = new Vue();
new Vue({
  el: '#vueBody',
  mounted: function() {
    this.$getTranslate(this);
  },
  components: { appSettings }
})
window.addEventListener('pageshow', function(event) {
  if (event.persisted) {
    location.reload();
  }
});
