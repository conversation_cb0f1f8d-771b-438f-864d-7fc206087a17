// require('babel-polyfill');

import Vue from 'vue'
import Chatlist from '../components/chatlist'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import filters from '../components/filters'
import UrlVars from '../components/url-vars'
UrlVars.init();

Vue.use(VueResource);
Vue.use(L10N);
Vue.filter('time', filters.time);
Vue.filter('dotdate', filters.dotdate);
/* eslint-disable no-new */
window.bus = new Vue();
new Vue({
  el: '#vueBody',
  mounted: function() {
    this.$getTranslate(this);
  },
  components: { Chatlist }
})
