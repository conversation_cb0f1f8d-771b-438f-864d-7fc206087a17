// require('babel-polyfill');

import Vue from 'vue'
// import AppMapSearch from '../components/appMapSearch'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import UrlVars from '../components/url-vars'
import map_mixins from '../components/map_mixins2'
import mapSearch_mixins from '../components/mapSearch_mixins'
import mapSearchLogic_mixins from '../components/mapSearchLogic_mixins'
import autocomplete_mixins from '../components/autocomplete_mixins'

import prop_mixins from '../components/prop_mixins'

import filters from '../components/filters'
// import progress from '../components/frac/ProgressBar'

import pagedata_mixins from '../components/pagedata_mixins'
// TODO:remove
import AutocompleteListWrapper  from '../components/frac/AutocompleteListWrapper.vue'
import PropList  from '../components/frac/PropList.vue'
import PropPreviewElement  from '../components/frac/PropPreviewElement.vue'
// import SchoolPreviewElement  from '../components/frac/SchoolPreviewElement.vue'
import SchoolListElement from '../components/frac/SchoolListElement.vue'
import ShareDialog from '../components/frac/ShareDialog2.vue'
import FlashMessage from '../components/frac/FlashMessage.vue'
import rmsrv_mixins from '../components/rmsrv_mixins'
import LoadingBar from '../components/frac/LoadingBar.vue'
import PropFavActions from '../components/frac/PropFavActions.vue'
import SchoolList from '../components/frac/SchoolList.vue'
import PropShowingActions  from '../components/showing/propShowingActions.vue'
import ListingShareDesc from '../components/frac/ListingShareDesc.vue'
import ContactRealtor from '../components/frac/ContactRealtor.vue'
// import ProjCitiesModal from '../components/project/ProjCitiesModal.vue'
// import ProjDetail from '../components/project/ProjDetail.vue'
// import PropShareModal from '../components/prop/PropShareModal.vue';
import PropTable from '../components/prop/PropTable.vue';

UrlVars.init();

Vue.use(VueResource);
// Vue.use(progress)
Vue.use(L10N);
// Vue.filter('time', filters.time);
Vue.filter('propPrice', filters.propPrice);
Vue.filter('dotdate', filters.dotdate);
Vue.filter('monthNameAndDate', filters.monthNameAndDate);
Vue.filter('percentage', filters.percentage);

// Vue.filter('datetime', filters.datetime);
/* eslint-disable no-new */
window.bus = new Vue();
// https://segmentfault.com/q/1010000005800495
Vue.http.interceptors.push((request, next) => {
  var timeout;
	// 這裡改用 _timeout ，就不會觸發原本的
  if (request._timeout) {
  	// 一樣綁定一個定時器，但是這裡是只要觸發了，就立即返回 response ， 並且這邊自定義了 status 和 statusText
    timeout = setTimeout(() => {
      next(request.respondWith(request.body, {
           status: 408,
           statusText: 'Request Timeout'
      }));
    }, request._timeout);
  }
  next((response) => {
      clearTimeout(timeout);
  });
})
new Vue({
  mixins:[map_mixins,pagedata_mixins,rmsrv_mixins,mapSearch_mixins,mapSearchLogic_mixins,prop_mixins,autocomplete_mixins],
  el: '#vueBody',
  computed:{
  },
  data () {
    return {
      curRealtor:{
        eml: '<EMAIL>',
        mbl: 9056142609,
        uid: 'hide'
      },
      smbMode:'contact',
      projCities:[],
      appmode:'mls',
      showProjCities:false,
      isNativeSearch:false,
      datas:[
        'isProdSales',
        'isProjAdmin',
        'isPaytop',
        'isCip',
        'isVipUser',
        'isVipRealtor',
        'isRealtor',
        'isVisitor',
        'lang',
        'allowedShareSignProp',
        'propPtypes',
        'ptpTypes',
        'propSortMethods',//need this
        'projSortMethods',
        'isApp',
        'isLoggedIn',
        'shareUID',
        'allowedPromoteProp',
        // 'jsGmapUrl',
        'hasFollowedRealtor',
        'hasFollowedVipRealtor',
        // 'propCounts',
        'reqHost',
        'shareAvt',
        'sessionUser',
        'domFilterVals',
        'domFilterValsShort',
        'propFeatureTags',
        // 'bsmtFilterVals',
        'allowedEditGrpName',
        'shareLinks',
        // 'autocomplete',
        // 'placeApi',
        'coreVer',
        // 'showSoldPriceBtn',
        'coreVer',
        'userCity',
        'useWebMap',
        // 'showSoldPrice',
        'userRoles',
        'isAdmin',
        'isRealGroup',
      ],
      stat:{},
    };
  },
  components: {
    // PageSpinner,
    // Progress,
    ListingShareDesc,
    // SchoolDetail,
    // PropNeedLogin,
    // Prop3pPromoteModal,
    // PropCreateWepageModal,
    PropList,
    PropPreviewElement,
    // SchoolPreviewElement,
    SchoolListElement,
    // Disclaimer,
    // BrkgPhoneList,
    ShareDialog,
    FlashMessage,
    // Switch,
    // CitySelectModal,
    LoadingBar,
    // LoadingSquare,
    PropFavActions,
    SchoolList,
    // DoubleGoLink,
    AutocompleteListWrapper,
    PropShowingActions,
    ContactRealtor,
    // ProjCitiesModal,
    // PropShareModal,
    PropTable
  },
  mounted () {
    var self = this, bus = window.bus;
    // self.clearCache();
    //after getPageData
    this.city = {o:vars.city,p:vars.prov,n:vars.cityName,pn:vars.provName};
    this.appmode = vars.appmode || vars.appMode || 'mls';
    bus.$on('pagedata-retrieved', function (d) {
      self.isNativeSearch = self.isNewerVer(self.dispVar.coreVer,'6.0.1');
      self.dispVar = Object.assign(self.dispVar, d);
      self.getCityInfo();
    });
    //from school preview modal
    bus.$on('school-changed', function (sch) {
      var cfg = {hide:false, title:this._('RealMaster')};
      let url=''
      if(sch.private) {
        url = '/1.5/school/private/detail/'+sch._id
      } else if (sch.tp=='university' || sch.tp=='college' ) {
        url = '/1.5/school/university/detail/'+sch._id
      } else {
        url = '/1.5/school/public/detail?id='+sch._id
      }
      url = self.appendDomain(url);
      RMSrv.getPageContent(url, '#callBackString', cfg, function(val){
        if (val == ':cancel') {
          console.log('canceled');
          return;
        }
        try {
          // var val = 'loc=43.5723199141038,-79.5785565078259&zoom=15&saletp=lease';
          if (/^cmd-redirect:/.test(val)) {
            // return alert(val);
            var url = val.split('cmd-redirect:')[1];
            return window.location = url;
          }
          var d = self.urlParamToObject(val);
          // alert(JSON.stringify(d));
          window.bus.$emit('school-prop', d);
        } catch (e) {
          console.error(e);
        }
        // console.log('returned city');
      });
    });
    bus.$on('prop-changed', function (prop) {
      // window.location = "/1.5/prop/detail/inapp?id="+prop._id;
      var cfg = {hide:false,title:self._('RealMaster')};
      var propId;
      if (/^RM/.test(prop.id)) {
        propId = prop.id;
      } else {
        propId = prop._id;
      }
      var base = "/1.5/prop/detail/inapp?lang=";
      if (prop.isProj || prop.tp1) {
        base = '/1.5/prop/projects/detail?inframe=1&lang='
      }
      if (Object.keys(prop).length < 2) {
        console.error('Error: '+JSON.stringify(prop));
      }
      let mode = self.appMapMode;
      if(mode !== 'fav'){
        mode = self.viewMode
      }
      var url = base+self.dispVar.lang+"&id="+propId+'&mode='+mode;
      url = self.appendDomain(url);
      console.log(url);
      // TODO: if vars.src == native rmCall conditions
      RMSrv.getPageContent(url, '#callBackString', cfg, function(val){
        if (val == ':cancel') {
          console.log('canceled');
          return;
        }
        if (/^redirect/.test(val)) {
          return window.location = val.split('redirect:')[1]
        }
        try {
          // var val = 'loc=43.5723199141038,-79.5785565078259&zoom=15&saletp=lease';
          // alert(val)
          self.requestHasWechat()
          if (/^cmd-redirect:/.test(val)) {
            // return alert(val);
            var url = val.split('cmd-redirect:')[1];
            return window.location = url;
          }
          // alert(val+':->'+self.dispVar.coreVer);
          var d = self.urlParamToObject(val);
          // alert(JSON.stringify(d));
          if (!self.dispVar.useWebMap && self.isNewerVer(self.dispVar.coreVer, '5.8.0')) {
            // alert(JSON.stringify(d));
            self.showMapView(d)
            return;
          } else {
            return window.location = `/1.5/mapSearch?${val}`;
          }
          window.bus.$emit('school-prop', d);
        } catch (e) {
          console.error(e);
        }
        // console.log('returned city');
      });
    });
    bus.$on('school-prop', function (d) {
      // After open prop detail, if search in adv filter, result will be in map mode
      // if (self.viewMode !== 'map') {
      //   self.viewMode = 'map'
      // }
      self.showItems = false;
      // self.propTmpFilter.saletp = d.saletp;
      self.setSaleTp((d['k-saletp'] || self.propTmpFilter.saletp))
      toggleModal('schoolDetailModal','close');
      //set center and zoom
      // alert(JSON.stringify(d));
      // if (d.sch && d.sch.loc) {
      //   self.recenterMapForCities({lat:d.sch.loc[0],lng:d.sch.loc[1]}, 14);
      // }
      if (d.loc) {
        if (d['k-dom']) {
          self.selectSoldFilterVal({k:'-90',v:'3 month'});
          self.moveToRecord({lat:d.loc[0],lng:d.loc[1]});
        } else {
          self.recenterMapForCities({lat:d.loc[0],lng:d.loc[1]}, 14);
        }
      }
      self.clearModals();
      self.doSearch();
      self.getCityInfo();
    });
    bus.$on('smb-mode', function (mode) {
      self.smbMode = mode;
    });
    bus.$on('set-project-city',function(c) {
      if (c) {
        self.propTmpFilter.city = c.o;
        self.propTmpFilter.prov = c.prov;
        self.propTmpFilterVals.city = c.n;
        self.propTmpFilterVals.prov = c.p;
        if (self.viewMode == 'map') {
          for (let i of ['city','prov','cmty']) {
            self.propTmpFilter[i] = '';
            self.propTmpFilterVals[i] = '';
          }
        }
        self.recenterMapForCities(c);
        self.doSearch({clear:true});
      }
    });
    // bus.$on('proj-retrived', function(proj){
    //   self.curProj = proj;
    //   toggleModal('projDetailModal','open');
    //   setTimeout(function () {
    //     bus.$emit('set-loading', false);
    //   }, 0);
    // });
    bus.$on('get-project-list',function() {
      self.doSearch({clear:true});
    });
    if(vars.id && vars.mapmode=='projects'){
      setTimeout(() => {
        window.bus.$emit('prop-changed',{_id:vars.id,isProj:true});        
      }, 300);
    };
    window.addEventListener('click', checkAndSendLogger);
  },
  methods: {
    // goBack2(opt){
    //   window.goBack2(opt)
    // },
    inquery(){
      var r = {
        mbl:'9056142609',
        eml:this.dispVar.defaultEmail || '<EMAIL>',
        uid:'hide',
        message:'I want to advertise or co-op projects'
      };
      window.bus.$emit('smb-mode','contact');
      window.bus.$emit('toggle-contact-smb',r);
    },
    onClickSearchBar() {
      var lang = this.dispVar.lang || 'en';
      if (vars.src == 'nativeAutocomplete') {
        window.rmCall(':ctx::cancel')
      }
      this.goTo({url:'/1.5/autocomplete?referer=index&lang='+lang});
    },
    getCityInfo() {
      var self = this;
      let params = {
        city:this.propTmpFilter.city,
        prov:this.propTmpFilter.prov,
        cmty:this.propTmpFilter.cmty,
        saletp:this.propTmpFilter.saletp
      }
      // v6.1.3 autocomplete did not pass prov, ignore req
      if (!params.prov) {
        return;
      }
      self.$http.post('/1.5/prop/stats/briefCityInfo', params).then(
        function (ret) {
          ret = ret.data;
          if (ret.e || ret.err) {
            window.bus.$emit('flash-message', (ret.e||ret.err));
          } else {
            self.stat = ret.stat;
          }
        },
        function (ret) {
          console.error( "server-error" );
          // RMSrv.dialogAlert( "server-error" );
        }
      );
    },
    goToStat() {
      var {city,prov,cmty} = this.propTmpFilter;
      var url = '/1.5/prop/stats';
      var city = {o:city, p:prov, n:this.propTmpFilterVals.city};
      url = this.appendCityToUrl(url, city);
      // var url = `/1.5/prop/stats?city=${city}&prov=${prov}`;
      if(cmty) {
        var cmtyName = this.propTmpFilterVals.cmty;
        url = `${url}&cmty=${encodeURIComponent(cmty)}&cmtyName=${encodeURIComponent(cmtyName)}`;
      }
      trackEventOnGoogle('mapSearchList','openStats');
      return window.location = url;
    },
    showAd(e) {
      e.stopPropagation();
      console.log(123);
      if (!this.dispVar.isLoggedIn) {
        var url = '/1.5/user/login#index';
        url = this.appendDomain(url);
        return RMSrv.closeAndRedirectRoot(url)
      }
      var url = '/1.5/prop/topup/charge';
      RMSrv.openTBrowser(this.appendDomain(url), {nojump:true, title:this._('TOP Listing')});
    },
    // searchOpenHouse(opt) {
    //   var url = '/1.5/mapSearch?mode=list&d=/1.5/index';
    //   var city = {o:this.propTmpFilter.city, p:this.propTmpFilter.prov};
    //   url = this.appendCityToUrl(url, city,opt);
    //   if(this.propTmpFilter.cmty) {
    //     url = url+'&cmty='+this.propTmpFilter.cmty;
    //   }
    //   trackEventOnGoogle('mapSearchList','openHouse');
    //   return window.location = url;
    // },
    getCityList(opts={}){
      this.isSelectedTag = 1;
      if (this.propTmpFilter.ptype == 'Project') {
        var url = '/1.5/prop/projects/cities';
        RMSrv.getPageContent(url,'#callBackString',{title:this._('Select City')},function(val) {
          if (val == ':cancel') {
            return;
          }
          try {
            var c = JSON.parse(val);
          } catch (err) {
            var c = {};
          }
          window.bus.$emit('set-project-city', c);
        });
        // window.bus.$emit('open-project-city-modal');
        return;
      }
      this.clearModals();
      var optsStr = opts.doSearch?'search=1':'';
      // window.location = '/1.5/city/select?'+optsStr;
      // window.bus.$emit('select-city', opts);
      var cfg = {hide:false,title:this._('Select City')};
      var self = this;
      var url = this.appendDomain('/1.5/city/select?'+optsStr);
      RMSrv.getPageContent(url, '#callBackString', cfg, function(val){
        if (val == ':cancel') {
          console.log('canceled');
          return;
        }
        try {
          var cityRet = JSON.parse(val);
          var city = cityRet.city;
          if(city.p_ab){
            city.pOrig = city.p;
            city.p = city.p_ab;
          }
          window.bus.$emit('set-city',cityRet);
          self.getCityInfo();
        } catch (e) {
          console.error(e);
        }
        // console.log('returned city');
      });
    },
    logAdvFilter(obj){
      for(let k in obj){
        let v = obj[k];
        if(v && v !== 'null'){
          console.log(k,' = ',v)
        }
      }
      // console.log('=======end=====')
    },
    showAdvFilter(){
      this.isSelectedTag = 1;
      this.halfDrop = false;
      this.quickFilterMode = '';
      this.showTitleTypeSelect = false;
      this.propHalfDetail = false;
      var self = this;
      var showBar = '1'
      // if(self.viewMode == 'map'){
      //   showBar = '1'
      // }
      var url = self.appendDomain(`/1.5/mapSearch/advFilter?showBar=${showBar}&viewmode=`+this.viewMode+'&'+this.serializeData({
        prefix:'k',
        data:this.propTmpFilter
      })+'&'+this.serializeData({
        prefix:'v',
        data:this.propTmpFilterVals
      }));
      if (this.viewMode == 'map') {
        url += '&'+this.serializeData({prefix:'opt',data:{bbox:this.getMapBbox()}});
      }
      // window.location = url;
      // console.log(url);
      // return;
      var cfg = {toolbar:false};
      RMSrv.getPageContent(url, '#callBackString', cfg, function(val){
        if (val == ':cancel') {
          console.log('canceled');
          return;
        }
        try {
          // if (/^cmd-redirect:/.test(val)) {
          //   var url = val.split('cmd-redirect:')[1];
          //   return window.location = url;
          // }
          // alert(val);
          // console.log(val)
          var d = self.urlParamToObject(val);
          // alert(JSON.stringify(d));
          // self.logAdvFilter(d)
          var opt = self.parseSerializedFilter(d);
          // console.log(opt)
          self.searchModeSaletp = self.propTmpFilter.saletp;
          // should not setSearchMode here, this should be done in advFilterModal
          // self.setSearchMode(self.getSearchMode(opt));
          self.curSearchMode = self.getSearchMode(opt);
          // alert(JSON.stringify(self.propTmpFilter));
          // alert(self.propTmpFilter.max_mfee);
          self.getCityInfo();
          self.doSearch(opt)
          // window.bus.$emit('school-prop', d);
        } catch (e) {
          console.error(e);
        }
        // console.log('returned city');
      });
      // toggleModal('advFilterModal');
    },
    viewListing (prop) {
      window.bus.$emit('prop-changed',prop);
      // window.location = "/1.5/prop/detail/inapp?id="+prop._id;
    },
  }
})
