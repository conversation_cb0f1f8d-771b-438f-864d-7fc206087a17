// require('babel-polyfill');

import Vue from 'vue'
// import AppMapSearch from '../components/appMapSearch'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import UrlVars from '../components/url-vars'
import map_mixins from '../components/map_mixins'
import prop_mixins from '../components/prop_mixins'

import mapSearchLogic_mixins from '../components/mapSearchLogic_mixins'
import mapSearch_mixins from '../components/mapSearch_mixins'
import filters from '../components/filters'
// import progress from '../components/frac/ProgressBar'

import pagedata_mixins from '../components/pagedata_mixins'
// TODO:remove
import ListingShareDesc from '../components/frac/ListingShareDesc.vue'
// TODO:remove
import PropDetail from '../components/frac/PropDetail.vue'
// TODO:remove
import SchoolDetail from '../components/frac/SchoolDetail.vue'
import PropNeedLogin from '../components/frac/PropNeedLogin.vue'
// import Prop3pPromoteModal from '../components/frac/Prop3pPromoteModal.vue'
// import PropCreateWepageModal from '../components/frac/PropCreateWepageModal.vue'
import PropList  from '../components/frac/PropList.vue'
import PropPreviewElement  from '../components/frac/PropPreviewElement.vue'
import SchoolPreviewElement  from '../components/frac/SchoolPreviewElement.vue'
// TODO:remove
import Disclaimer  from '../components/frac/Disclaimer.vue'
import BrkgPhoneList  from '../components/frac/BrkgPhoneList.vue'
import RmBrkgPhoneList  from '../components/frac/RmBrkgPhoneList.vue'

// TODO:remove
import ShareDialog from '../components/frac/ShareDialog2.vue'
import FlashMessage from '../components/frac/FlashMessage.vue'
import Switch from '../components/frac/Switch.vue'
// TODO:remove
import CitySelectModal from '../components/frac/CitySelectModal.vue'
import rmsrv_mixins from '../components/rmsrv_mixins'
import LoadingBar from '../components/frac/LoadingBar.vue'
// import LoadingSquare from '../components/frac/LoadingSquare.vue'

import PropFavActions from '../components/frac/PropFavActions.vue'
import SchoolList from '../components/frac/SchoolList.vue'
// import DoubleGoLink from '../components/frac/DoubleGoLink.vue'
import SignUpForm from '../components/frac/SignUpForm.vue'

UrlVars.init();

Vue.use(VueResource);
// Vue.use(progress)
Vue.use(L10N);
// Vue.filter('time', filters.time);
// Vue.filter('number', filters.number);
Vue.filter('dotdate', filters.dotdate);
// Vue.filter('datetime', filters.datetime);
/* eslint-disable no-new */
window.bus = new Vue();
// https://segmentfault.com/q/1010000005800495
Vue.http.interceptors.push((request, next) => {
  var timeout;
	// 這裡改用 _timeout ，就不會觸發原本的
  if (request._timeout) {
  	// 一樣綁定一個定時器，但是這裡是只要觸發了，就立即返回 response ， 並且這邊自定義了 status 和 statusText
    timeout = setTimeout(() => {
      next(request.respondWith(request.body, {
           status: 408,
           statusText: 'Request Timeout'
      }));
    }, request._timeout);
  }
  next((response) => {
      clearTimeout(timeout);
  });
})
new Vue({
  mixins:[map_mixins,mapSearch_mixins,pagedata_mixins,rmsrv_mixins,mapSearchLogic_mixins,prop_mixins],
  el: 'body',
  computed:{
  },
  data () {
    return {
      signupTitle:this._('Book a Tour'),
      userForm:{
        ueml:dispVar.defaultEmail,
        sid:'',
        nm:'',
        eml:'',
        mbl:'',
        projShareUID:''
      },
      slideMenu: false,
      slideMenuTp: '',
      slideMenuElem:null,
      slideMenuLastScrolltop:0,
      slideMenuItems:[],
      slideMapping:{},
      datas:[
        'defaultEmail',
        'isPaytop',
        'isCip',
        'isVipUser',
        'isVipRealtor',
        'isRealtor',
        'isVisitor',
        'lang',
        'allowedShareSignProp',
        'propPtypes',
        'ptpTypes',
        'propSortMethods',
        'isApp',
        'isLoggedIn',
        'shareUID',
        'allowedPromoteProp',
        // 'jsGmapUrl',
        'hasFollowedRealtor',
        // 'propCounts',
        'reqHost',
        'shareAvt',
        'sessionUser',
        'domFilterVals',
        'bsmtFilterVals',
        'allowedEditGrpName',
        'shareLinks',
        'projShareUID',
        'userCity'
        // 'showSoldPriceBtn',
        // 'showSoldPrice',
      ],
      owner:{vip:1},
    };
  },
  mounted(){
  },
  components: {
    // PageSpinner,
    // Progress,
    ListingShareDesc,
    PropDetail,
    SchoolDetail,
    PropNeedLogin,
    // Prop3pPromoteModal,
    // PropCreateWepageModal,
    PropList,
    PropPreviewElement,
    SchoolPreviewElement,
    Disclaimer,
    BrkgPhoneList,
    RmBrkgPhoneList,
    ShareDialog,
    FlashMessage,
    Switch,
    CitySelectModal,
    LoadingBar,
    // LoadingSquare,
    PropFavActions,
    SchoolList,
    // DoubleGoLink,
    SignUpForm
  },
  created () {
    var self = this, bus = window.bus;
    function genSlideMapping(length) {
      var ret = [];
      for (let i = 1; i<length+1; i++) {
        ret.push({k:i+'',v:i+''});
        ret.push({k:i+'+',v:i+'+'});
      }
      return ret;
    }
    function initSlideMapping(self) {
      for (let tp of [['bdrms',5],['bthrms',5],['gr',4]]) {
        self.slideMapping[tp[0]] = genSlideMapping(tp[1]);
      }
      self.slideMapping['bsmt'] = [];
    }
    initSlideMapping(self);
    self.clearCache();
    //after getPageData
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      if (d.domFilterVals) {
        self.slideMapping['dom'] = d.domFilterVals;
      }
      if (d.bsmtFilterVals) {
        self.slideMapping['bsmt'] = d.bsmtFilterVals;
      }
      if (d.sessionUser) {
        for (let i of ['nm','eml','mbl']) {
          self.userForm[i] = d.sessionUser[i];
        }
      }
      if (d.projShareUID) {
        self.userForm.projShareUID = d.projShareUID;
      }
    });
    bus.$on('school-prop', function (d) {
      if (self.viewMode !== 'map') {
        self.viewMode = 'map'
      }
      self.propTmpFilter.saletp = d.type;
      toggleModal('schoolDetailModal','close');
      //set center and zoom
      if (d.sch && d.sch.loc) {
        self.recenterMapForCities({lat:d.sch.loc[0],lng:d.sch.loc[1]}, 14);
      }
      self.clearModals();
      self.doSearch();
    });
    bus.$on('school-retrieved', function (sch) {
      if (sch.e) {
        return window.bus.$emit('prop-need-login', sch.e);
      }
      toggleModal('schoolDetailModal','open');
    });
    bus.$on('prop-retrived', function (prop) {
      self.propHalfDetail = false;
      if (/^RM/.test(prop.id)) {
        self.userForm.rmid = prop.id;
      } else {
        self.userForm.sid = prop._id;
      }
      if (prop.e) {
        bus.$emit('set-loading', false);
        if (prop.ec == 1) {
          bus.$emit('prop-need-login', prop.e);
        }
      } else {
        if (!prop.img) {
          prop.img = self.picUrl(prop);
        }
        self.prop = prop;
        self.clearCache();
        toggleModal('propDetailModal');
        setTimeout(function () {
          bus.$emit('set-loading', false);
        }, 0);
      }
    });
  },
  ready () {
    this.slideMenuElem = document.querySelector('#advFilterModal .content');
    this.slideMapping.sort = this.dispVar.propSortMethods || {};
  },
  methods: {
    showDelSavedSearch(hist, idx){
      hist.del = true;
    },
    removeSavedSearch(hist={}, idx){
      if (!hist.k) {
        return;
      }
      this.httpSavedSearch({ts:hist.ts, q:hist.k, idx:idx, mode:'del'});
    },
    httpSavedSearch(d){
      function parseSavedSearch(l) {
        for (let i = 0; i < l.length; i++) {
          var hist = l[i], d = {};
          for (let j = hist.r.length; j > -1; j--) {
            var record = hist.r[j];
            if (record && ['src','saletp','city','bbox'].indexOf(record.k) > -1) {
              d[record.k] = record.v || record.vv;
              if (record.k == 'bbox') {
                hist.r.splice(j,1);
              }
            }
          }
          hist.del = false;
          hist.d = d;
        }
        return l;
      }
      var self = this;
      self.$http.post('/1.5/props/savedSearch', d).then(
        function (ret) {
          ret = ret.data;
          if (ret.e) {
            window.bus.$emit('flash-message', ret.e);
          } else {
            if (ret.l && ret.l.length) {
              self.slideMenuItems = parseSavedSearch(ret.l);
            } else if (d.mode == 'del') {
              window.bus.$emit('flash-message', ret.msg);
              self.slideMenuItems.splice(d.idx, 1);
            }
          }
        },
        function (ret) {
          console.error( "server-error" );
          // RMSrv.dialogAlert( "server-error" );
        }
      );
    },
    getSavedSearch(){
      // post to server
      this.httpSavedSearch({mode:'get'});
    },
    getCityList(opts={}){
      this.clearModals();
      window.bus.$emit('select-city', opts);
    },
    // closeListSearchTip(){
    //   this.listSearchTip = false;
    // },
    // getSchoolsInfo(prop, cfg){
    //   // var d = {lat1:ne.lat(),lat2:sw.lat(),lng1:ne.lng(),lng2:sw.lng(),mode:'mapBnds'};
    //   window.bus.$emit('get-school-info', {prop:prop, cfg:cfg});
    // },
    showSlideMenu(tp){
      this.slideMenuLastScrolltop = this.slideMenuElem.scrollTop;
      this.slideMenuElem.scrollTop = 0;
      this.slideMenu = !this.slideMenu;
      this.slideMenuTp = tp;
      if (['bdrms','bthrms','gr','dom','bsmt'].indexOf(tp)>-1) {
        if (this.slideMapping[tp]) {
          this.slideMenuItems = this.slideMapping[tp].slice();
        } else {
          this.slideMenuItems = [];
        }
        // this.slideMenuItems = this.slideMapping[tp] || [];
      } else if (tp == 'ptype'){
        this.slideMenuItems = this.dispVar.propPtypes.slice();
      } else if (tp == 'sort'){
        this.slideMenuItems = this.dispVar.propSortMethods.slice();
      } else if (tp == 'ptype2') {
        // console.log(this.ptype2s);
        this.slideMenuItems = this.ptype2s.slice();
      } else if (tp == 'advpref') {
        this.slideMenuItems = [];
        this.getSavedSearch();
      } else if (tp == 'cmty') {
        this.slideMenuItems = this.cmtyList;
      }
    },
    showAdvFilter(){
      this.halfDrop = false;
      this.quickFilterMode = '';
      this.showTitleTypeSelect = false;
      this.propHalfDetail = false;
      toggleModal('advFilterModal');
    },
    viewListing (prop) {
      // TODO: fix this
      window.bus.$emit('prop-changed',prop);
      // window.location = "/1.5/prop/detail/inapp?id="+prop._id;
    },
    closeSlideMenu(){
      this.slideMenuElem.scrollTop = this.slideMenuLastScrolltop;
      this.slideMenu = false;
      this.slideMenuTp = '';
    },
    setAdvSlideVal(i){
      var k = i.k, v = i.v;
      var tp = this.slideMenuTp;
      if (!tp) {
        return;
      }
      if (['bdrms','bthrms','gr','sort','dom','bsmt','cmty'].indexOf(tp)>-1) {
        this.propTmpFilter[tp] = k;
        this.propTmpFilterVals[tp] = v;
      } else if (tp == 'ptype'){
        this.propTmpFilter[tp] = k;
        this.propTmpFilterVals[tp] = v;
        this.propTmpFilter.ptype2 = [];
        this.propTmpFilterVals.ptype2 = [];
        // this.titleString = v;
        this.getPtype2s(k, v);
      } else if (tp == 'ptype2') {
        this.ptype2Select(k, v);
        return; //do not close
      } else if (tp == 'advpref') {
        // this.propTmpFilter[tp] = k;
        this.resetFilter();
        if (k.ptype !== this.propTmpFilter.ptype) {
          this.getPtype2s(k.ptype);
        }
        this.propTmpFilter = Object.assign(this.propTmpFilter, k);
        var tmp = {};
        for (let tp of i.r) {
          tmp[tp.k]=tp.vv || tp.v;
        }
        this.propTmpFilterVals = Object.assign(this.propTmpFilterVals, tmp);
        if (i.d.city) {
          this.propTmpFilterVals.city = i.d.city;
        }
        if (i.d && i.d.bbox) { //show bbox on map
          this.viewMode = 'map'; //this will set bbox
          this.doSearch({mapView:true, bbox:i.d.bbox});
        } else {
          this.viewMode = 'list'; //this will set bbox
          this.doSearch({clear:true});
        }
        toggleModal("advFilterModal",'close');
      }
      this.closeSlideMenu();
    }
  }
})
