// require('babel-polyfill');

import Vue from 'vue'
import Syschat from '../components/syschat'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import filters from '../components/filters'
import rmsrv_mixins from '../components/rmsrv_mixins'

Vue.use(VueResource);
Vue.use(L10N);
Vue.filter('time', filters.time);
Vue.filter('currency', filters.currency);
/* eslint-disable no-new */
window.bus = new Vue();
new Vue({
  mixins:[rmsrv_mixins],
  el: '#vueBody',
  mounted: function() {
    this.$getTranslate(this);
  },
  components: { Syschat }
})
