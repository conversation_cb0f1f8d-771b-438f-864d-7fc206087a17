// require('babel-polyfill');

import Vue from 'vue'
import forumEdit from '../components/forum/forumEdit'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import UrlVars from '../components/url-vars'

UrlVars.init();

Vue.use(VueResource);
Vue.use(L10N);
window.bus = new Vue();

Vue.http.interceptors.push(window.onhttpError);

/* eslint-disable no-new */
new Vue({
  el: '#forumEdit',
  mounted: function() {
    this.$getTranslate(this);
  },
  components: { forumEdit }
})
