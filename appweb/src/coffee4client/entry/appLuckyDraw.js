// require('babel-polyfill');

import Vue from 'vue'
import VueResource from 'vue-resource'
import L10N from '../components/vue-l10n'

import ShareDialog from '../components/frac/ShareDialog.vue'
import FlashMessage from '../components/frac/FlashMessage.vue'
import filters from '../components/filters'
import PropDetail from '../components/frac/PropDetail.vue'
// import GetAppBar from '../components/frac/GetAppBar.vue'
import rmsrv_mixins from '../components/rmsrv_mixins'

Vue.use(VueResource);
Vue.use(L10N);
// Vue.config.debug = true;
// Vue.config.devtools = true;
/* eslint-disable no-new */
window.bus = new Vue();
new Vue({
  filters:{
    time:filters.time,
    day:filters.day,
    dotdate:filters.dotdate,
    number:filters.number,
  },
  el: '#ldrawCtrl',
  mixins:[rmsrv_mixins],
  components:{
    ShareDialog,
    FlashMessage,
    PropDetail
    // GetAppBar
  },
  data: {
    dispVar:{
      lang:'en' //hide lang select in share dialog
    },
    submitted: false,
    show:false,
    evt: vars.evt || {},
    rand: vars.rand || [],
    randOne: {},
    randIdx:0,
    isApp: vars.isApp || false,
    isWeChat: vars.isWeChat || false,
    mybid: vars.mybid || null,
    expd: vars.expd || false,
    noend:vars.noend || false,
    host: vars.host || '',
    showDlBar: false,
    avt: vars.avt || '/img/logo_s.png',
    shareUrl: vars.shareUrl?decodeURIComponent(vars.shareUrl):'',
    mbl: vars.mbl || null,
    price:''
  },
  created () {
    // if (vars.mybid != null) {
    //   this.rand = [];
    // }
    var self = this;
    this.price = vars.mybid!=null?''+parseInt(vars.mybid):'';
    if (this.rand.length) {
      this.showRand();
    }
    setTimeout(function () {
      if (self.computedTitle) {
        document.title = self.computedTitle;
      }
    }, 500);
    window.bus.$on('prop-detail-close', function (d) {
      self.showDlBar = false;
    });
    window.bus.$on('prop-retrived', function (prop) {
      self.propHalfDetail = false;
      if (prop.e) {
        if (prop.ec == 1) {
          bus.$emit('prop-need-login', prop.e);
        }
      } else {
        // if (!prop.img) {
        //   prop.img = self.picUrl(prop);
        // }
        // self.prop = prop;
        self.showDlBar = true;
        toggleModal('propDetailModal');
      }
    });
  },
  computed:{
    computedShowDlBar:function () {
      if (!this.isApp) {
        return true;
      }
      return this.showDlBar;
    },
    dlWording:function () {
      if (this.expd) {
        return '免费下载房大师';
      }
      return '下载房大师APP,参与猜房价活动';
    },
    showMybid:function () {
      // if (this.expd == true) {
      //   return true;
      // }
      return (this.mybid != null) && (!this.isApp);
    },
    computedDesc:function () {
      if (this.evt.desc) {
        return this.evt.desc;
      }
      return '房大师';
    },
    computedTitle:function () {
      if (this.evt.title) {
        return this.evt.title;
      }
      return '我猜这房子值'+this.compute(this.mybid)+'万，你觉得我能中奖么？';
      // return '房大师 '+this.evt.sp_name+'猜价格赢大奖';
    }
  },
  methods:{
    copyWeChat(){
      var emailLink = document.querySelector('#RMWeChatOA');
      var range = document.createRange();
      range.selectNode(emailLink);
      window.getSelection().addRange(range);
      try {
        // Now that we've selected the anchor text, execute the copy command
        var successful = document.execCommand('copy');
        var msg = successful ? 'successful' : 'unsuccessful';
        console.log('Copy email command was ' + msg);
        if (successful) {
          if (this.isApp) {
            RMSrv.dialogAlert('复制成功');
          } else {
            alert('复制成功');
          }
        }
      } catch(err) {
        console.log('Oops, unable to copy');
      }
      // Remove the selections - NOTE: Should use
      // removeRange(range) when it is supported
      window.getSelection().removeAllRanges();
    },
    showRand(){
      var self = this;
      self.show = true;
      self.randOne = self.rand[0];
      setInterval(function () {
        self.show = false;
        self.randIdx = (self.randIdx+1)%self.rand.length;
        setTimeout(function () {
          self.randOne = self.rand[self.randIdx];
          self.show = true;
        }, 1300);
      }, 5000);
    },
    compute(price){
      if (price) {
        return Math.floor(parseInt(price)/10000);
      }
      return 0;
    },
    showDl(){
      window.location = this.host+'/app-download?lang=zh-cn';
    },
    viewPropDetail(){
      var propId = this.evt.prop_id, url, self = this;
      function showPropUrlApp() {
        if (self.evt.prop_url) {
          return RMSrv.showInBrowser(self.evt.prop_url);
        }
        if (propId.substr(0,2) == 'RM') {
          url = self.host+'/1.5/promote/market?ml_num='+propId;
          // http://app.test:8081/1.5/rmprop/detail/RM1-00159?lang=zh-cn&wDl=1&aid=55999841daf10a33d53e79d6
          return window.location = url;
        } else {
          url =  self.host+'/1.5/search/prop?d=/1.5/index&id='+propId;
          window.bus.$emit('prop-changed',{_id:propId});
        }
      }
      function showPropUrl() {
        if (self.evt.prop_url) {
          url = self.evt.prop_url;
        } else if (propId.substr(0,2) == 'RM') {
          url = self.host+'/1.5/rmprop/detail/'+propId+'?lang=zh-cn&wDl=1';
          return window.location = url;
        } else {
          url =  self.host+'/1.5/prop/detail?id='+propId+'&lang=zh-cn&wDl=1';
          // window.bus.$emit('prop-changed',{_id:propId});
        }
        return window.location = url;
      }
      if (this.isApp) {
        showPropUrlApp();
      } else {
        showPropUrl();
      }
      // window.location = url;
    },
    showShare(){
      RMSrv.showSMB('show');
    },
    isValidInputs(){
      var self = this;
      if (!self.mbl) {
        window.bus.$emit('flash-message','请输入手机号以便于我们联系您');
        return false;
      }
      if (self.price && self.price < (self.evt.prop_lp/2)) {
        window.bus.$emit('flash-message','是否太便宜了呢？');
        return false;
      }
      return true;
    },
    placeBid( action ){
      var self = this;
      if (!self.isValidInputs()) {
        return;
      }
      self.$http.post('/spevent/bid', {mbl:this.mbl, price:this.price, evtid:this.evt._id}).then((ret) => {
        // success callback
        ret = ret.data;
        if (!ret.ok) {
          RMSrv.dialogAlert(ret.e);
        } else {
          // self.submitted = true;
          self.mybid = parseInt(self.price);
          // use rmsrv dialogConfirm
          // window.bus.$emit('flash-message',ret.msg);
          function _doShare(r) {
            if (r === 2) {
               self.showShare();
            } else {
              console.log('canceld share');
            }
          }
          RMSrv.dialogConfirm(ret.msg, _doShare, "Message", ['取消', '分享']);
        }
      }, (ret) => {
        // error callback
        console.error(ret);
      });
    }
  }
});
