import Vue from 'vue'
import AppYellowpageCategory from '../components/yellowpage/appYellowpageCategory'
import L10N from '../../coffee4client/components/vue-l10n'
import VueResource from 'vue-resource'
import UrlVars from '../../coffee4client/components/url-vars'

UrlVars.init();

Vue.use(VueResource);
Vue.use(L10N);
window.bus = new Vue();
Vue.http.interceptors.push(window.onhttpError);

/* eslint-disable no-new */
new Vue({
  el: '#appServicesCategory',
  mounted: function() {
    this.$getTranslate(this);
  },
  components: {
    AppYellowpageCategory
  }
})
