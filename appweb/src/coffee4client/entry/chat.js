// require('babel-polyfill');

import Vue from 'vue'
import Chat from '../components/chat'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import rmsrv_mixins from '../components/rmsrv_mixins'
import filters from '../components/filters'

Vue.use(VueResource);
Vue.use(L10N);
Vue.filter('datetime', filters.datetime);
Vue.filter('currency', filters.currency);

window.bus = new Vue();
/* eslint-disable no-new */
new Vue({
  mixins:[rmsrv_mixins],
  el: '#vueBody',
  mounted: function() {
    this.$getTranslate(this);
  },
  components: { Chat }
})
