// require('babel-polyfill');

import Vue from 'vue'
import AppListSearch from '../components/appListSearch'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import prop_mixins from '../components/prop_mixins'
import rmsrv_mixins from '../components/rmsrv_mixins'
import filters from '../components/filters'
import UrlVars from '../components/url-vars'

UrlVars.init();

Vue.use(VueResource);
Vue.use(L10N);
Vue.filter('time', filters.time);
Vue.filter('number', filters.number);
Vue.filter('propPrice', filters.propPrice);
Vue.filter('dotdate', filters.dotdate);
Vue.filter('datetime', filters.datetime);
// Vue.config.debug = true;
// Vue.config.devtools = true;
/* eslint-disable no-new */
window.bus = new Vue();
new Vue({
  mixins:[prop_mixins,rmsrv_mixins],
  el: '#vueBody',
  mounted: function() {
    this.$getTranslate(this);
  },
  components: { AppListSearch }
})
