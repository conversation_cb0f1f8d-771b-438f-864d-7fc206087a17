// require('babel-polyfill');

import Vue from 'vue'
import AppLogin from '../components/appLogin'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import VeeValidate from 'vee-validate';

Vue.use(VueResource);
Vue.use(L10N);
Vue.use(VeeValidate);
/* eslint-disable no-new */
window.bus = new Vue();
new Vue({
  el: '#vueBody',
  mounted: function() {
    this.$getTranslate(this);
  },
  components: { AppLogin }
})
