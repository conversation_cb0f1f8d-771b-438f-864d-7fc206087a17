
import Vue from 'vue'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'

import UrlVars from '../components/url-vars'
import appForumDetailFooter from '../components/forum/appForumDetailFooter'
import appForumDetailContent from '../components/forum/appForumDetailContent'
import propInfo from '../components/prop/propInfo'


import FlashMessage from '../components/frac/FlashMessage'
import LZString from 'lz-string'

import cpmBanner from '../components/cpm/cpmBanner'
import rmsrv_mixins from '../components/rmsrv_mixins'
import forum_common_mixins from '../components/forum/forum_common_mixins'
import forum_detail_mixins from '../components/forum/forum_detail_mixins'

UrlVars.init();

Vue.use(VueResource);
Vue.use(L10N);
window.bus = new Vue();

Vue.http.interceptors.push(window.onhttpError);
new Vue({
  el: '#forumDetail',
  components: {
    appForumDetailContent,
  },
  mounted () {
    this.$getTranslate(this);
    this.post = vars.post;
  }
});
window.vm = new Vue({
  mixins:[rmsrv_mixins,forum_common_mixins,forum_detail_mixins],
  el: '#forumDetailFooter',
  data () {
    return {
    };
  },
  methods: {
  },
  mounted () {
    this.$getTranslate(this);
    var self = this;
    this.post = vars.post;
    self.dispVar = Object.assign(self.dispVar, vars.dispVar);

  },
  components: {
    appForumDetailFooter,
    FlashMessage,
  }
})
window.cpmBannerVm = new Vue(cpmBanner);
window.propInfp = new Vue({
  el: '#forumProp',
  components:{
    propInfo
  },
  mounted () {
    this.$getTranslate(this);
    var self = this;
    // this.post = vars.post;
    self.dispVar = Object.assign({}, vars.dispVar);
  },
})
