// require('babel-polyfill');

import Vue from 'vue'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import UrlVars from '../components/url-vars'
import SchoolListElement from '../components/frac/SchoolListElement.vue'
import schoolDetailFooter from '../components/school/schoolDetailFooter'

import rmsrv_mixins from '../components/rmsrv_mixins'
UrlVars.init();

Vue.use(VueResource);
Vue.use(L10N);
window.bus = new Vue();

Vue.http.interceptors.push(window.onhttpError);
window.bus = new Vue();
window.footer = new Vue({
  mixins:[rmsrv_mixins],
  el: '#event-detail',
  data () {
    return {
      post:{},
      dispVar:{},
      type:'private'
    };
  },
  methods: {
  },
  mounted () {
    this.$getTranslate(this);
    var self = this;
    this.post = vars.post;
    var bus = window.bus;
    bus.$on('school-prop', function (d) {
      var url = '/1.5/mapSearch', self = this;
      if (d.sch && d.sch.loc) {
        url += '?loc=' + d.sch.loc[0] + ',' + d.sch.loc[1];
        url += '&zoom=15';
        url += '&saletp='+d.type;
      }
      var callBackStr = ':ctx:'+url.split('?')[1];
      if (vars.redirect) {
        callBackStr = ":ctx:cmd-redirect:"+url;
      }
      // console.log(callBackStr);
      window.rmCall(callBackStr);
      // return window.location = url;
    });
  },
  components: {
    schoolDetailFooter,
    SchoolListElement
  }
})
