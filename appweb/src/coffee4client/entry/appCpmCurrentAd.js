// require('babel-polyfill');

import Vue from 'vue'
import cpmBanner from '../components/cpm/cpmBanner'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import UrlVars from '../components/url-vars'
import filters from '../components/filters'

UrlVars.init();

Vue.use(VueResource);
Vue.use(L10N);
Vue.filter('time', filters.time);
// Vue.config.debug = true;
// Vue.config.devtools = true;
/* eslint-disable no-new */
window.bus = new Vue();
new Vue({
  el: '#vueBody',
  mounted: function() {
    this.$getTranslate(this);
    let data = {loc:"MP1", city:vars.city,prov:vars.prov,ptype:vars.ptype,isWebView:true};
    window.bus.$emit('get-current-ad',data);
  },
  components: { cpmBanner }
})
