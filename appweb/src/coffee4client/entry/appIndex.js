// require('babel-polyfill');
// NOTE: no longer used
import Vue from 'vue'
// import AppIndex from '../components/appIndex'
import L10N from '../components/vue-l10n'
import VueResource from 'vue-resource'
import rmsrv_mixins from '../components/rmsrv_mixins'
import autocomplete_mixins from '../components/autocomplete_mixins'
import PropList  from '../components/frac/PropList.vue'
import AutocompleteListWrapper  from '../components/frac/AutocompleteListWrapper.vue'

import PageSpinner from '../components/frac/PageSpinner.vue'
import FeaturePopup from '../components/frac/FeaturePopup.vue'
import BottomNav from '../components/frac/BottomNav.vue'
import LangSelectCover from '../components/frac/LangSelectCover.vue'
import CitySelectModal from '../components/frac/CitySelectModal.vue'
// import Swipe from '../components/frac/swipe.vue'
// import SwipeItem from '../components/frac/swipe-item.vue'
import FlashMessage from '../components/frac/FlashMessage.vue'
import LoadingBar from '../components/frac/LoadingBar.vue'
import LazyImage from '../components/frac/LazyImage.vue'
import SignUpForm from '../components/frac/SignUpForm.vue'


import pagedata_mixins from '../components/pagedata_mixins'
import prop_mixins from '../components/prop_mixins'
// import IndexMoreOption from '../components/frac/IndexMoreOption.vue'
// import IndexMoreCategory from '../components/frac/IndexMoreCategory.vue'

// import IndexRealtorMiddlePage from '../components/frac/IndexRealtorMiddlePage.vue'
// import VipUpgradeTip from '../components/frac/VipUpgradeTip.vue'
import filters from '../components/filters'
import UrlVars from '../components/url-vars'

UrlVars.init();
Vue.use(VueResource);
Vue.use(L10N);
Vue.filter('propPrice', filters.propPrice);
Vue.filter('monthNameAndDate', filters.monthNameAndDate);
Vue.filter('currency', filters.currency);
Vue.filter('dotdate', filters.dotdate);

// Vue.config.debug = true;
// Vue.config.devtools = true;
/* eslint-disable no-new */

// 首页右上角判断登陆逻辑，勿删
{/* <a v-cloak href="javascript:void 0" @click="goTo({url:'/1.5/mapSearch?d=/1.5/index&gps=1',googleCat:'homeTopBar',googleAction:'openNearby'});" v-show="!focused && dispVar.isLoggedIn" class="chat pull-right"><img src="/img/icon_index_nearby_trans.png"></img><span>{{_('Nearby','indexSearch')}}</span></a>
<a v-cloak id="loginText" href="javascript:void 0" @click="goTo({login:true, url:'/1.5/user/login',googleCat:'homeTopBar',googleAction:'openLogin'})" v-show="!focused && !dispVar.isLoggedIn" class="pull-right">{{_('Login')}}</a><a href="javascript:void 0" @click="goBack()" v-show="focused" class="pull-left icon fa fa-back"></a> */}

window.bus = new Vue();
new Vue({
  template:`
  <div>
  <div style="display:none" id="rmPageBarColor">:name:mainTheme</div>
  <lang-select-cover :disp-var="dispVar" :lang="dispVar.lang" :old-ver="dispVar.langCoverOldVer"></lang-select-cover>
    <header class="bar bar-nav">
      <a v-cloak href="javascript:;" @click="getCityList('setUserCity')" v-show="!focused" class="pull-left city">
        <span class="cityName">{{dispVar.userCity.n || dispVar.userCity.o}}
        </span>
        <span class="icon icon-down-nav"></span>
      </a>
      <div class="nativeSearchEntry" v-show="isNativeSearch" @click="onClickSearchBar"></div>
      <input v-cloak v-model="searchStr" @focus="" :placeholder="computed_placeholder" :class="{focused:focused}" class="search"/>
      <a v-cloak id="searchBtn" @click="searchAutocomplete({all:true})" class="fa fa-rmsearch"></a>
      <a v-cloak href="javascript:void 0" @click="goTo({url:'/1.5/mapSearch?d=/1.5/index',googleCat:'homeTopBar',googleAction:'openNearby'});" class="chat pull-right"><img src="/img/icon_index_nearby_trans.png"></img><span>{{_('MAP','indexSearch')}}</span></a>
    </header>
    <div class="content" style="z-index:1;" v-show="!focused && indexMode == 'index' && showItems" v-cloak>
      <prop-list :list.sync="propItems" :disp-var="dispVar" :show-fav="false"></prop-list>
      <button @click="showMoreProps()" id="showMoreBtn" class="btn btn-block btn-positive" v-show="hasMorePropInList">{{_('Show More')}}</button>
    </div>
    <div id="SignupModal" class="modal modal-fade" v-if="!hasWechat">
      <header class="bar bar-nav">
      </header>
      <div class="content">
        <sign-up-form :feedurl="feedurl" :owner="{vip:true}" :user-form="userForm" :title="signupTitle" ></sign-up-form>
        <div class="close" @click="toggleModal('SignupModal')">
          <img src="/img/staging/close.png">
        </div>
      </div>
    </div>
    <div id="autocompleteWrapper" class="content" v-show="focused && indexMode == 'index'" v-cloak>
      <autocomplete-list-wrapper :show-autocompletes="showAutocompletes" :hist="hist" :glist="gautocompletes" :alist="autocompletes" :cnt="cnt" :acloading.sync="acloading" :disp-var="dispVar"></autocomplete-list-wrapper>
    </div>
    <city-select-modal :show-subscribe="true" :need-loc="true" :cur-city.sync="dispVar.userCity" :loading.sync="loading"></city-select-modal>
  </div>
  `,
  mixins:[rmsrv_mixins, prop_mixins, pagedata_mixins, autocomplete_mixins],
  el: '#vueBody',
  computed:{
    // computedShowIndexReno:function(){
    //   var dispVar = this.dispVar;
    //   return dispVar.showIndexReno && (dispVar.lang != 'en') && (dispVar.userCity.p == 'Ontario');
    // },
    // computedProjAdClass:function () {
    //   if (!(this.indexAd && this.indexAd.length)) {
    //     return '';
    //   }
    //   if (this.indexAd.length == 1) {
    //     return 'one';
    //   } else if (this.indexAd.length == 2) {
    //     return 'two';
    //   } else {
    //     return 'three';
    //   }
    // },
    // spWrapperWidth:function() {
    //   return this.calculatedSpWidth * this.indexAd.length;
    // },
    computed_placeholder:function () {
      return this._('Input Address/Listing ID');
    },
    // showIndicators:function () {
    //   return this.banners.length>1?true:false;
    // },
    // isVisitorNoFollow:function () {
    //   if (!this.dispVar.isLoggedIn) {
    //     return true;
    //   }
    //   if (!this.dispVar.isRealtor && !this.dispVar.hasFollowedRealtor) {
    //     return true;
    //   }
    //   return false;
    // }
  },
  beforeMount () {
    this.$getTranslate(this);
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    // var cacher, headline, rcmd;
    // this.cacher = new Cacher();
    // rcmd = this.cacher.getCached('rcmd');
    // headline = this.cacher.getCached('headline');
    // if (rcmd) {
    //   this.handleRecommend(rcmd);
    // }
    // if (headline) {
    //   this.handleHeadline(headline);
    // }
    this.getPageData(this.datas,{},true);
  },
  mounted () {
    function getAndroidVersion(ua) {
      ua = (ua || navigator.userAgent).toLowerCase();
      var match = ua.match(/android\s([0-9\.]*)/);
      return match ? match[1] : false;
    }
    var self = this, bus = window.bus;
    if (getAndroidVersion() && parseFloat(getAndroidVersion()) < 4.4) {
      self.oldVerBrowser = true;
    }
    // if(RMSrv && RMSrv.setBarColor){
    //   RMSrv.setBarColor({name:'mainTheme'})
    // }
    //after getPageData
    // self.rcmdHeight = parseInt(window.innerWidth/1.6);
    // self.calculatedSpWidth = parseInt((window.innerWidth-30)/2.05);
    // NOTE: test google analytics loaded
    // setTimeout(function () {
    //   var ggjs = document.querySelector('script[src="//www.google-analytics.com/analytics.js"]');
    //   if (!ggjs) {
    //     ggjs = {};
    //   }
    //   alert(ggjs.src);
    // }, 2000);
    // RMSrv.setItemObj('@flag.appInited','5.3.0',function(err){
    //   RMSrv.dialogAlert(ret);
    // });
    // RMSrv.getItemObj('@flag.appInited',function(ret){
    //   RMSrv.dialogAlert(ret);
    // });
    // RMSrv.removeItemObj('@flag.appInited');
    // NOTE: onReady not always work, tested on simulator, added 100 timeout to ready
    // setTimeout(function () {
    //   RMSrv.action('pushToken');
    // }, 1000);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      self.isNativeSearch = self.isNewerVer(self.dispVar.coreVer,'6.0.1');
      self.hasDeviceId = self.isNewerVer(self.dispVar.coreVer,'6.1.5');
      if (!self.updatedPN && d.updatePN && localStorage.pn) {
        self.postPN();
      }
      // if (d.showSoldPriceBtn != null && d.showSoldPriceBtn !== true) {
      //   self.newDrawers2.splice(4,1);
      //   self.newDrawers2.push({ t:'More',
      //     img:require('../../webroot/public/img/icon_index_more.png'),
      //     url:'#'
      //   });
      // }
      if (d.userCity) {
        self.rcmdCity = {o:d.userCity.o, n:d.userCity.n, lat:d.userCity.lat, lng:d.userCity.lng, p:d.userCity.p};
        if (d.userCity.cnt) {
          self.userCityCnt = d.userCity.cnt;
        }
      }
      if (self.dispVar.sessionUser && self.dispVar.sessionUser.eml) {
        for (let i of ['nm','eml','mbl','fnm']) {
          self.userForm[i] = self.dispVar.sessionUser[i];
        }
        if(!self.userForm.nm || self.userForm.fnm){
          self.userForm.nm = self.userForm.fnm || d.sessionUser.nm_en || d.sessionUser.nm_zh
        }
      }
      function showCover() {
        // return window.localStorage.lang?false:true;
        if (/src=setting/.test(document.URL)) {
          return true;
        }
        // if (dispVar.coreVer > 5.3 && dispVar.appVer < 5.3) {
        //   return true
        // }
        var lang;
        // alert(self.dispVar.langCover+'::'+self.readCookie('locale'));
        return (lang = self.readCookie('locale'))?false:true;
        // var dateThen = typeof localStorage !== "undefined" && localStorage !== null ? localStorage.lastCoverHideDate : void 0;
        // if (!dateThen) {
        //   return true;
        // }
        // return (dateThen = new Date(dateThen)) && ((new Date() - dateThen) > (3600000 * 12))
      }
      // console.log(self.dispVar.langCover + ':' + showCover());
      // self.dispVar.langCover ||
      if (showCover()) {
        setTimeout(function () {
          toggleModal('coverPageModal','open');
        }, 0);
      }
      if (d.lang && RMSrv.setAppLang && self.dispVar.lang) {//&& !localStorage.lang
        // localStorage.lang = self.dispVar.lang;
        // alert(self.dispVar.lang)
        if ('function' == typeof RMSrv.onReady) {
          RMSrv.onReady(()=>{
            // alert(Object.keys(RMSrv))
            // RMSrv.appInstalledChecker('wechat',(ret)=>{
            //   alert(ret)
            // })
            // RMSrv.action({tp:'AppInstalledChecker', name:'wechat'})
            if (self.hasDeviceId) {
              RMSrv.getUniqueId((id)=>{
                if (id && id.length > 5) {
                  self.logUserId(id)
                }
              })
            }
            // if (RMSrv.hasWechat){
            //   RMSrv.hasWechat((has)=>{
            //     if (has!=null) {
            //       self.logUserHasWechat(has)
            //       self.hasWechat = has
            //       if(!has){
            //         self.hideElemById('#banners')
            //         self.hideElemBySelectors('#indexAd > div.sp-holder  div.img > div')
            //         // self.showSignupOnclick('#drawers > div.swiper-wrapper  a.drawer.link-agent')
            //         self.hideDrawAndMove()
            //         setTimeout(() => {
            //           if(!self.dispVar.isVipPlus){
            //             self.hideElemById('#realtorTodos')
            //             self.hideElemById('#promotionsMarketing')  
            //           }
            //         }, 100);
            //       }
            //     }
            //   })
            // }
            var location = window.location.href;
            // var arr = location.split("/");
            // var domain = arr[0] + "//" + arr[2];
            var domain = self.extractDomain(location);
            RMSrv.setItemObj({key:'Cookie@'+domain,value:document.cookie,stringify:true,store:true});
            if(RMSrv.setCookie){
              RMSrv.setCookie();
            }
            RMSrv.action('pushToken');
            RMSrv.setAppLang(self.dispVar.lang);
            if (RMSrv.refreshSystemValue) {
              RMSrv.refreshSystemValue();
            }
            if (RMSrv.removeItemObj && !window.localStorage.lastRMMapPosTs) {
              window.localStorage.lastRMMapPosTs = '202004230653';
              RMSrv.removeItemObj('lastMapPosition')
            }
            // alert(RMSrv.refreshSystemValue)
            // if (RMSrv.refreshSystemValue) {
            //   RMSrv.refreshSystemValue({})
            // }
            //give ready signal to native for pn
            // window.postMessage(JSON.stringify({tp:'alert',msg:'test msg'}));
          })
        }
      }
      if (!self.inited) {
        self.initIndexData();
      }
    });
    // bus.$on('lazy-image-onpress', function (e, b) {
    //   console.log(e,b);
    //   var el=e.target||e.srcElement;
    //   if (b && b._id) {
    //     var index = parseInt(el.getAttribute('dataindex'));
    //     self.clickedAd(b,'homeAds',index);
    //   }
    // });
    bus.$on('index-view-mode', function (mode) {
      self.indexMode = mode;
    });
    bus.$on('index-redirect-goto', function (dr) {
      self.goTo(dr);
    });
    bus.$on('prop-changed', function (prop) {
      // window.location = "/1.5/prop/detail/inapp?id="+prop._id;
      if (/^RM/.test(prop.id)) {
        propId = prop.id;
      } else {
        propId = prop._id;
      }
      if (!self.isNewerVer(self.dispVar.coreVer, '5.3.0')) {
        return window.location = "/1.5/search/prop?d=/1.5/index&id=" + propId;
      }
      var cfg = {hide:false,title:self._('RealMaster')};
      var propId;
      var url = "/1.5/prop/detail/inapp?lang="+self.dispVar.lang+"&id="+propId;//+'&mode='+self.appMapMode;
      url = self.appendDomain(url);
      RMSrv.getPageContent(url, '#callBackString', cfg, function(val){
        if (val == ':cancel') {
          console.log('canceled');
          return;
        }
        if (/^redirect/.test(val)) {
          return window.location = val.split('redirect:')[1]
        }
        try {
          // var val = 'loc=43.5723199141038,-79.5785565078259&zoom=15&saletp=lease';
          if (/^cmd-redirect:/.test(val)) {
            // return alert(val);
            var url = val.split('cmd-redirect:')[1];
            return window.location = url;
          }
          // alert(val);
          window.location = '/1.5/mapSearch?d=/1.5/index&'+val;
          // var d = self.urlParamToObject(val);
          // alert(JSON.stringify(d));
          // if (d.loc) {
          //   self.recenterMapForCities({lat:d.loc[0],lng:d.loc[1]}, 14);
          // }
          // window.bus.$emit('school-prop', d);
        } catch (e) {
          console.error(e);
        }
        // console.log('returned city');
      });
    });
    bus.$on('set-city', function (d) {
      var url = '',
          city = d.city,
          data = {city:city};
      if (self.mode == 'subscribe') {
        url = '/1.5/index/subscribe';
      } else {//set user city
        url = '/1.5/settings/city';
      }
      // post to server and get data for mode
      self.$http.post(url, data).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            // reset recommend
            // console.log(ret);
            toggleModal('citySelectModal');
            if (ret.msg) {
              window.bus.$emit('flash-message', ret.msg);
            }
            setTimeout(()=>{
              location.reload();
            },500)
            // if (self.mode == 'setUserCity') {
            //   self.rcmdCity = ret.city;
            //   self.dispVar.userCity = ret.city;
            //   self.cityIdx = 0;
            //   self.getRcmd({rcmdOnly:true});
            // } else {
            //   self.cityIdx = self.userCityCnt;
            //   self.userCityCnt += 1;
            // }
            // if (ret.cnt != null) {
            //   self.userCityCnt = ret.cnt;//force overwrite
            // }
          } else {
            window.bus.$emit('flash-message', ret.e);
          }
        },
        function (ret) {
          console.error( "server-error" );
          // RMSrv.dialogAlert( "server-error" );
        }
      );
    })
  },
  data (){
    return {
      nativeSearchClicked:false,
      isNativeSearch:false,
      pgNum:0,
      acloading:false,
      hasWechat:true,
      dispVar: {
        userFollowedRltr: {}, //this var used in children component swipe
        userCity:    {o:'Toronto'},
        isLoggedIn:  false,
        isRealtor:   false,
        langCover:   false,
        langCoverOldVer: false,
        // newsAdmin:   false,
        lang:        'en',
        popup:       {},
        isApp:       false,
        isCip:       false,
        // showForum:   false,
        // hasNewMsg:   false,
        hasNewVer:   false,
        // showIndexReno:false,
        isAdmin:     false,
        // showSoldPriceBtn: false,
        // hasFollowedVipRealtor: false,
        // allowedShareSignProp: false,
        newFormInput:false,
      },
      stat:{hasSld:false},
      rcmdCity:    {o:'Toronto'},
      userCityCnt: 0,
      cnt:0,
      hasMorePropInList: 0,
      feedurl: '/1.5/form/forminput',
      signupTitle: this._('Find a Realtor'),
      userForm:{
        ueml:'<EMAIL>',
        nm:'',
        eml:'',
        formid:'system',
        url: document.URL,
        tp:'home2',
        mbl:'',
      },
      datas:[
        // 'showIndexReno',
        // 'allowedShareSignProp',
        'sessionUser',
        'userFollowedRltr',
        'hasFollowedVipRealtor',
        'userCity',
        'isLoggedIn',
        'isRealtor',
        'isVipPlus',
        'hasAid',
        'langCover',
        // 'newsAdmin',
        'lang',
        'isApp',
        'isCip',
        // 'showForum',
        // 'hasNewMsg',
        'hasNewVer',
        // 'isAllowedPredict',
        // 'userFavGroups',
        'hasFollowedRealtor',
        // 'updatePN',
        'popup',
        // 'autocomplete',
        // 'placeApi',
        'coreVer',
        // 'appVer',
        'isAdmin',
        // 'showSoldPriceBtn',
        'projLastQuery',
        // 'showSoldPrice',
        'streetViewMeta',
        // 'useWebMap',
        'newFormInput',
        'languageObj',
      ],
      // reqUrl:  '/1.5/index',
      reqUrl:'',
      focused: false,
      jumping: false,
      loading:false,
      actPos: 10,
      // headlines:[],
      indexAd:[],
      headline:{},
      reco:{},
      // recos:[],
      // mreco:[],
      // combinedRecos:[],
      // banners:[],
      evtBanner:[],
      mode:'',
      cityIdx:0,
      // rcmdHeight:170,
      // calculatedSpWidth:144,
      // currentDrawer1:{d:false},
      // currentDrawer2:{d:false},
      dridx1:0,
      dridx2:0,
      headlineItvl:null,
      oldVerBrowser:false,
      indexMode:'index', //toogle between index/realtor/more
      // guideURL: "http://realmaster.cn/mp/5e91b2bb1984ae8b3d615dc6948a1203a244e48633bbda6819155f049305ce32cd1c6cdc880858aa82d3b04aa24a936a434b9e942d8cae9c07ac1ee5c4d8228d807bf3?dl=1&",
      drawers1:[{},{},{},{},{}],
      drawers2:[{},{},{},{},{}],
      swipeHeight: (window.innerWidth || document.documentElement.clientWidth)*180/600
    }
  },
  components: {
    PageSpinner,
    BottomNav,
    LangSelectCover,
    CitySelectModal,
    // Swipe,
    // SwipeItem,
    FlashMessage,
    LoadingBar,
    LazyImage,
    // IndexMoreOption,
    PropList,
    // IndexRealtorMiddlePage,
    // VipUpgradeTip,
    FeaturePopup,
    AutocompleteListWrapper,
    SignUpForm,
    // IndexMoreCategory,
  },
  beforeDestroy:function(){
    clearInterval(this.headlineItvl);
  },
  methods: {
    // setUpPreview (evt) {
    //   var h, w, self=this, el=evt.target;
    //   if (el) {
    //     if (/^data:image/.test(el.src)) {
    //       return;
    //     }
    //     w = el.naturalWidth;
    //     h = el.naturalHeight;
    //     el.setAttribute('data-size', w + 'x' + h);
    //     self.sizes.push(w + 'x' + h);
    //     // console.log(el);
    //   }
    //   // console.log(self.sizes);
    // },
    // showSignupOnclick(id){
    //   var elem = document.querySelector(id)
    //   if(elem && !this.dispVar.isRealtor){
    //     elem.onclick = ()=>{toggleModal('SignupModal')}
    //   }
    // },
    hideElemById(id){
      var banner = document.querySelector(id)
      if(banner){
        banner.remove()
      }
    },
    hideDrawAndMove(){
      var self = this;
      var agent = '#drawers > div.swiper-wrapper  a.drawer.link-agent';
      var elem = document.querySelector(agent)
      if(elem){
        self.hideElemBySelectors(agent)
        self.hideElemBySelectors('#drawers > div.swiper-wrapper  a.drawer.link-yellowpage')
        // push forward and remove
        var stig = '#drawers > div.swiper-wrapper  a.drawer.link-stigmatized'
        var trend = '#drawers > div.swiper-wrapper  a.drawer.link-trend'
        var stigElem = document.querySelector(stig)
        var trendElem = document.querySelector(trend)
        var parent = document.querySelector('#drawers > div.swiper-wrapper > div.drawers.swiper-slide > div:nth-child(2)')
        parent.append(stigElem)
        parent.append(trendElem)
        self.appendEmptyPlaceHolder()
        // setTimeout(()=>{
        //   self.hideElemBySelectors(stig)
        //   self.hideElemBySelectors(trend)
        // },10)
      } else {
        let stepParent = document.querySelector('#drawers > div.swiper-wrapper  .wrapper.newDrawers3')
        if(stepParent && stepParent.childElementCount < 5){
          self.appendEmptyPlaceHolder()
        }
      }
    },
    appendEmptyPlaceHolder(){
      let self = this;
      let stepParent = document.querySelector('#drawers > div.swiper-wrapper  .wrapper.newDrawers3')
      if(stepParent){
        for (let index = 0; index <= 1; index++) {           
          let div = document.createElement("a")
          div.classList.add('drawer')
          stepParent.append(div)
        }          
      }
    },
    hideElemBySelectors(sel){
      var avt = document.querySelectorAll(sel)
      if(avt && avt.length){
        for (let a of avt){
          a.remove()
        }
      }
    },
    logUserHasWechat(has){
      var self = this;
      if (!self.dispVar.isLoggedIn) {
        return;
      }
      if(self.loggedWechat){return}
      self.loggedWechat = true
      self.$http.post('/loguserwechat', {hasWechat:has}).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            console.log(ret);
          }
        },
        function (ret) {
          // console.error( "server-error" );
        }
      );
    },
    logUserId(id){
      var self = this;
      if (!self.dispVar.isLoggedIn) {
        return;
      }
      self.$http.post('/matchuser', {id}).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            console.log(ret);
          }
        },
        function (ret) {
          // console.error( "server-error" );
        }
      );
    },
    onClickSearchBar() {
      if (!this.nativeSearchClicked) {
        //prevent user open multiple autocomplete
        var city = this.dispVar.userCity.o;
        var prov = this.dispVar.userCity.p;
        var lang = this.dispVar.lang || 'en';
        this.nativeSearchClicked = true;
        this.goTo({url:'/1.5/autocomplete?referer=index&city='+city+'&prov='+prov+'&lang='+lang});
        setTimeout(()=>{
          //set nativeSearchClicked back to false, so user can open autocomplete again
          // or set it using goback from autocomplete
          this.nativeSearchClicked = false;
        },1000);
      }
    },
    extractDomain(url){
      var hostname;
      //find & remove protocol (http, ftp, etc.) and get hostname
      if (url.indexOf("//") > -1) {
          hostname = url.split('/')[2];
      }
      else {
          hostname = url.split('/')[0];
      }
      //find & remove port number
      hostname = hostname.split(':')[0];
      //find & remove "?"
      hostname = hostname.split('?')[0];
      return hostname;
    },
    picUrl(r){
      var ret = this.setupThisPicUrls(r);
      return ret[0] || (window.location.origin + "/img/noPic.png");
      // var self = this;
      // if (/^RM1/.test(r.id)) {
      //   if (!r.pic.ml_num) {
      //     r.pic.ml_num = r.sid;
      //   }
      //   return  self.convert_rm_imgs(self, r.pic, 'reset')[0] || '/img/noPic.png';
      // }
      // return listingPicUrls(r, this.dispVar.isCip)[0] || '/img/noPic.png';
    },
    initPropListImg (data) {
      var recursive = this.propItems;
      if (data.target) {
        recursive = this[data.target];
      }
      for (var prop of recursive) {
        if (!prop.img) {
          prop.img = this.picUrl(prop);
        }
      }
    },
    postPN(){
      if (!localStorage.pn) {
        return;
      }
      var self = this;
      self.updatedPN = true;
      self.$http.post('/1.5/user/updPn', {pn:localStorage.pn}).then(
        function (ret) {
          ret = ret.data;
          if (ret.r) {
          } else {
            console.error(ret.e);
            self.updatedPN = false;
          }
        },
        function (ret) {
          console.error( "server-error" );
        }
      );
    },
    unsubscribe(){
      trackEventOnGoogle('homeSubscribedCities','unsubscribe');
      var url = '/1.5/index/subscribe', self = this;
      self.$http.post(url, {mode:'unsubscribe', idx:this.cityIdx}).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            if (ret.cnt != null) {
              self.userCityCnt = ret.cnt;
            } else {
              self.userCityCnt -= 1;
            }
            if (ret.msg) {
              window.bus.$emit('flash-message', ret.msg);
            }
            self.cityIdx = 0;
          } else {
            window.bus.$emit('flash-message', ret.e);
          }
        },
        function (ret) {
          console.error( "server-error" );
          // RMSrv.dialogAlert( "server-error" );
        }
      );
    },
    initIndexData(){
      this.inited = true;
      // this.getRcmd();
      this.hist = (typeof indexHistCtrl !== "undefined" && indexHistCtrl !== null ? indexHistCtrl.getHist() : void 0) || [];
    },
    updateTranslate () {
      var self = this;
      setTimeout(function () {
        self.$getTranslate(self);
      }, 0);
    },
    // viewProfile (id) {
    //   var url = "/1.5/wesite/" + id + "?inFrame=1"
    //   this.tbrowser(url);
    // },
    appendCmtyToUrl(url, prop={}, opt={}){
      if (!prop.cmty_en) {
        return;
      }
      var fChar = url.indexOf('?')>0?'&':'?';
      url += fChar+'cmty='+prop.cmty_en;
      url += '&cmtyName='+prop.cmty;
      return url;
    },
    // parseEventBanner(l){
    //   var ret = [];
    //   for (let i of l) {
    //     if ((i.cOnly && this.isVisitorNoFollow) || !this.dispVar.hasFollowedRealtor) {
    //       ret.push(i)
    //     }
    //   }
    //   return ret;
    // },
    // parseBannerImageUrl(url){
    //   return url.replace('[timestamp]',(''+Date.now())).replace('[ts]',(''+Date.now()));
    // },
    // parseBannerImage(banner){
    //   if (!banner) {
    //     return;
    //   }
    //   for (var b of banner) {
    //     if (b.impImgUrl) {
    //       b.impImgUrlOrig = b.impImgUrl;
    //       b.impImgUrl = this.parseBannerImageUrl(b.impImgUrl);
    //     }
    //   }
    //   return banner;
    // },
    lostFocus () {
      if (!this.jumping) {
        return this.dispVar.focused = false;
      }
    },
    directSearch(q){
      if (q) {
        this.searchStr = q;
      }
      var url = "/1.5/search/prop?d=/1.5/index&id=" + encodeURIComponent(this.searchStr);
      return this.goTo({url:url});
    },
    // TODO:retire
    getCityList (mode) {
      let category = 'homeTopBar';
      if (mode == 'subscribe') {
        category = 'homeSubscribedCities';
        window.bus.$emit('select-city', {noloading:true});
        if (!this.dispVar.isLoggedIn) {
          window.location = '/1.5/user/login';
        }
      }
      this.mode = mode;
      window.bus.$emit('select-city', {noloading:true});
      trackEventOnGoogle(category,'selectCity');
    },
  }
})
