/**
 * Vue-Resource 适配器 (基于拦截器)
 *
 * 通过 vue-resource 的拦截器机制，实现对请求的代理。
 * - 如果 `window.RMSrv.fetch` 可用，则通过它发送请求。
 * - 否则，请求将由原始的 vue-resource 处理。
 *
 * 这种方法比替换整个 $http 对象更健壮、更简单，并能与其他拦截器完美共存。
 */

const adapter = {
  install(Vue) {
    // 检查 Vue 和 vue-resource 是否已加载
    if (!Vue || !Vue.http) {
      console.error(
        'Vue-Resource Adapter: Vue.http is not available. ' +
        'Please ensure Vue and Vue-resource are loaded before this adapter.'
      );
      return;
    }

    // 向 vue-resource 添加我们的拦截器
    // see: https://github.com/pagekit/vue-resource/blob/develop/docs/http.md#interceptors
    Vue.http.interceptors.push(async(request, next) => {
      // 检查 RMSrv.fetch 是否可用, not coffeescript, no ?
      if (typeof window !== 'undefined' && !window.RMSrv && !window.RMSrv.fetch) {
        // RMSrv.fetch 不可用，让请求继续
        console.log('Vue-Resource Adapter: RMSrv.fetch not found, passing through.', request.url);
        if(next){
          next();
        }
      }
        
      // RMSrv.fetch 可用，通过它代理请求
      // console.log('Vue-Resource Adapter: Proxying request via RMSrv.fetch', request.url);
      const fetchOptions = {
        method: request.method,
        headers: request.headers || {},
        body: request.body,
      };

      // 调用 RMSrv.fetch
      var result;
      try {
        // 不能cb和await连用会导致bug， cb no return 会继续执行
        result = await window.RMSrv.fetch(request.url, fetchOptions)//, (error, result) => {
      } catch (error){
        // 发生错误，构建一个 vue-resource 风格的错误响应
        const errorResponse = {
          data: error.response?.data || null,
          body: error.response?.data || null,
          status: error.response?.status || error.status || 0,
          statusText: error.message || 'RMSrv.fetch Error',
          headers: error.response?.headers || {},
        };
        // 使用 respondWith 中断请求链并返回错误
        return request.respondWith(errorResponse.body, {
          status: errorResponse.status,
          statusText: errorResponse.statusText,
          headers: errorResponse.headers,
        });
      }
     
      // 请求成功，构建一个 vue-resource 风格的成功响应
      // console.log('Vue-Resource Adapter: success',result);
      const successResponse = {
        body: result,
        status: 200,
        statusText: 'OK',
        headers: {}, // RMSrv.fetch 不返回头信息，这里提供一个默认值
      };
      // 使用 respondWith 中断请求链并返回成功响应
      return request.respondWith(successResponse.body, {
        status: successResponse.status,
        statusText: successResponse.statusText,
        headers: successResponse.headers,
      });

    });
    // console.log('Vue-Resource Adapter (Interceptor) installed.');
  }
};

export default adapter;

// CommonJS 兼容
if (typeof module !== 'undefined' && module.exports) {
  module.exports = adapter;
  module.exports.default = adapter;
}