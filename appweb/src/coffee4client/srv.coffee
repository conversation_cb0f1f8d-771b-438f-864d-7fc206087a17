RMSrv =
  getCookie: (cname)->
    name = cname + '='
    ca = document.cookie.split(';')
    for c in ca
      while (c.charAt(0) is ' ')
        c = c.substring(1)
      if c.indexOf(name) is 0
        return c.substring(name.length,c.length)
    ''
  init: ->
    # this.cb = null
    unless window.rmCall
      window.rmCall = (m)->
        unless 'string' is typeof m
          m = JSON.stringify(m)
        window.parent.postMessage(m, '*')
    self = @
    removeModal = ()->
      if $elems = document.querySelectorAll('[data-modal="simPopUpModal"]')
        $elems.forEach ($elem)->
          # NOTE: see htmlToElement
          $elem.parentNode.parentNode.removeChild($elem.parentNode)
    notQuerySelector = (msg)->
      return /^\{/.test(msg) or (not /^\#/.test(msg))
    window.addEventListener('message', (msg)->
      # parent got msg from child
      if msg and msg.data
        msg = msg.data
      if /^:ctx/.test msg
        msg = msg.substr(5)
      # NOTE: ignore vue devtool msg
      if msg.vueDetected
        return
      if /^:cancel/.test msg
        removeModal()
        RMSrv.cb = null
        return
      # parent got msg from child
      if notQuerySelector(msg)
        # try
        #   obj = JSON.parse(msg)
        # catch error
        #   console.error msg
        # console.log self.cb,RMSrv.cb
        if cb = RMSrv.cb
          cb msg
          RMSrv.cb = null
        # else
        #   console.log 'no cb here'
        return removeModal()
      # child got msg from parent
      if ('string' is typeof msg) and (elem = document.querySelector(msg))
        return rmCall elem.innerHTML()
    )
  needApp: -> alert 'Need RealMaster APP'
  scanQR: -> RMSrv.needApp()
  isIOS: -> /iPhone|iPad|iPod/i.test(navigator.userAgent)
  isAndroid: -> /Android/i.test(navigator.userAgent)
  isWeChat: -> /MicroMessenger/i.test(navigator.userAgent)
  isBlackBerry: -> /BlackBerry/i.test(navigator.userAgent)
  showInBrowser: (url)->
    window.open(url, '_blank')
  appendDomain:(url)->
    location = window.location.href
    arr = location.split('/')
    domain = arr[0] + '//' + arr[2]
    domain + url
  htmlToElement:(html)->
    # TODO: htmlToElement will create an empty div parent
    template = document.createElement('div')
    html = html.trim()  #Never return a text node of whitespace as the result
    template.innerHTML = html
    # console.log template
    return template
  getPageContent: (url, selector, opt, cb) -> # TODO
    unless /^(http|https)/.test url
      url = @.appendDomain(url)
    if not cb?
      cb = opt
      opt = {wait:0,hide:true}
    # alert selector+':'+url
    # setTimeout (()->
    cfg = Object.assign(opt,{sel:selector,tp:'pageContent',url})
    # alert(JSON.stringify(cfg))
    # RMSrv.action cfg,(ret)->
    #   cb ret
    # NOTE: create iframe and return content
    width = opt.width or "#{window.innerWidth}px"
    height = opt.height or "#{window.innerHeight - 44}px"
    modalId = opt.modalId or 'simPopUpModal'
    if opt.mountId then mountSelector = "##{opt.mountId}" else mountSelector ='body'
    iframeEl = document.querySelector("##{modalId} #the_iframe")
    sendMsg = (m)->
      unless 'string' is typeof m
        m = JSON.stringify(m)
      # Make sure you are sending a string, and to stringify JSON
      iframeEl.contentWindow.postMessage(m, '*')
    if iframeEl
      iframeEl.src = url
      toggleModal("#{modalId}",'open')
      sendMsg(selector)
      return
    
    closeString="""
      <a class="icon icon-close pull-right" href="javascript:;", \
        onclick='toggleModal("#{modalId}","close")'> </a>
    """
    if opt.arrow
      closeString = """
        <span class="icon fa fa-arrow-#{opt.arrow} pull-#{opt.arrow}"\
          onclick='toggleModal("#{modalId}","close")'></span>
      """
    modalHeaderString = """
    <header class="bar bar-nav #{if opt.white then 'common'}">
      #{closeString}
      <h1 class="title">#{cfg.title or 'RealMaster'}</h1>
    </header>"""
    if opt.hide
      modalHeaderString=''

    elem = @.htmlToElement("""
    <div id="#{modalId}" class="modal active" data-modal="simPopUpModal" style="z-index:9999">
      #{modalHeaderString}
      #{closeString}
      <div class="content">
        <iframe src="#{url}" id="the_iframe" style="width:#{width}; height:#{height}">
      </div>
    </div>
    """)
    # iframe = document.createElement('iframe');
    # iframe.setAttribute('src', url);
    # iframe.setAttribute('id', 'the_iframe');
    # iframe.style.width = height + 'px';
    # iframe.style.height = width + 'px';
    # TODO: append iframe and bar to close it;
    # iframe.style.postion =
    document.querySelector(mountSelector).appendChild(elem)
    # iframeEl = document.getElementById('the_iframe')
    self = @
    RMSrv.cb = cb if cb
    # document.body.appendChild(elem)
    # iframeEl = document.getElementById('the_iframe')
    # self = this
    # # NOTE: cb from pagecontent, invoke when next page exits
    # RMSrv.cb = cb if cb
    # TODO: send msg when ready
    if opt.wait
      setTimeout ()->
        sendMsg(selector)
      ,opt.wait
  openInAppBrowser: (url)->
    window.open(encodeURI(url), '_blank')
  openTBrowser: (url)->
    window.open(url, '_blank')
  dialogAlert: (msg)-> alert msg.toString()
  dialogConfirm: (msg,cb,tit,btns)->
    # console.log '============dialogConfirm'
    # console.log msg.toString()
    # console.log cb
    # console.log "" + btns
    # confirm msg.toString(),cb,btns
    string="""
    <div id="confirm-message-box" style="top:0;left:0;right:0;bottom:0;position:absolute;">
      <div class="backdrop"></div>
      <div style="border-radius:10px;padding-top:15px;position:absolute;width:66%;top:45%;left:50%;transform:translate(-50%,-50%);z-index:20;font-size:16px;background:white;text-align:center;">
        <div style="padding-left:20px;padding-right:20px;">#{msg.toString()}</div>
        <div style="display:flex;justify-content:space-between;border-top:1px solid #f1f1f1;padding-top:10px;">
          <button id="rmbtn1"  style="border:none;color:#157AFB;font-size:14px;background:transparent;padding-top:13px;padding-right:11px;padding-bottom:13px;padding-left:11px;width:49.5%;text-align:center;border-radius:13px;">
            #{btns[0]}
          </button>
          <div style="display:inline-block;border-left-width:1px;border-left-style:solid;border-left-color:rgb(241, 241, 241);"></div>
          <button id="rmbtn2"  style="font-weight:bold;border:none;color:#157AFB;font-size:14px;background:transparent;padding-top:13px;padding-right:11px;padding-bottom:13px;padding-left:11px;width:49.5%;text-align:center;border-radius:13px;">
              #{btns[1]}
            </button>
        </div>
      </div>
    </div>
    """
    elem = @htmlToElement(string)
    document.body.appendChild(elem)
    removeHtml = ()->
      # document.body.removeChild(document.querySelector('#confirm-message-box'))
      elem = document.querySelector('#confirm-message-box')
      elem.parentNode.removeChild(elem)
    callBack = ()->
      removeHtml()
      # console.log cb
      cb(2)
    document.querySelector('#rmbtn1').addEventListener 'click', removeHtml
    document.querySelector('#rmbtn2').addEventListener 'click', callBack

    # elem = document.createElement 'div'
    # elem.id ='confirm-message-box'
    # elem.style ='top:0;left:0;right:0;bottom:0;position:absolute;'
    # ebackdrop = document.createElement 'div'
    # ebackdrop.style='position: fixed;top: 0;right: 0;bottom: 0;left: 0;z-index: 15;background-color: rgba(0,0,0,.8)'
    # ebtmbox =  document.createElement 'div'
    # ebtmbox.style='border-radius:10px;padding-top:15px;position:absolute;width:66%;top:45%;left:50%;transform:translate(-50%,-50%);z-index:20;font-size:16px;background:white;text-align:center;'

    # epadding=document.createElement 'div'
    # epadding.innerHTML="#{msg.toString()}"
    # epadding.style='padding-left:20px;padding-right:20px;'

    # ebox = document.createElement 'div'
    # ebox.style = 'display:flex;justify-content:space-between;border-top-width:1px;border-top-style:solid;border-top-color:rgb(241, 241, 241);padding-top:10px;'
    # ebtn = document.createElement 'button'
    # ebtn.style = 'border:none;color:#157AFB;font-size:14px;background:transparent;padding-top:13px;padding-right:11px;padding-bottom:13px;padding-left:11px;width:49.5%;text-align:center;border-radius:13px;'
    # ebtn.innerHTML = "#{btns[0]}"
    # ebtn.id = 'btn1'
    # ebtn1 = document.createElement 'button'
    # ebtn1.style = 'font-weight:bold;border:none;color:#157AFB;font-size:14px;background:transparent;padding-top:13px;padding-right:11px;padding-bottom:13px;padding-left:11px;width:49.5%;text-align:center;border-radius:13px;'
    # ebtn1.innerHTML = "#{btns[1]}"
    # ebtn1.id = 'btn2'
    # ebtnline = document.createElement 'div'
    # ebtnline.style = 'display:inline-block;border-left-width:1px;border-left-style:solid;border-left-color:rgb(241, 241, 241);'

    # ebox.appendChild(ebtn)
    # ebox.appendChild(ebtnline)
    # ebox.appendChild(ebtn1)
    # ebtmbox.appendChild(epadding)
    # ebtmbox.appendChild(ebox)
    # elem.appendChild(ebackdrop)
    # elem.appendChild(ebtmbox)


    # callBack = ()->
    #   removeHtml()
    #   cb(1)
    # removeHtml = ()->
    #   document.body.removeChild(document.getElementById('confirm-message-box'))
    # document.body.appendChild(elem)
    # cancelBtn = document.getElementById('btn1')
    # confrimBtn = document.getElementById('btn2')

    # cancelBtn.addEventListener 'click', removeHtml
    # confrimBtn.addEventListener 'click', callBack
  fDoc: ->
    iframe = document.getElementById 'iframe'
    try
      doc = iframe.contentDocument or iframe.contentWindow
      doc ?= document
      doc = doc.document if doc.document
    catch e
      console.log e
    # console.log doc
    doc
  getMeta: (doc)->
    meta = doc.querySelectorAll 'meta'
    ret = title:doc.title
    for m in meta
      ret[m.getAttribute('name')] = m.getAttribute('content')
    ret
  getShareImage: (doc)->
    if div = (doc.getElementById('content_div') or doc.body)
      for img in div.getElementsByTagName 'img'
        if img?.src
          return img.src
    'https://realmaster.com/img/logo.png'
  logoImg: "data:image/png;base64,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"
  resizeImage: (doc,url,max,cb)->
    canvas = doc.createElement 'canvas'
    img = doc.createElement 'img'
    img.onload = ->
      try
        wc = w = this.width
        hc = h = this.height
        if (w > h)
          if (w > max)
            wc = max
            hc = Math.round(h / w * max)
        else
          if (h > max)
            hc = max
            wc = Math.round(w / h * max)
        canvas.width = wc
        canvas.height = hc
        ctx = canvas.getContext('2d')
        ctx.drawImage(this, 0, 0, wc, hc)
        data = canvas.toDataURL("image/png")
        cb data
      catch e
        console?.log e
        cb RMSrv.logoImg
    img.onerror = (e)->
      console?.log e
      cb RMSrv.logoImg
      #RMSrv.dialogAlert "Load Error:" + e
    img.setAttribute('crossOrigin', 'anonymous')
    img.crossOrigin = "Anonymous"
    if m = /^(.*\?v=)\d+$/g.exec url
      img.src = m[1] + Date.now()
    else
      img.src = url
    null
  wechatShare: (doc,share)->
    # get image thumbnail
    RMSrv.resizeImage doc,share.image,100,(imgData)->
      opts =
        title:  share.title or "RealMaster Sharing"
        description: share.description or "RealMaster App Sharing"
        thumbData: imgData.split(',')[1],
        url: share.url.replace('realmaster.com','realmaster.cn')
      # console.log opts
      RMSrv.needApp()
  qrcodeShare: (cmd,url,id)->
    id ?= 'id_share_qrcode'
    if cmd is 'show'
      if dialog = document.getElementById(id)
        dialog.style.display = 'block'
        genQrCode = ->
          # generate qrcode
          holder = document.getElementById id+'_holder'
          holder.innerHTML = ''
          new QRCode(holder, url)
        if QRCode?
          genQrCode()
        else
          # load js if not yet
          po = document.createElement 'script'
          po.type = 'text/javascript'
          po.src = '/js/qrcode/qrcode.min.js'
          document.getElementsByTagName('head')[0].appendChild po
          po.onload = genQrCode
    else
      if dialog = document.getElementById(id)
        dialog.style.display = 'none'
  showSMB: (cmd,prefix = 'share-')-> # backdrop must be after the main detail content div, with class 'backdrop', style is 'display:none'
    if not RMSrv._shareMask
      RMSrv._shareMask = document.getElementById("backdrop")
      RMSrv._shareMask.addEventListener 'click', -> RMSrv.showSMB 'hide'
    if cmd is 'show'
      RMSrv._sharePrefix = prefix
      if newParent = document.getElementById prefix + 'placeholder'
        newParent.appendChild document.getElementById 'shareDialog'
      document.body.classList.add 'smb-open'
      RMSrv._shareMask.style.display = 'block'
      RMSrv.shareLang()
    else if cmd is 'hide'
      document.body.classList.remove 'smb-open'
      RMSrv._shareMask.style.display = 'none'
  _shareLang: null
  shareLang: (lang,doc = window.document)-> # en or not
    if (lang is 'en') or ((not lang) and document.getElementById('id_share_lang_en')?.classList.contains('active'))
      RMSrv._shareMap = {"title-en":'title',"desc-en":'description',url:'url',image:'image'}
      document.getElementById('id_share_lang_en')?.classList.add('active')
      document.getElementById('id_share_lang_cur')?.classList.remove('active')
    else
      RMSrv._shareMap = {title:'title',desc:'description',url:'url',image:'image'}
      document.getElementById('id_share_lang_cur')?.classList.add('active')
      document.getElementById('id_share_lang_en')?.classList.remove('active')
    _shareInfo = RMSrv._getShareInfo doc,RMSrv.getMeta(doc)
    document.getElementById('id_share_title')?.value = _shareInfo.title
    document.getElementById('id_share_desc')?.value = _shareInfo.description
    if lang? and lang isnt 'cur' # only need to specify
      RMSrv._shareLang = lang
    else
      RMSrv._shareLang = null # TODO
  _getShareInfo: (doc,share)->
    _getInfo = (key,name)->
      try
        if tmp = (doc.getElementById(RMSrv._sharePrefix + key) or doc.getElementById('share-' + key) or doc.getElementById('alt-' + key))
          share[name or key] = tmp.value or tmp.textContent
      catch e
        console?.log e
    for k,n of RMSrv._shareMap # {title:'title',desc:'description',url:'url',image:'image'}
      _getInfo k,n
    if not share.image? then share.image = RMSrv.getShareImage doc
    if not share.url? then share.url = doc.URL or window.location.href
    share
  share: (type,doc = window.document)->
    _getShareInfo = ->
      _shareInfo = RMSrv._getShareInfo doc,RMSrv.getMeta(doc)
      if title = doc.getElementById('id_share_title')?.value
        _shareInfo.title = title
      if desc = doc.getElementById('id_share_desc')?.value
        _shareInfo.description = desc
      # update url for language
      if RMSrv._shareLang
        # console.log _shareInfo.url
        if m = /\?.*(lang\=[a-zA-Z\-]+)/.exec _shareInfo.url
          _shareInfo.url = _shareInfo.url.replace m[0],(m[0].replace m[1],"lang=#{RMSrv._shareLang}")
        else if /\?[a-z0-9]\=/i.test _shareInfo.url
          _shareInfo.url += '&lang=' + RMSrv._shareLang
        else
          _shareInfo.url += '?lang=' + RMSrv._shareLang
        _shareInfo.url
      _shareInfo
    # get meta; get title/desc/url/image
    switch type
      when 'show','hide'
        RMSrv.showSMB type
      when 'lang-en'
        RMSrv.shareLang 'en',doc
      when 'lang-cur'
        RMSrv.shareLang 'cur',doc
      when 'lang-kr'
        RMSrv.shareLang 'kr',doc
      when 'qr-code'
        sInfo = _getShareInfo()
        RMSrv.qrcodeShare 'show',sInfo.url
      when 'qr-code-close'
        RMSrv.qrcodeShare 'hide'
      when 'wechat-friend'
        sInfo = _getShareInfo()
        RMSrv.wechatShare doc,sInfo,0
      when 'wechat-moment'
        sInfo = _getShareInfo()
        RMSrv.wechatShare doc,sInfo,1
      when 'facebook-feed'
        sInfo = _getShareInfo()
        RMSrv.facebookShare doc,sInfo,'feed'
      else
        #window.plugins.socialsharing.share (share.title or share.description or 'Shared with RealMaster App'),(share.title or share.description or 'RealMaster App Sharing'),share.image,share.url

  clearCache: ->
  setFileChooser: ->
  onReady: (cb)->
    cb()
    true
  getKeyboard: (cb)->
  fetch: (url, options = {}, callback) ->
    # 参数类型检查：第一个参数必须是URL字符串
    if typeof url isnt "string"
      throw new TypeError("fetch方法的第一个参数必须是URL字符串，实际类型: #{typeof url}")

    # 简化的callback参数处理：第二个参数可以是callback函数
    if typeof options is "function"
      callback = options
      options = {}

    # 设置默认值
    options.method ?= "POST"
    options.credentials ?= "same-origin"
    options.headers ?= {}
    options.headers["Accept"] = "application/json"

    # 处理请求体和Content-Type
    if options.method.toUpperCase() is "GET"
      delete options.body
    else
      # POST请求默认空对象
      if options.method.toUpperCase() is "POST"
        options.body ?= {}

      # 简化处理：对象自动JSON序列化（覆盖99%的实际使用场景）
      # 如果未来需要支持FormData等二进制数据，开发者应明确传入非对象类型
      if options.body and typeof options.body is "object"
        options.headers["Content-Type"] = "application/json"
        options.body = JSON.stringify(options.body)

    # Native fetch 支持
    # 当useNativeFetch为true时，通过RMSrv.action调用Native端的fetch实现
    # 注意：RMSrv.action使用单参数callback，与统一的双参数模式不同
    # 这里保持原有行为，调用方需要适配单参数callback，保持native端单参数不做修改
    # Native端返回格式：成功时返回数据，失败时返回以"Error:"开头的字符串
    if options.useNativeFetch
      delete options.useNativeFetch
      return RMSrv.action {tp:"fetch", opt:options, url:url}, callback

    # 执行原生fetch请求
    # 使用浏览器原生fetch API，支持完整的fetch选项配置
    executeRequest = ->
      try
        # 调用浏览器原生fetch API
        response = await window.fetch(url, options)

        # 检查HTTP状态码，非2xx状态码视为错误
        unless response.ok
          error = new Error("HTTP #{response.status}: #{response.statusText}")
          error.response = response  # 保留原始响应对象供调用方使用
          throw error

        # 自动解析JSON响应体
        result = await response.json()

        # 成功时的callback处理（统一的双参数模式：error, result）
        if callback
          callback(null, result)
          return
        return result  # 返回解析后的JSON数据供Promise使用
      catch error
        # 错误时的callback处理（统一的双参数模式：error, result）
        if callback
          callback(error, null)
          return
        else
          # 没有callback时，抛出错误供Promise.catch捕获
          throw error

    # 执行请求并返回Promise，支持await调用
    return executeRequest()
RMSrv.init()
