app = angular.module('listingApp',['datePicker']) #'ngTouch''ng',
listingCtrl = ($scope, $http, $timeout, $interval, $filter)->
  locfld = ['addr', 'st_num', 'st', 'city', 'cmty', 'prov', 'cnty',  'lat', 'lng']
  previewImgType = 'picUrls'
  $scope.disabledInsert = false
  $scope.type = vars.type
  $scope.isAssignAdmin = vars.isAssignAdmin
  # $scope.title = vars.tml
  # if vars.type isnt 'mylisting'
  #   $scope.title = vars.tlm

  $scope.hideBackdrop = true
  $scope.isArray = angular.isArray
  $scope.formData = {} #input form data
  $scope.userData = {} #user fn ln
  $scope.userForm = {src:'myListingNonRealtor'} #signup form
  for i in ['nm','eml','mbl']
    if vars['userInfo'+i]
      if i is 'mbl'
        $scope.userForm[i] = parseInt(vars['userInfo'+i])
      else
        $scope.userForm[i] = vars['userInfo'+i]
  $scope.user = {vip:1}

  $scope.minDate = new Date()#moment.tz('EST').add(0, 'd').hour(12).startOf('h') #use moment

  $scope.ml_num = vars.ml_num
  $scope.roomNumbers = [1..12]
  $scope.hasMore = false
  $scope.wSign = true
  $scope.wDl = true
  $scope.tmpData = {} #use to store map search result
  $scope.curBrkg = {} #display brkg info
  $scope.selectedProp = [] #select array of id for deletion
  $scope.toShare = 1
  $scope.contactList = []
  $scope.contactInfo = {nm:'',mbl:'',id:null}
  $scope.isAddContact = false
  $scope.isShowDelete = false
  $scope.allListing = []
  $scope.showSearchBar = false
  $scope.searchId = ''
  $scope.verified = vars.verified
  $scope.resetSearch = ->
    $scope.showSearchBar = false
    $scope.searchId = ''
    $scope.mylistings = $scope.allListing
    return $timeout -> $scope.$apply()
  $scope.$watch 'searchId', (newValue, oldValue) ->
    if oldValue isnt '' and  newValue is ''
      return $scope.resetSearch()
    if (newValue isnt undefined) and (newValue isnt null) and (newValue isnt oldValue) and (newValue.length > 2)
      if $scope.loading
        return
      clearTimeout(searchTimer)
      searchTimer = setTimeout ->
        $scope.loading = true
        $scope.mylistings = []
        searchById(newValue)
      , 500

  searchById = (id)->
    $http.post('/1.5/promote/search', {search:$scope.searchId, skip:$scope.mylistings.length})
      .success (data)->
        unless data.ok
          return console.log 'Err:del '+data.err
        $scope.loading = false
        $scope.mylistings  = data.list
        $timeout ->
          $scope.$apply()
        return
      .error (data)->
        RMSrv.dialogAlert(data.toString())

  $scope.openOwnerInfo = (id)->
    url = "/1.5/wesite/#{id}?inFrame=1"
    cfg = {hide:false,title:'Owner Info'}
    RMSrv.getPageContent(url, '#callBackString', cfg, (val)->
      if val is ':cancel'
        return
    )

  $scope.openVerifyPopup = (mode,isV)->
    return if isV and ($scope.verified[isV] is true)
    if not mode
      if ($scope.verified.emlV is false)
        mode = 'v'
      else if ($scope.verified.mblV is false)
        mode = 'p'
      else
        return

    to = '/1.5/settings/verify?d=/1.5/settings/editProfile&verify='+mode
    $scope.loading = true
    setTimeout(()->
      $scope.loading = false
      $scope.$apply()
    , 1000)
    RMSrv.getPageContentIframe(to,'#callBackString', {transparent:true}, (val)->
      if (val is ':cancel')
        return
      try
      # 刷新显示，mylisting publish验证手机/邮箱
        ret = JSON.parse(val)
        if ret.delete is 'phone'
          $scope.userData.mbl = ''
          $scope.verified.mbl = ''
          $scope.verified.mblV = false
        else
          for k,v of ret
            if k is 'eml'
              $scope.userData[k] = v
            else if k is 'mbl'
              $scope.userData[k] = Number(v)
            $scope.verified[k] = v
        $timeout ->
          $scope.$apply()
      catch e
        console.error(e)
    )

  if not vars.isRealtor
    $scope.wSign = false

  if vars.action is 'showSMB'
    toggleModal('newListingModal')
    $scope.hideBackdrop = false

  isValidZipCode = (zip)->
    if zip.length isnt 6
      return false
    # if zip.substr(0,3) isnt $scope.zipRet3
    #   return false
    for i in [0..5]
      if i%2 is 0
        unless (zip[i] and zip[i].match(/[a-z]/i))
          return false
      else
        unless (zip[i] and zip[i].match(/[0-9]/i))
          return false
    true
  resetFormAddress = (name)->
    for i in locfld
      $scope[name][i] = ""
    # unless skipAddrRet
    $scope[name].zip = ""
    $scope[name].subCity = "" if $scope[name]?.subCity
    # $scope.tmpData = {}
  getFormattedAddr = (data)->
    return unless data
    ret = ""
    for i in ['addr','city','prov','zip','cnty']
      if data[i]
        ret += data[i] or ""
        if i is 'prov'
          if data.zip
            ret += ' '
          else
            ret += ','
        else if i is 'cnty'
          ret += ' '
        else
          ret += ', '
    return ret
  #format img [] to {} used to store
  getUserData = (to)->
    error = (data)->
      $scope.err = data.err
      flashMessage "server-error"
    $http.post('/1.5/promote/userInfo.json?to=' + to, {}, {timeout: 5000})
    .success (data)->
      parseMbl = (mbl)->
        if ('' + mbl).substr(-2) is '#0'
          return ('' + mbl).slice(0, -1).replace(/\D/g, '')
        ('' + mbl).replace(/\D/g, '')
      if data.ok
        if Array.isArray data.user.eml
          data.user.eml = data.user.eml[0]
        data.user.mbl = parseInt parseMbl data.user.mbl if data.user.mbl
        $scope.userData = data.user
        $scope.original= angular.copy(data.user)
        $scope.dataHasChanged= false
        unless $scope.userData.tl
          tl = ($scope.userData.ln or '') + ($scope.userData.fn or '') + ' ' + \
            ($scope.userData.cpny or '') + ' ' + ($scope.userData.cpny_pstn or '')
          tmp = tl + ' ' + ($scope.userData.itr or '')
          $scope.userData.tl = tmp if tmp.length < 64
          $scope.userData.tl = tl.substr(0, 64)
          $scope.dataHasChanged= true
        # console.log data
      else
        error(data)
    .error (data)->
      error(data)
  getCityList = ()->
    return null if $scope.favCities
    #TODO: use new api
    $http.post('/1.5/props/cities.json', {})
    .success (data)->
      for c in data.fc
        c.k = c.o+'_'+c.p
      $scope.favCities = data.fc
      $scope.extCities = data.ec
    .error (data)->
      $scope.message = data.message
      RMSrv.dialogAlert "Error when getting city list!"
    return
  # data = {id:'rmid', to:'58', val:{}}
  updateAfterPromoted = (data)->
    return unless data.id
    for prop in $scope.mylistings
      if prop.id is data.id
        if data.to is 'market' and not data.val
          data.val = {adok:1}
          if $scope.isAssignAdmin
            data.val.rmProp = ''
        prop[data.to] = data.val #update view in list
        prop.tl58 ?= $scope.curProp.tl58 #prevent overwrite
        prop.addr58 ?= $scope.curProp.addr58
        $scope.curProp = prop if $scope.curProp #overwrite and show promo status in promote pane
        $timeout ->
          $scope.$apply()
        return
    return
  $scope.signUp = ()->
    error = (nm, eml)->
      $scope.nmErr = true if nm
      $scope.emlErr = true if eml
    $scope.emlErr = false
    $scope.nmErr = false
    return error(1,1) unless $scope.userForm
    error(0,1) unless $scope.userForm.eml
    error(1,0) unless $scope.userForm.nm
    return unless $scope.userForm.nm and $scope.userForm.eml
    return if $scope.sending
    return error(0,1) unless isValidEmail($scope.userForm.eml)
    # $scope.userForm.img = $scope.picUrls?[0]
    $scope.sending = true
    $scope.userForm.formid='system'
    $scope.userForm.tp='rmlisting'
    $scope.userForm.url= document.URL
    # /1.5/user/contact
    # /chat/api/feedback
    $http.post('/1.5/form/forminput', $scope.userForm)
    .success (ret) ->
      $scope.sending = false
      msg = document.querySelector('#signUpSuccess')
      if ret.ok
        document.querySelector('#signUpForm').style.display = 'none'
        flashMessage 'sendSuccess'
      else
        msg.textContent = ret.err
      msg.style.display = 'block'
    .error ->
      $scope.sending = false
      RMSrv.dialogAlert 'Server Error'
    return
  $scope.toggleModal = (a, b)->
    toggleModal(a, b)

  $scope.saveAndPromote = (isValid)->
    $scope.submitForm(isValid, true)

  $scope.goBack = ()->
    if vars.d
      window.location = vars.d
    else
      # if /#/.test document.URL
      #   window.location = '/1.5/realtor'
      # else
      window.history.back()

  $scope.showInBrowser = (url)->
    RMSrv.showInBrowser(url)
  $scope.dateConfirm = ()->
    if (not $scope.rangeMode)
      return unless $scope.date
      $scope.formData[$scope.fldTobeUpdated] = new Date($scope.date)
      delete $scope.formData[$scope.fldTobeUpdated+'f']
      delete $scope.formData[$scope.fldTobeUpdated+'t']
    else
      return unless ($scope.dateFrom and $scope.dateTo)
      delete $scope.formData[$scope.fldTobeUpdated]
      $scope.formData[$scope.fldTobeUpdated+'f'] = $scope.dateFrom
      $scope.formData[$scope.fldTobeUpdated+'t'] = $scope.dateTo
    $scope.toggleModal('datePickerModal')
    $scope.hideBackdrop = true
    $scope.propForm.$dirty = true
    null
  #isEnable true for enable icon
  # TODO: hide if mls to market
  $scope.highLightPromoteIcon = (prop, to, isEnable)->
    if to is 'wecard'
      return if isEnable then prop.rcmd else not prop.rcmd
    # NOTE: if promoted can revoke, but cant promote
    if (to is 'market') and prop.ltp in ['mlslisting','mlsrent']
      if prop?.market?.st is 'Promoted'
        return isEnable#if isEnable then false else true
      else
        return false
    if prop[to]?.st in ['To Be Promoted', 'Promo Pending', 'Published', 'Promoted', 'Waiting Approval']
      return isEnable
    else
      return not isEnable

  addrDataHasChanged = ()->
    for i in locfld
      # console.log $scope.formData[i] + ':' + $scope.tmpData[i]
      if $scope.formData[i] and $scope.tmpData[i] and typeof($scope.formData[i]) is 'string' and $scope.formData[i].localeCompare($scope.tmpData[i]) isnt 0
        return true
    if $scope.formData.zip != $scope.tmpData.zip
      return true
    false

  $scope.closeAddrModal = ()->
    # unless $scope.fullAddr
    #   $scope.inputAddrRet = ""
    #   resetFormAddress()
    # close write back
    # if ($scope.fullAddr and addrDataHasChanged()) or ($scope.inputZipRet isnt $scope.tmpData.zip)
    if addrDataHasChanged() or ($scope.inputZipRet isnt $scope.tmpData.zip)
      for i in locfld
        $scope.formData[i] = $scope.tmpData[i]
      if $scope.tmpData.subCity and $scope.tmpData.subCity.length
        $scope.formData.subCity = $scope.tmpData.subCity
      # 如果之前保存的地址里面有subCity，但是地址更换后，现在的地址city就是主city,则需要把之前的subCity字段去掉
      if (not $scope.tmpData.subCity) and $scope.formData.subCity
        delete $scope.formData.subCity
      $scope.formData['zip'] = $scope.inputZipRet #$scope.tmpData['zip']
      $scope.propDataHasChanged = true
      $scope.propForm.$dirty = true
    # if (not vars.isApp)# or (/app\.test\:/.test(document.URL))
    #   $scope.inputAddrRet = $scope.searchAddrRet
    #   toggleModal('inputAddressModal')
    $scope.hideBackdrop = true
    RMSrv.clearCache() if RMSrv


  $scope.showDate = (type,fld)->
    # if $scope.formData[fld] and $scope.formData[fld].toString().indexOf('~') > 0
    if (type is 'tba') or (type is 'immed')
      return type == $scope.formData[fld]
    else if type is 'date' and $scope.formData[fld] and  ($scope.formData[fld] not in ['tba','immed'])
      return true
    else if type is 'range' and $scope.formData[fld+'f']
      return true
    else
      return false
  #used in _generate map and scroll directive
  $scope.height = (elem) -> # cannot move to the only location where use it
    elem = elem[0] or elem
    if isNaN(elem.offsetHeight)
      elem.document.documentElement.clientHeight
    else
      elem.offsetHeight
  #inputAddr -> input in modal, on top
  #inputAddrRet -> display in edit page
  #searchAddrRet -> search result in modal, bottom

  # NOTE: still used in web?
  $scope.popAddrModal = ()->
    # if (not vars.isApp)# or (/app\.test\:/.test(document.URL))
    #   toggleModal('inputAddressModal');
    #   $scope.inputAddr = $scope.searchAddrRet = getFormattedAddr($scope.formData)
    #   $scope.inputZipRet = $scope.formData.zip?.replace(' ','').toUpperCase()
    #   $scope.inputSearchAddr = $scope.inputAddrRet #$scope.searchAddrRet?.toString()
    #   $scope._generateMap({lat:$scope.formData.lat,lng:$scope.formData.lng})
    #   # $scope.searchAddr()
    #   return
    # else
    cfg = {hide:false,title:'Select Address'}
    url = "/1.5/map/searchLocation?1=1"
    lat=$scope.formData.lat
    lng = $scope.formData.lng
    if lat and lng
      url = url+'&lat='+lat+'&lng='+lng

    addr = getFormattedAddr($scope.formData)
    if addr
      url = url+'&addr='+addr
    url = RMSrv.appendDomain(url)
    RMSrv.getPageContent(url, '#callBackString', cfg, (val)->
      if val is ':cancel'
        return
      else
        # console.log val
        # val = '{"addr":"22 Dersingham Crescent","address":"22 Dersingham Crescent, Markham, Ontario L3T 4P5, Canada","lat":43.8149393,"lng":-79.3618453,"place_id":"address.1672716579465562","st_num":"22","st":"Dersingham Crescent","zip":"L3T 4P5","city":"Markham","prov":"Ontario","cnty":"Canada"}'
        try
          ret = JSON.parse(val)
          resetFormAddress('tmpData')
          $scope.inputAddrRet = ret.address
          $scope.inputZipRet = ret.zip
          $scope.tmpData = ret
          $scope.$apply()
          $scope.closeAddrModal()
        catch e
          console.log e
          # console.log $scope.tmpData
    )

  # if ensureMap()
  # geocoder = null
  # geocodeTs = Date.now()

  mapHeight = mapWidth = 0
  #put {lat:123,lng:123} on map, used in global
  $scope._generateMap = (data)->
    # mapDiv =  document.getElementById('id_d_map')
    # unless mapHeight or mapWidth
    #   mapHeight = $scope.height(mapDiv)
    #   mapWidth = mapDiv.offsetWidth
    # imgSrc = vars.gmap_static + "?sensor=false&center=" + data.lat + "," + data.lng + \
    # "&zoom=14&size=" + mapWidth + "x" + mapHeight + "&maptype=roadmap&markers=color:red%7Clabel:J%7C" + data.lat +
    # "," + data.lng
    # $scope.mapImg = imgSrc
    # #            ?center=Brooklyn+Bridge,New+York,NY&zoom=13&size=600x300&maptype=roadmap
    # # &markers=color:blue%7Clabel:S%7C40.702147,-74.015794&markers=color:green%7Clabel:G%7C40.711614,-74.012318
    # # &markers=color:red%7Clabel:C%7C40.718217,-73.998284
    # return
    document.getElementById('id_d_map').style.height = (window.innerHeight - 210) + 'px'
    lat = 43.7182412
    lng = -79.378058
    ll = data
    map = undefined
    marker = undefined
    opts = undefined
    geocoder = undefined
    address =  $scope.inputAddr or $scope.inputSearchAddr or "Toronto, ON, CA"
    ll = new google.maps.LatLng(lat, lng)
    opts =
      zoom: 12
      center: ll
      mapTypeControl: false
      mapTypeControlOptions:
        style: google.maps.MapTypeControlStyle.DROPDOWN_MENU
      navigationControl: false
      mapTypeId: google.maps.MapTypeId.ROADMAP
    if (!window.map)
      map = new google.maps.Map(document.getElementById("id_d_map"), opts)
      window.map = map
    geocoder = new google.maps.Geocoder()
    if geocoder
      geocoder.geocode
        address: address
      , (results, status) ->
        geocodePosition = (pos) ->
          geocoder.geocode
            latLng: pos
          , (responses) ->
            if responses and responses.length > 0
              scope = angular.element(document.getElementById('realtorUtilPage')).scope()
              scope.inputAddr = responses[0].formatted_address
              scope._processRetAddr(responses[0])
              scope.$apply()
            else
              console.log "Cannot determine address at this location."
        if status is google.maps.GeocoderStatus.OK
          unless status is google.maps.GeocoderStatus.ZERO_RESULTS
            window.map.setCenter results[0].geometry.location
            if (!window.marker)
              window.marker = marker =  new google.maps.Marker(
                position: results[0].geometry.location
                map: map
                draggable: true
                animation: google.maps.Animation.DROP
                title: address
                optimized: false
              )
              marker.addListener "click", ->
                if marker.getAnimation() isnt null
                  marker.setAnimation null
                else
                  marker.setAnimation google.maps.Animation.BOUNCE
              google.maps.event.addListener marker, "dragend", ->
                # updateMarkerStatus('Drag ended');
                geocodePosition marker.getPosition()
          else
            RMSrv.dialogAlert "No results found"
        else
          RMSrv.dialogAlert "Geocode was not successful for the following reason: " + status

  #used in global for google autocomplete
  $scope._processRetAddr = (place)->
    return unless place?.address_components
    # console.log place
    for i in [0..place.address_components.length-1]
      type = place.address_components[i].types[0]
      if (type is "postal_code")
        $scope.fullAddr = true
        #$scope.propDataHasChanged = true
        #$scope.propForm.$dirty = true
        $scope.gotFullAddrFromSearch = true
        # $scope.$apply()
        break
    # $scope.searchAddrRet = place.formatted_address
    resetFormAddress('tmpData')
    # debug results[0].address_components
    # unless $scope.fullAddr
    #   resetFormAddress(true)
    fld = "tmpData"
    $scope[fld]['lat'] = if typeof place.geometry?.location?.lat is 'function' then place.geometry.location.lat() else place.geometry.location.lat
    $scope[fld]['lng'] = if typeof place.geometry?.location?.lng is 'function' then place.geometry.location.lng() else place.geometry.location.lng
    for i in [0..place.address_components.length-1]
      type = place.address_components[i].types[0]
      nm = place.address_components[i].short_name
      ln = place.address_components[i].long_name
      # console.log type+':'+nm
      #keep incomplete addr in tmpData
      # if $scope.fullAddr
      #   fld = "formData"
      # else
      if type is "street_number" #addr
        $scope[fld]['addr'] = nm + ' '
        $scope[fld]['st_num'] = nm or ''
      else if type is "route" #addr
        $scope[fld]['addr'] += nm
        $scope[fld]['st'] = nm
      else if type is "neighborhood" #might not have this value
        $scope[fld]['cmty'] = nm
      else if type is "locality" #addr
        $scope[fld]['city'] = nm
      else if type is "administrative_area_level_2"
        # $scope[fld]['cmty'] = nm
      else if type is "administrative_area_level_1" # addr
        $scope[fld]['prov'] = ln
      else if type is "country" #addr
        $scope[fld]['cnty'] = ln
      else if (type is "postal_code") or (type is "postal_code_prefix")#addr
        $scope.zipRet3 = nm.substr(0,3)
        $scope.inputZipRet = nm.replace(/\s+/g, '')
        #will auto set inputAddrRet
        # if $scope.fullAddr
        #   $scope.inputAddrRet = place.formatted_address
        $scope[fld]['zip'] = nm.replace(/\s+/g, '')
        # $scope.$apply()
      else
        console.log type

    # console.log $scope.tmpData
    if not $scope[fld]['st_num']
      st_num = $scope.extratStNum($scope.inputAddr)
      if st_num
        $scope[fld]['st_num'] = st_num
        $scope[fld]['addr'] = st_num + ' ' + $scope[fld]['addr']
    $scope.searchAddrRet = getFormattedAddr($scope.tmpData)
    null

  $scope.extratStNum = (addr) ->
    #probally has problem if no st num is entered,like 19th Sideroad, King, ON, Canada,
    if /^\d/.test addr
      return addr.split(' ')[0]
    return ''

  $scope.searchAddr = ->
    ###
    debug = console.log
    if (Date.now() - geocodeTs) < 3000
      debug "Re-entering searchAddr"
      return
    geocodeTs = Date.now()
    return unless ensureMap()
    geocoder ?= new google.maps.Geocoder()
    debug 'got geocoder'
    ###

    address =  $scope.inputAddr or $scope.inputSearchAddr or "Toronto, ON, CA"
    $scope.fullAddr = false
    $scope.inputZipRet = ""
    $scope.gotFullAddrFromSearch = false

    if (typeof address is 'string') and (address.toLowerCase().indexOf('ca') < 0)
      address += '&components=country:ca'
    # alert address
    firstChar = '?'
    if vars.gmap_geocode.indexOf('?') > 0
      firstChar = '&'
    $http.get(vars.gmap_geocode + firstChar + 'language=en&address=' + address
    ).success( (data)->
      # console.log data
      if data.status is 'OK'
        #show static map with no marker
        unless data.results.length #is google.maps.GeocoderStatus.ZERO_RESULTS
          return RMSrv.dialogAlert "No results found"
        results = data.results
        console.log results[0]
        pos = results[0].geometry.location
        setMarker(pos)
        $scope._processRetAddr(results[0])
      else
        RMSrv.dialogAlert "Geocode was not successful for the following reason: " + status
    ).error( (err)->
      console.log err
    )
    ###
    geocoder.geocode {address: address, componentRestrictions:{country: 'CA'}}, (results, status)->
      unless status is google.maps.GeocoderStatus.OK
        return RMSrv.dialogAlert "Geocode was not successful for the following reason: " + status
      if status is google.maps.GeocoderStatus.ZERO_RESULTS
        return RMSrv.dialogAlert "No results found"
      for i in [0..results[0].address_components.length-1]
        type = results[0].address_components[i].types[0]
        if (type is "postal_code")
          $scope.fullAddr = true
          #$scope.propDataHasChanged = true
          #$scope.propForm.$dirty = true
          $scope.gotFullAddrFromSearch = true
          $scope.$apply()
          break
      $scope.searchAddrRet = results[0].formatted_address
      resetFormAddress('tmpData')
      debug results[0].address_components
      # unless $scope.fullAddr
      #   resetFormAddress(true)
      for i in [0..results[0].address_components.length-1]
        type = results[0].address_components[i].types[0]
        nm = results[0].address_components[i].short_name
        #keep incomplete addr in tmpData
        # if $scope.fullAddr
        #   fld = "formData"
        # else
        fld = "tmpData"
        if type is "street_number" #addr
          $scope[fld]['addr'] = nm + ' '
          $scope[fld]['st_num'] = nm or ''
        else if type is "route" #addr
          $scope[fld]['addr'] += nm
          $scope[fld]['st'] = nm
        else if type is "neighborhood" #might not have this value
          $scope[fld]['cmty'] = nm
        else if type is "locality" #addr
          $scope[fld]['city'] = nm
        else if type is "administrative_area_level_2"
          # $scope[fld]['cmty'] = nm
        else if type is "administrative_area_level_1" # addr
          $scope[fld]['prov'] = nm
        else if type is "country" #addr
          $scope[fld]['cnty'] = nm
        else if (type is "postal_code") or (type is "postal_code_prefix")#addr
          $scope.zipRet3 = nm.substr(0,3)
          $scope.inputZipRet = nm.replace(/\s+/g, '')
          #will auto set inputAddrRet
          if $scope.fullAddr
            $scope.inputAddrRet = results[0].formatted_address
          $scope[fld]['zip'] = nm.replace(/\s+/g, '')
          $scope[fld]['lat'] = results[0].geometry.location.lat()
          $scope[fld]['lng'] = results[0].geometry.location.lng()
          $scope.$apply()
        else
          debug type
      debug "before show marker"
      pos = results[0].geometry.location
      debug "before set center"
      gmap.setCenter pos
      debug "before set marker"
      if gm = window.gmapMarker
        gm.setPosition pos
      else
        window.gmapMarker ?= new google.maps.Marker(
          position: pos
          map: gmap
          optimized: false
          #animation: google.maps.Animation.DROP
          #title: address
        )
      debug "after set marker"
    ###
  # document.querySelector('#toggleImgSelect').addEventListener("click", ((e)->
  #   toggleModal('imgSelectModal')
  #   e.preventDefault()
  #   e.stopPropagation()
  #   ), true)
  # document.querySelector('#listUserPics').addEventListener("click", ((e)->
  #   $scope.getUserFiles(true)
  #   ), true)
  $scope.showImgSelect = ()->
    opt =
      url :'/1.5/img/insert'
    insertImage opt,(val)->
      # alert 'insertImage cb'+JSON.stringify val
      #  val = '{"picUrls":["http://file.test:8091/img/G/BWMB/BHA.png","http://file.test:8091/img/G/BWMB/BHB.png"]}
      if val is ':cancel'
        console.log 'canceled'
        return
      try
        # alert val
        ret = JSON.parse(val)
        if ret.e or ret.err or (ret.tp is 'alert')
          return
        if ret.userFiles and not $scope.userFiles
          $scope.userFiles = ret.userFiles
        if ret.picUrls.length isnt 0
          $scope.propForm.$dirty = true
          for img in ret.picUrls
            if $scope.picUrls.indexOf(img) is -1
              $scope.picUrls.push(img)
              $scope.$apply()
      catch e
        # alert 'Error:'+e.toString()
        console.error e
      return
    # setTimeout ()->
    #   picUrls = ["http://file.test:8091/img/G/BWMB/BHK.png","http://file.test:8091/img/G/BWMB/BHL.png","http://file.test:8091/img/G/BWMB/BHM.png"]
    #   for img in picUrls
    #     if $scope.picUrls.indexOf(img) is -1
    #       $scope.picUrls.push(img)
    #       $scope.$apply()
    # ,120
    # toggleModal('imgSelectModal');
    # $scope.hideBackdrop = true
    # #remove selected border and list
    # $scope.selected = []
    # imgs = document.querySelectorAll("#imgSelectPicList .thumb-wrapper img.selected")
    # for i in imgs
    #   i.classList.remove('selected')

  $scope.setCnum = (k,v)->
    if $scope.formData[k] is undefined
      $scope.formData[k] = 1
      return null
    $scope.formData[k] = parseInt($scope.formData[k]) or 0
    if $scope.formData[k] is 0 and v is -1
      return null
    $scope.formData[k] += v
    $scope.propForm.$dirty = true

  $scope.setBool = (k, v)->
    if ($scope.formData[k] is 0 or $scope.formData[k] is 1)and $scope.formData[k] is v
      return $scope.formData[k] = undefined
    $scope.formData[k] = v
    $scope.propForm.$dirty = true

  $scope.save = ()->
    $scope.toggleModal('savePromoteModal', 'close')
    $scope.propDataHasChanged = false
    $scope.changeSaved = true
    return unless $scope.canSave
    if $scope.formData['status'] is "Active"
      $scope.toggleModal('savePromoteModal', 'open')
    else
      $scope.hideBackdrop = true

  $scope.cancelSaveListing = ()->
    toggleModal('saveTipModal')
    $scope.hideBackdrop = true
    $scope.changeSaved = true
    if $scope.tip_save_change
      toggleModal('editListingModal','close')
      $scope.tip_save_change = false

  $scope.cancelPromoteModal = ()->
    toggleModal('savePromoteModal')
    $scope.hideBackdrop = true
    $scope.changeSaved = true
    # if $scope.tip_save_change
    #   toggleModal('editListingModal','close')
    #   $scope.tip_save_change = false

  $scope.showPromote = (isValid)->
    if isValid and $scope.formData['status'] is 'Active'
      $scope.tip_save_ready = true
      toggleModal('savePromoteModal')
      $scope.hideBackdrop = false
    else
      flashMessage('tip_save_err')
      $scope.showError = true
      return null

  $scope.getInvalidInput = ()->
    for i in ['bdrms','bthrms','kch','gr','lp','mfee','tax']
      if $scope.propForm[i]? and $scope.propForm[i].$dirty
        v = $scope.formData[i]
        if v == null
          continue
        if i not in ['mfee','tax']
          return i if not v?
        return i if not /\d+/.test v
    null

  $scope.deleteProp = (prop)->
    return unless prop.id
    tip = vars.str_confirm or 'Confirm'
    later = vars.vipLaterStr or 'Later'
    seemore = vars.str_yes or 'Yes'
    _doShowVip = (idx)->
      if idx+'' isnt '2'
        return
      data = {id:prop.id,_id:prop._id}
      $http.post('/1.5/promote/api', {cmd:'pmt-p-delete', data:data })
      .success (data)->
        unless data.ok
          return console.log 'Err:del '+data.err
        # toggleModal('promoteModal')
        # toggleModal('editListingModal', 'close')
        if $scope.formData.id
          $scope.promptSaveModal()
        flashMessage('deleted')
        if vars.isPopup
          return window.rmCall(':ctx::cancel')
        #instead of load all, refresh the tartget
        # console.log data
        $scope.loadListings($scope.type,0)
        # updateAfterPromoted data
        #$scope.$apply()
        $timeout ->
          $scope.$apply()
        return
      .error (data)->
        RMSrv.dialogAlert(data.toString())
    return RMSrv.dialogConfirm(tip, _doShowVip, "", [later, seemore])

  $scope.submitForm = (isValid, promote)->
    if not isValid
      $scope.showError = true
    #判断如果不满足发布条件直接return
    return flashMessage('publish_draft_err') if (isValid is false) and (promote is true)
    #show save tips
    $scope.hideBackdrop = false
    $scope.tip_save_err = false
    $scope.tip_save_ready  = false
    $scope.tip_save_change = false
    $scope.tip_save_inactive = false
    $scope.tip_save_other = false
    if fld = $scope.getInvalidInput()
      $scope.hideBackdrop = true
      $scope.showError = true
      msg1 = ''
      elem = document.querySelector('input[name='+fld+']')
      if elem and sib = document.querySelector('div[field-name='+fld+']')#elem?.parentElement?.previousSibling
        msg1 = sib.textContent+' '
      msg2 = document.querySelector('#fm-integers-only').innerText
      document.querySelector('#fm-integers-only2 .flash-message-inner').textContent = msg1+msg2
      return flashMessage('integers-only2')

    if $scope.formData.rvkRsn
      $scope.formData.rvkRsn = ''
    $scope.canSave = true
    status = $scope.formData['status']
    if status is 'Active'
      $scope.tip_save_ready = true
      # not prompt for promote
      # toggleModal('savePromoteModal');
    else if status is 'Inactive'
      $scope.tip_save_inactive = true
    else
      $scope.tip_save_other = true
    $scope.hideBackdrop = true
    #modify imgs list to cust format
    if $scope.picUrls
      $scope.formData['pic'] = proplib.convert_rm_imgs($scope, $scope.picUrls, 'set')
      $scope.formData['pho'] = $scope.picUrls?.length
    # tmp = $scope.formData['ptp']
    $scope.formData['pstyl'] = $scope.formData['ptp'][1] if $scope.formData['ptp']
    $scope.formData['addr58'] = $scope.formData['addr'] if $scope.formData['addr']
    if $scope.formData['rtp'] is 'epl'
      $scope.formData['rgdr'] = null
    # save id to listing
    # after success set scope.promoteID
    $scope.changeSaved = true
    #保存数据是否填完
    $scope.formData['isValid'] = isValid
    # console.log $scope.formData
    $http.post('/1.5/promote/api', {cmd:'pmt-p-save', data:{prop:$scope.formData} }, {timeout: 5000})
    .success (data)->
      unless data.ok
        return RMSrv.dialogAlert 'Err:save '+data.err
      if data.id
        $scope.formData['id'] = data.id
      $scope.prop = $scope.formData
      flashMessage('saved')
      $scope.loadListings($scope.type,0)
      $timeout (->
        $scope.changeSaved = true
        if promote
          $scope.promote('market', $scope.prop)
      ), 100
      if vars.promoteAfterSave
        $scope.promote(vars.to, $scope.formData)
        vars.promoteAfterSave = 0
      $timeout ->
        $scope.$apply()
      return
    .error (data)->
      RMSrv.dialogAlert(data.toString())
    $scope.propStatus = $scope.formData['status']
    $scope.loadListings($scope.type, 0)

  $scope.dialogAlertVip = ()=>
    tip = vars.vipTipStr or "Available only for Premium VIP user! Upgrade and get more advanced features."
    later = vars.vipLaterStr or 'Later'
    seemore = vars.vipSeeStr or 'See More'
    _doShowVip = (idx)=>
      # url = '/event?tpl=getvip'
      # if vars.lang
      #   url += "&lang=" + vars.lang
      url = 'https://www.realmaster.ca/membership'
      RMSrv.showInBrowser(url) if idx+'' is '2'
    return RMSrv.dialogConfirm(tip, _doShowVip, "VIP", [later, seemore])

  $scope.promote = (to, prop)->
    return unless prop.id
    if prop.isValid == false
      flashMessage('publish_draft_err')
      $scope.showError = true
      return
    # NOTE: ignore promote mls listing
    # if (to is 'market') and prop.ltp in ['mlslisting','mlsrent']
    #   return
    #to = 58/market/wecard
    if (to isnt 'wecard') and (prop.ltp isnt 'rent') and (not vars.isVipUser)
      # return flashMessage('vip-only')
      return $scope.dialogAlertVip()
    if to is 'wecard'
      prop.rcmd = !prop.rcmd
      console.log 'posting to server'
      data = {
        id:prop.id
        rcmd:prop.rcmd
      }
      $http.post('/1.5/promote/api', {cmd:'pmt-p-rcmd', data:data })
      .success (data)->
        unless data.ok
          return RMSrv.dialogAlert 'Err:get status '+data.err
        return
      .error (data)->
        RMSrv.dialogAlert(data.toString())
      # $scope.$apply()
      return
    if (prop['status'] isnt "Active") #and to is '58'
      # if not promoted before
      if to is 'market' and prop['market'].st is 'Promoted'
        console.log 'user can revoke'
      else
        flashMessage('status-not-active')
        return null
    $scope.hideBackdrop = true
    $scope.promoteID = prop.id
    prop.addr58 ?=  prop.addr
    bdrms = $scope.template?.pd[1]?.fldV or vars.str_bdrms or ''
    bthrms = $scope.template?.pd[2]?.fldV or vars.str_bthrms or ''
    ptp = if Array.isArray($scope.formData.ptp) then $scope.formData.ptp[3] else (prop.ptpT or prop.ptp or '')
    if prop['58'] and prop.tl #already posted
      prop.tl58 = prop.tl
    else
      prop.tl58 = (prop.cityT or prop.city or '') + ' ' + (ptp or '') + ' ' + (bdrms or '') + (prop.bdrms or '') + '+' \
        + bthrms + (prop.bthrms or '') + ' ' + $filter('currency')(prop.lp, '$', 0) # force update if has change
    $scope.curProp = prop
    if to is 'weshare'
      # invoke RMSrv.share weshare
      return null
    $scope.toggleModal('promoteModal')
    if ($scope.promoteTo isnt to) #(not $scope.userData['fn'] ) or
      getUserData(to)
    $scope.promoteTo = to
    # toggleModal('savePromoteModal')
    RMSrv.share('hide')
    getPromoteStatus()
    # $scope.$apply()

  # publish current promote req to server
  $scope.publish = ()->
    return unless ($scope.promoteTo and $scope.promoteID)
    if ($scope.formData['status'] isnt "Active") and not $scope.curProp
      flashMessage('status-not-active')
      return null
    if $scope.curProp.rvkRsn
      flashMessage('revoke-not-change')
      return null
    data = {}
    data.to = $scope.promoteTo
    if (data.to is 'market') and $scope.curProp.ltp in ['mlslisting','mlsrent']
      return flashMessage('no-longer-supported')
    $scope.showError = false
    if (data.to is 'uhouzz') and ($scope.promoteUhouzz is false)
      RMSrv.dialogAlert "Not allowed"
      return null
    if $scope.dataHasChanged
      data.user = $scope.userData
    if (data.to is 'market') and $scope.commi
      # 2022-05-05 不强制选择 adok
      # if not $scope.curProp['market'].adok
      #   $scope.showError = true
      #   return null
      if ($scope.curProp['market'] and $scope.curProp['market'].cmstn )#and $scope.agreeCheck
        data.cmstn = $scope.curProp['market'].cmstn
        data.rmProp = $scope.curProp['market'].rmProp
        data.sortOrder = $scope.curProp['market'].sortOrder
        data.agrmntImg = $scope.curProp['market'].agrmntImg
        data.adok = $scope.curProp['market'].adok
        data.isV = $scope.curProp['market'].isV
        data.nm = $scope.curProp['market'].nm if $scope.curProp['market'].nm
        data.mbl = $scope.curProp['market'].mbl if $scope.curProp['market'].mbl
      else
        $scope.showError = true
        flashMessage('cmstn-req')
        return null
    if data.to is '58'
      # if $scope.curProp.tl58 != $scope.curProp.tl#will overwrite anyway
      data.tl = $scope.curProp.tl58
      data.addr58 = $scope.curProp.addr58
    data.id = $scope.promoteID
    data.marketPromoTs = $scope.curProp.marketPromoTs
    $http.post('/1.5/promote/api', {cmd:'pmt-p-pub', data:data }, {timeout: 5000})
    .success (data)->
      unless data.ok
        if data.unverify
          tip = data.err
          later = vars.str_cancel or 'Cancel'
          seemore = vars.str_verify or 'Verify'
          _doShowVerify = (idx)->
            if idx+'' isnt '2'
              return
            return $scope.openVerifyPopup()
          return RMSrv.dialogConfirm(tip, _doShowVerify, "", [later, seemore])
        else
          return RMSrv.dialogAlert ''+data.err
      toggleModal('promoteModal')
      $scope.hideBackdrop = true
      toggleModal('editListingModal', 'close')
      flashMessage('promoted')
      # light the market icon, find the listing and update it
      # console.log data
      $scope.loadListings vars.type, 0  if data.to is '58' #58 updates tl and addr58
      updateAfterPromoted data
      # $scope.loadListings $scope.type, 0
      return
    .error (data)->
      RMSrv.dialogAlert(data.toString())

  $scope.revoke = ()->
    return unless ($scope.promoteTo and $scope.promoteID)
    unless $scope.curProp[$scope.promoteTo]
      return flashMessage("revoke_fail")
    data = {}
    data.to = $scope.promoteTo
    data.id = $scope.promoteID
    $http.post('/1.5/promote/api', {cmd:'pmt-p-revoke', data:data })
    .success (data)->
      unless data.ok
        return console.log 'Err:revoke '+data.err
      toggleModal('promoteModal')
      toggleModal('editListingModal', 'close')
      flashMessage('revoked')
      #instead of load all, refresh the tartget
      # console.log data
      updateAfterPromoted data
      #$scope.$apply()
      return
    .error (data)->
      RMSrv.dialogAlert(data.toString())

  $scope.uploadAgrmntImg = ()->
    opt =
      url :'/1.5/img/insert?uploadOne=1&imgSize=xl'
    insertImage opt,(val)->
      #  val = '{"picUrls":["http://file.test:8091/img/G/BWMB/BHA.png","http://file.test:8091/img/G/BWMB/BHB.png"]}
      if val is ':cancel'
        console.log 'canceled'
        return
      try
        ret = JSON.parse(val)
        if ret.userFiles and not $scope.userFiles
          $scope.userFiles = ret.userFiles
        if ret.picUrls.length > 0
          $scope.curProp['market'].agrmntImg = ret.picUrls[0]
        $scope.$apply()
      catch e
        console.error e
      return

  $scope.deleteAgrmntImg = ()->
    $scope.curProp['market'].agrmntImg = ''
    $scope.curProp['market'].isV = false
    # $scope.$apply()

  getPromoteStatus = ()->
    data = {
      id:$scope.promoteID
      to:$scope.promoteTo
    }
    #user save and promote, no id at this time
    unless data.id and data.to
      return
    $http.post('/1.5/promote/api', {cmd:'pmt-p-status', data:data })
    .success (data)->
      unless data.ok
        return RMSrv.dialogAlert 'Err:get status '+data.err
      #toggleModal('promoteModal')
      # console.log data
      updateAfterPromoted data
      return
    .error (data)->
      RMSrv.dialogAlert(data.toString())

  getTemplate = (type)->
    $http.post('/1.5/promote/api', {cmd:'pmt-p-tpl', data:type }, {timeout: 5000})
    .success (data)->
      if data.ok is 1
        $scope.template = data.tpl
        getPtpIndex = (list, ptp)->
          return unless list
          for i in [0..list.length-1]
            if list[i][2] == ptp
            # if angular.equals(list[i], ptp)
              return i
          0
        # save and display different value, pd[0] is drpd2 {}
        dbvs = data.tpl.pd[0].dbv
        vs = data.tpl.pd[0].v
        for i in [0..dbvs.length-1]
          dtmp = dbvs[i][0] + ' ' + dbvs[i][1]
          vtmp = vs[i][0] + ' ' + vs[i][1]
          dbvs[i].push dtmp #[2]
          dbvs[i].push vtmp #[3]
          # vs[i].push vtmp
        $scope.ptpList = data.tpl.pd[0]
        if $scope.formData.ptp
          index = getPtpIndex($scope.ptpList.dbv, $scope.formData.ptp[2])
        $scope.formData.ptp = $scope.ptpList.dbv[index]
        $scope.formData.exp = new Date( new Date().valueOf() + 3600000 * 24 * 90) #3 month
        picUrlsListBox = document.querySelector("#picUrlsList")
        opt =
          animation: 150,
          ghostClass: 'sort-placeholder',
          delay:100,
          delayOnTouchOnly:true,
          touchStartThreshold: 10,
          onUpdate: (evt)->
            newIndex = evt.newIndex
            oldIndex = evt.oldIndex
            item = $scope.picUrls.splice(oldIndex,1)
            $scope.picUrls.splice(newIndex,0,item[0])
            $scope.$apply()
        $scope.sortable = new Sortable picUrlsListBox,opt
        $timeout ->
          $scope.$apply()
      else
        RMSrv.dialogAlert 'Err:getting template' + data.err
    .error (data)->
      RMSrv.dialogAlert(data.toString())

  $scope.createListing = (type)->
    # alert type
    resetFormAddress('tmpData')
    $scope.toggleModal('newListingModal', 'close')
    $scope.hideBackdrop = true
    if type in ['assignment','exlisting']
      if not vars.isRealtor
        return $scope.toggleModal('signupModal')
    $scope.toggleModal('editListingModal')
    $scope.showError = false
    $scope.picUrls = []
    $scope.inputAddrRet = undefined
    $scope.inputZipRet = undefined
    $scope.inputAddr = undefined
    $scope.fullAddr = false
    $scope.formData = {ltp:type, status:"Active"}
    # $scope.formData['exp'] = new Date(new Date() + 3600*1000*24*90)
    $scope.prop = $scope.formData
    $scope.propStatus = "Active"
    getTemplate(type)

  $scope.pickDate = (fld)->
    $scope.toggleModal('datePickerModal')
    $scope.fldTobeUpdated = fld
    $scope.hideBackdrop = false
    $scope.date = $scope.minDate
    if $scope.fldTobeUpdated is 'exp'
      $scope.dateMode = true
      $scope.rangeMode = false
      $scope.dateOnly = true
      return
    $scope.dateMode = false
    $scope.rangeMode = false
    $scope.dateOnly = false
    if $scope.formData[fld] and $scope.formData[fld] isnt 'immed' and $scope.formData[fld] isnt 'tba'
      $scope.dateMode = true
    # if $scope.formData[fld] and $scope.formData[fld].toString().indexOf('~') > 0
    if $scope.formData[fld + 'f']
      $scope.rangeMode = true
      $scope.range = "from"
      # $scope.dateFrom = $scope.formData[fld].split('~')[0]
      # $scope.dateTo = $scope.formData[fld].split('~')[1]
      $scope.dateFrom = $scope.formData[fld+'f']
      $scope.dateTo = $scope.formData[fld+'t']

  $scope.setDate = (v)->
    if v is 'tba' or v is 'immed'
      return if $scope.fldTobeUpdated is 'exp'
      $scope.dateMode = false
      $scope.rangeMode = false
      $scope.hideBackdrop = true
      $scope.formData[$scope.fldTobeUpdated] = v
      $scope.range = ""
      toggleModal('datePickerModal')
      delete $scope.formData[$scope.fldTobeUpdated+'f']
      delete $scope.formData[$scope.fldTobeUpdated+'t']
      $scope.dateFrom = undefined
      $scope.dateTo = undefined
      # $scope.showDP = false;
      $scope.rangeMode = false
      $scope.propForm.$dirty = true
    else if v is 'date'
      # $scope.showDP = true
      $scope.dateMode = true
      $scope.rangeMode = false
    else
      return if $scope.fldTobeUpdated is 'exp'
      $scope.range = 'from'
      $scope.dateMode = false
      $scope.rangeMode = true

  $scope.loadListings = (type, skip)->
    # alert type
    $scope.loading = true
    if $scope.searchId.length > 0
      return searchById()
    $scope.type = type
    # if type is 'mylisting'
    #   $scope.title = vars.tml
    # else
    #   $scope.title = vars.tlm
    #send listings command to server and get response
    data = {type:type}
    data.skip = skip or 0
    $http.post('/1.5/promote/api', {cmd:'pmt-p-lst', data:data })
    .success (data)->
      getFirstImg = (data)->
        return unless data
        for i in data.l
          if i.pic
            i.pic.ml_num = i.sid or i.ml_num # TODO: if i.src is 'TRB'
            i.img = proplib.convert_rm_imgs($scope, i.pic, 'reset')[0]
      # $scope.template = data.tpl
      if data.ok is 1
        #market has user list
        if data.type is 'market'
          fld = "marketListings"
          userDict = {}
          for user in data.ul
            userDict[user._id] = user
          for prop in data.l
            user = userDict[prop.uid]
            prop.fn   = user.fn
            prop.ln   = user.ln
            prop.avt  = user.avt
            prop.cpny = user.cpny
            prop.mbl  = user.mbl
            prop.tel  = user.tel
        else
          fld = "mylistings"
      else
        return console.log data.err
      getFirstImg(data)
      if data.skip is 0
        $scope[fld] = data.l
      else if $scope[fld]
        $scope[fld] = $scope[fld].concat data.l
      else
        $scope[fld] = data.l
      $scope.allListing = $scope[fld]
      $scope.hasMore = if data.l.length >= 20 then  true else false
      # remove loading
      $scope.loading = false
      document.getElementById('busy-icon').style.display = 'none'
      $timeout ->
        $scope.$apply()
      return
    .error (data)->
      RMSrv.dialogAlert(data.toString())

  $scope.loadMore = ()->
    return unless $scope.hasMore
    return if $scope.loading
    fld =  if $scope.type is 'market' then "marketListings" else "mylistings"
    skip = $scope[fld].length
    $scope.loadListings($scope.type, skip)

  $scope.getContactList = ()->
    $http.post('/1.5/rmlisting/contactList', {})
    .success (data)->
      unless data.ok
        flashMessage 'server-error'
        return
      $scope.contactList = data.result
      for item in $scope.contactList
        item.isShowDelete = false
      toggleModal('selectContactModal')
      return
    .error (data)->
      RMSrv.dialogAlert(data.toString())

  $scope.saveContact = ()->
    unless $scope.contactInfo.nm and $scope.contactInfo.mbl
      flashMessage 'conten_empty'
      return
    info = {nm:$scope.contactInfo.nm,mbl:$scope.contactInfo.mbl}
    if $scope.contactInfo.id
      info.id = $scope.contactInfo.id
    $http.post('/1.5/rmlisting/saveContact', info)
    .success (data)->
      unless data.ok
        flashMessage 'server-error'
        return
      if $scope.contactInfo.id
        index = $scope.contactList.findIndex((val)-> return val._id.toString() is $scope.contactInfo.id.toString())
        $scope.contactList[index].nm = $scope.contactInfo.nm
        $scope.contactList[index].mbl = $scope.contactInfo.mbl
      else
        info._id = data._id
        info.isShowDelete = false
        $scope.contactList.unshift(info)
      $scope.contactInfo = {nm:'',mbl:'',id:null}
      $scope.isAddContact = false
      flashMessage 'saved'
      return
    .error (data)->
      RMSrv.dialogAlert(data.toString())
  
  $scope.cancelSave = ()->
    $scope.contactInfo = {nm:'',mbl:'',id:null}
    $scope.isAddContact = false
  
  $scope.deleteContact = (id,index)->
    $http.post('/1.5/rmlisting/deleteContact', {id})
    .success (data)->
      unless data.ok
        flashMessage 'server-error'
        return
      $scope.contactList.splice(index,1)
      flashMessage 'deleted'
      return
    .error (data)->
      RMSrv.dialogAlert(data.toString())
  
  $scope.selectContact = (nm,mbl)->
    $scope.curProp['market'].nm = nm
    $scope.curProp['market'].mbl = mbl
    toggleModal('selectContactModal','close')
  
  $scope.editContact = (id,nm,mbl)->
    $scope.contactInfo.nm = nm
    $scope.contactInfo.mbl = mbl
    $scope.contactInfo.id = id
    $scope.isAddContact = true
  
  $scope.confirmDeletion = (index)->
    $scope.contactList[index].isShowDelete = true
  $scope.cancelDelete = (index)->
    $scope.contactList[index].isShowDelete = false
  $scope.deleteSel = ()->
    delete $scope.curProp['market'].nm
    delete $scope.curProp['market'].mbl
  # $scope.addServiceCity = (k,n)->
  #   unless $scope.userData.ck
  #     $scope.userData['ck'] = []
  #     $scope.userData['cn'] = []
  #
  #   if (index = $scope.userData.ck.indexOf(k)) >= 0
  #     $scope.userData['ck'].splice index,1
  #     $scope.userData['cn'].splice index,1
  #   else
  #     $scope.userData['ck'].push(k)
  #     $scope.userData['cn'].push(n)
  #   #limit to one, TODO: might remove in future when 58 supports
  #   if $scope.userData.ck.length > 1
  #     $scope.userData.ck = [$scope.userData['ck'].pop()]
  #     $scope.userData.cn = [$scope.userData['cn'].pop()]
  #   return

  # $scope.showCityList = ()->
  #   getCityList()
  #   toggleModal('citySelectModal')
  #also used in img-select-modal view
  $scope.showAllImg = ()->
    if $scope.hasMoreImg
      $scope.hasMoreImg = false
      $scope.userFiles.pl = $scope.allImgs
  $scope.getRecentUserFiles = (files)->
    recent = {}
    thres = 40
    if files?.count > thres
      $scope.hasMoreImg = true
      $scope.allImgs = files.pl
      start = files.count - thres
      for k,v of files.pl
        if v.i > start
          recent[k] = v
      files.pl = recent
    return files
  $scope.getUserFiles = (refresh)->
    # alert(refresh)
    return if $scope.userFiles and not refresh
    $scope.picS3RmConfirm = false
    $http.get('/1.5/userFiles.json', {})
    .success (data)->
      $scope.userFiles = $scope.getRecentUserFiles(data)
      # alert(JSON.stringify($scope.userFiles))
      $scope.selected = []
    .error (data)->
      $scope.message = data.message
      RMSrv.dialogAlert "Error when getting city list!"
    return
  #view this listing in 58 or market
  $scope.checkPromotResult = (to, prop)->
    if to isnt 'market' and prop[$scope.promoteTo]?.url
      RMSrv.showInBrowser prop[$scope.promoteTo].url
    # else if to is 'market'
    #   RMSrv.dialogAlert "No longer Supported!"
      # for i in ['fn','ln','cpny','mbl','tel','avt']
      #   $scope.curBrkg[i] = $scope.userData[i] or ""
      # toggleModal('promoteModal', 'close')
      # $scope.viewListing(prop)
      # window.location = "/1.5/promote/market?ml_num=" + prop.id
    return
  #used in img-select-modal, need
  $scope.selectImg = (event,k)->
    if (index = $scope.selected.indexOf(k)) >= 0
      event.target.classList.remove('selected')
      $scope.selected.splice index, 1
    else
      event.target.classList.add('selected')
      $scope.selected.push k

  $scope.toggleRemovePic = ()->
    $scope.picRmConfirm = ! $scope.picRmConfirm

  #de select in preview pic modal
  $scope.removePic = (img)->
    if previewImgType is 'agrmntImg'
      $scope.curProp['market'].agrmntImg = ''
      return $scope.toggleModal('imgPreviewModal', 'close')
    currIndex = $scope.picUrls.indexOf(img)
    return if currIndex < 0
    $scope.picUrls.splice currIndex, 1
    $scope.toggleModal('imgPreviewModal', 'close')
    $scope.propForm.$dirty = true

  $scope.removePicS3 = ()->
    del = {}
    del.fldr =  $scope.userFiles.fldr
    del.files = $scope.selected
    return unless del.files.length > 0
    if del.files.length > 9
      return RMSrv.dialogAlert 'You can select up to 9 images at a time!'
    $http.post('/1.5/deleteFiles', del)
    .success (ret)->
      if ret.ok is 1
        $scope.getUserFiles(true)
      else
        RMSrv.dialogAlert ret.err
    .error (ret)->
      $scope.err = ret.err
      RMSrv.dialogAlert "sever error!, please try again later"
    return

  $scope.editMyListing = (rmid)->
    $scope.inputAddrRet = undefined
    # show modal first
    toggleModal('editListingModal')
    $http.post('/1.5/rmprop/detail', {rmid:rmid}, {timeout: 5000})
    .success (data)->
      if data.ok
        # resetFormAddress('tmpData')
        data.prop.pic?.ml_num = data.prop.sid or data.prop.ml_num #TODO: if data.prop.src is 'TRB'
        $scope.picUrls = proplib.convert_rm_imgs($scope, data.prop.pic, 'reset')
        if data.prop.ts
          diff = (new Date() - new Date(data.prop.ts))/(24*3600*1000)
          data.prop.dom = parseInt diff
        $scope.hideBackdrop = true
        $scope.showError = false
        getTemplate(data.prop.ltp)
        data.prop.ptp =  [data.prop.ptp , data.prop.pstyl, data.prop.ptp + ' ' + data.prop.pstyl]
        $scope.propStatus = data.prop.status
        $scope.formData = data.prop
        #make a copy of address components
        for i in locfld
          $scope.tmpData[i] = data.prop[i]
        $scope.tmpData.zip = data.prop.zip

        $scope.changeSaved = true
        # mllisting with no zip
        $scope.inputZipRet = if data.prop.zip then data.prop.zip.replace(/\s+/g, '') else ""
        # $scope.inputAddr = (data.prop.addr or "") + ', ' + data.prop.city + ', ' + (data.prop.prov || '')
        # $scope.inputAddrRet = (data.prop.addr or "") + ' ' + data.prop.city + ', ' + (data.prop.prov || '')
        $scope.inputSearchAddr = $scope.inputAddrRet = getFormattedAddr(data.prop)
        # $scope.searchAddr()
        # if data.prop.status in ['Expired','Unavailable']
        #   $scope.template['psf'][1]['v'].push data.prop.state
        $scope.prop = data.prop
        $scope.showError = true
        $timeout (->
          # TODO: Rain: to check if these two are needed here. These raised error and $apply is not runned
          #$scope.propForm.tl.$pristine = false
          #$scope.propForm.$dirty = false
          $scope.propForm.$setPristine()
          $scope.propDataHasChanged = false
          # $scope.fullAddr = true
          $scope.$apply()
        ),10
      else
        RMSrv.dialogAlert(data.err)
    .error (data)->
      RMSrv.dialogAlert(data.err)

  $scope.showSMB = ()->
    return if $scope.formData['status'] is 'Inactive'
    if $scope.propDataHasChanged and $scope.propForm.$dirty and not $scope.changeSaved
      flashMessage('share_save_err')
      $scope.showError = true
      return
    if !$scope.propForm.$valid
      flashMessage('share_draft_err')
      $scope.showError = true
      return
    RMSrv.share('show')

  # insert img to formData
  $scope.insert = ()->
    $scope.getUserFiles() unless $scope.userFiles
    hide = ()->
      toggleModal "imgSelectModal"
      $scope.hideBackdrop = true
      $scope.propForm.$dirty = true
    unless $scope.picUrls
      $scope.picUrls = []

    if $scope.imgInputURL
      if $scope.picUrls.indexOf(sUrl) is -1
        $scope.picUrls.push($scope.imgInputURL)
      hide()
    else if $scope.selected and $scope.selected.length > 0
      data = $scope.userFiles
      selected = $scope.selected
      sUrl = undefined
      sName = undefined
      i = 0
      while i <= selected.length - 1
        sUrl = data.base + "/" + data.pl[selected[i]].nm
        sName = selected[i]
        if $scope.picUrls.indexOf(sUrl) is -1
          $scope.picUrls.push(sUrl)
        i++
      hide()
    else if input = document.querySelector("#imgInputFiles").files
      if input.length > 0
        $scope.propForm.$dirty = true
        processFiles input
      else
        console.log "no files"
    else
      console.log "no files"
  ##preview selection list pic
  $scope.previewPic = (src,type)->
    previewImgType = type or 'picUrls'
    $scope.toggleModal('imgPreviewModal')
    $scope.hideBackdrop = true
    $scope.currentPic = ""
    $scope.currentPic = src
    $scope.picRmConfirm = false
    # $scope.$apply()
  $scope.promptSaveModal = ()->
    if $scope.propDataHasChanged and $scope.propForm.$dirty and not $scope.changeSaved #and $scope.formDatap['status'] is "Active"
      toggleModal('saveTipModal', 'open')
      $scope.tip_save_change = true
      $scope.hideBackdrop = false
    else
      toggleModal('editListingModal', 'close')
      $scope.hideBackdrop = true
    $scope.sortable.destroy()
  #rootScope value
  $scope.titleFocus = (val)->
    $scope.editingTitle = val

  processFiles = (files) ->
    doone = undefined
    i = undefined
    if files and typeof FileReader isnt "undefined"
      i = 0
      doone = (err)->
        file = undefined
        if i < files.length
          file = files[i++]
          readFile file, (err)->
            doone(err)
        else
          unless err
            flashMessage "img-inserted"
          toggleModal "imgSelectModal"
      doone()
    else
      RMSrv.dialogAlert "Unsuppored browser. Can't process files."
      # RMSrv.dialogAlert "Unsuppored browser. Can't process files."
  getRMConfig = (img, cb) ->
    fd = {}
    sname = splitName(img.name, img.type)
    fd.ext = sname[1] or "jpg" #img.ext;
    img.ext = fd.ext
    # fd.isThumb = isThumb;
    fd.w = img.width
    fd.h = img.height
    fd.s = img.size
    # fd.t = (if hasThumb then 1 else 0)
    $scope.loading = true
    $scope.disabledInsert = true
    $http.post('/1.5/rmSign', fd)
    .success (data)->
      $scope.disabledInsert = false
      if data.key
        #Save to global
        window.rmConfig = data
        # console.log "successful got config"
        cb() if cb
      else
        if data.e
          RMSrv.dialogAlert data.e
        else
          flashMessage "server-error"
        # console.log "get config no key fail"
    .error (data)->
      $scope.disabledInsert = false
      $scope.message = data.message
      # console.log 'gets3 config fail'
      flashMessage "server-error"
  getS3Config = (img, cb, hasThumb) ->
    fd = {}
    fd.ext = "jpg" #img.ext;
    # fd.isThumb = isThumb;
    fd.w = img.width
    fd.h = img.height
    fd.s = img.size
    fd.t = (if hasThumb then 1 else 0)
    $scope.loading = true

    $http.post('/1.5/s3sign', fd)
    .success (data)->
      if data.key
        #Save to global
        window.s3config = data
        # console.log "successful got config"
        cb() if cb
      else
        # console.log "get config no key fail"
        flashMessage "server-error"
    .error (data)->
      $scope.message = data.message
      # console.log 'gets3 config fail'
      flashMessage "server-error"
  uploadFile = (img, cfg, cb) ->
    # isThumb = cfg.isThumb
    # hasThumb = cfg.hasThumb
    # file = undefined
    # console.log window.rmConfig
    fd = new FormData()
    $scope.disabledInsert = true
    payload = {type:'image/jpeg'}
    # if isThumb
    #   file = img.blob2
    #   fd.append 'thumbKey',rmConfig.thumbKey
    #   fd.append 'thumbSignature',rmConfig.thumbSignature
    # else
    file = img#.blob
    fd.append 'key',rmConfig.key
    fd.append 'signature',rmConfig.signature
    payload.fileNames = rmConfig.fileNames.join(',')
    payload.ext = img.ext or 'jpg'
    fd.append 'date',rmConfig.date
    fd.append 'backgroundS3',true
    fd.append 'contentType',rmConfig.contentType
    fd.append "file", file#, fileKey
    savePicServerUrl = rmConfig.credential
    success = 0
    fail = (data)->
      $scope.disabledInsert = false
      $scope.message = data?.message
      if data.e
        RMSrv.dialogAlert data.e
      else
        flashMessage "server-error"
      cb(data)
      $http.post('/1.5/uploadFail', {})
      .success ()->
         # console.log "upload fail post ret:" + JSON.stringify(data)
    # if isThumb
    #   file = img.blob2
    # else
    #   file = img.blob
    cfg = {
      transformRequest: angular.identity,
      headers: {'Content-Type': undefined} #allication/json
      # headers: {
      #   "Access-Control-Request-Origin":"anonymous"
      # }
    }
    $http.post(savePicServerUrl, fd, cfg)
    .success (ret)->
      $scope.disabledInsert = false
      unless ret.ok
        return fail(ret)
      $scope.loading = false
      # console.log ret
      # $.ajax( url: amGloble.uploadSuccess, data: payload, type: 'post' )
      payload.t = ret.hasThumb
      payload.w = ret.width
      payload.h = ret.height
      payload.s = ret.size
      $http.post("/1.5/uploadSuccess", payload, type:'post')
      $scope.picUrls.push(ret.sUrl)
      cb null
    .error (data)->
      $scope.loading = false
      fail(data)

  uploadFile2 = (img, isThumb) ->
    fail = (data)->
      $scope.message = data.message
      # console.log 'uploads3 fail'
      flashMessage "server-error"
      $http.post('/1.5/uploadFail', {})
      .success ()->
         # console.log "upload fail post ret:" + JSON.stringify(data)
    if isThumb
      file = img.blob2
    else
      file = img.blob
    fd = new FormData()
    fd.append "file", file#, fileKey
    #getUserCardInfo
    cfg = {
      transformRequest: angular.identity,
      headers: {'Content-Type': undefined} #allication/json
      # headers: {
      #   "Access-Control-Request-Origin":"anonymous"
      # }
    }
    $http.post("/file/uploadImg", fd, cfg)
    .success (data)->
      $scope.loading = false
      console.log data?
      # $scope.picUrls.push("http://" + window.s3config.s3bucket + "/" + window.s3config.key ) unless isThumb
      $scope.picUrls.push(data.sUrl) unless isThumb
    .error (data)->
      fail(data)
  uploadFile3 = (img, isThumb) ->
    fail = (data)->
      $scope.message = data?.message
      # console.log data
      flashMessage "server-error"
      $http.post('/1.5/uploadFail', {})
      .success ()->
         # console.log "upload fail post ret:" + JSON.stringify(data)
    file = undefined
    fileKey = undefined
    policy = undefined
    signature = undefined
    if isThumb
      file = img.blob2
      fileKey = window.s3config.thumbKey
      policy = window.s3config.thumbPolicy
      signature = window.s3config.thumbSignature
    else
      file = img.blob
      fileKey = window.s3config.key
      policy = window.s3config.policy
      signature = window.s3config.signature

    fd = new FormData()
    fd.append "acl", "public-read"
    fd.append "key", fileKey
    fd.append "x-amz-server-side-encryption", "AES256" #v4
    fd.append "x-amz-meta-uuid", "14365123651274" #v4
    fd.append "x-amz-meta-tag", "" #v4
    fd.append "Content-Type", window.s3config.contentType
    fd.append "policy", policy
    fd.append "x-amz-credential", window.s3config.credential
    fd.append "x-amz-date", window.s3config.date
    fd.append "x-amz-signature", signature

    # fd.append('success_action_redirect',window.s3config.success_action_redirect);
    fd.append "x-amz-algorithm", "AWS4-HMAC-SHA256"
    fd.append "file", file, fileKey
    s3Url = vars.s3protocol + "://" + vars.s3bucket + ".s3.amazonaws.com/"
    cfg = {
      transformRequest: angular.identity,
      headers: {'Content-Type': undefined} #allication/json
      # headers: {
      #   "Access-Control-Request-Origin":"anonymous"
      # }
    }
    $http.post(s3Url, fd, cfg)
    .success (data)->
      $scope.loading = false
      console.log data?
      $scope.picUrls.push("http://" + window.s3config.s3bucket + "/" + window.s3config.key ) unless isThumb
    .error (data)->
      fail(data)
  readFile = (file, callback) ->
    reader = undefined
    if /image/i.test(file.type)
      reader = new FileReader()
      reader.onload = (e) ->
        # image = undefined
        image = new Image()
        image.onload = ->
          # newimage = undefined
          # newimage = getCanvasImage(this, file)
          # hasThumb = (newimage.width > 400 or newimage.height > 300)
          if file.size > vars.maxImageSize
            RMSrv.dialogAlert(vars.tooLargeStr or 'Too Large')
            return
          getRMConfig file, (->
            # uploadFile newimage, false
            uploadFile file, {}, callback
          # uploadFile newimage, true  if hasThumb
          )
          # callback()
        image.src = e.target.result
      reader.readAsDataURL file
    else
      RMSrv.dialogAlert "Unsupported file extension for: "+file.name+". Please try other files."
      callback()
  splitName = (name, type) ->
    p = undefined
    if (p = name.lastIndexOf(".")) > 0
      [ name.substr(0, p), name.substr(p + 1).toLowerCase() ]
    else
      [ name, "." + type.substr(type.lastIndexOf("/")).toLowerCase() ]
  dataURItoBlob = (dataURI) ->
    ab = undefined
    byteString = undefined
    dataView = undefined
    i = undefined
    ia = undefined
    mimeString = undefined
    if dataURI.split(",")[0].indexOf("base64") >= 0
      byteString = atob(dataURI.split(",")[1])
    else
      byteString = unescape(dataURI.split(",")[1])
    mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0]
    ab = new ArrayBuffer(byteString.length)
    ia = new Uint8Array(ab)
    i = 0
    while i < byteString.length
      ia[i] = byteString.charCodeAt(i)
      i++
    dataView = new DataView(ab)
    new Blob([ dataView ],
      type: mimeString
    )
  getCanvasImage = (image, file) ->
    imgLWL = 1000 # for long edge
    imgLHL = 1000 # Long Edge Height Limit
    imgSWL = 680 #for short edge
    imgSHL = 680
    thumbH = thumbW = 128
    thumbNumber = 10
    canvas = undefined
    canvas2 = undefined
    ctx = undefined
    ctx2 = undefined
    h = undefined
    heightRatio = undefined
    img = undefined
    imh = undefined
    imw = undefined
    ratio = undefined
    sname = undefined
    w = undefined
    widthRatio = undefined
    xs = undefined
    ratio = 1
    if (image.width > imgLWL) or (image.height > imgLHL)
      widthRatio = imgLWL / image.width
      heightRatio = imgLHL / image.height
      ratio = Math.min(widthRatio, heightRatio)
    if (image.width >= image.height) and (image.height > imgSHL)
      heightRatio = imgSHL / image.height
      ratio = heightRatio  if heightRatio < ratio
    if (image.width <= image.height) and (image.width > imgSWL)
      widthRatio = imgSWL / image.width
      ratio = widthRatio  if widthRatio < ratio
    canvas = document.createElement("canvas")
    canvas.width = image.width * ratio
    canvas.height = image.height * ratio
    ctx = canvas.getContext("2d")
    ctx.drawImage image, 0, 0, image.width, image.height, 0, 0, canvas.width, canvas.height
    sname = splitName(file.name, file.type)
    img =
      name: file.name
      nm: sname[0]
      ext: sname[1]
      origType: file.type
      origSize: file.size
      width: canvas.width
      height: canvas.height
      ratio: ratio


    # img.type = /png/.test(file.type) ? "image/png" : "image/jpeg";
    img.type = "image/jpeg"
    img.url = canvas.toDataURL(img.type, 0.8)
    img.blob = dataURItoBlob(img.url)
    img.size = img.blob.size
    img.canvas = canvas
    canvas2 = document.createElement("canvas")
    canvas2.width = (w = Math.min(thumbW, image.width))
    canvas2.height = (h = Math.min(thumbH, image.height))
    if (image.width * h) > (image.height * w)
      xs = (image.width - (image.height / h * w)) / 2
      imw = image.width - (xs * 2)
      imh = image.height
    else
      xs = 0
      imw = image.width
      imh = image.width
    ctx2 = canvas2.getContext("2d")
    ctx2.drawImage image, xs, 0, imw, imh, 0, 0, w, h
    img.url2 = canvas2.toDataURL(img.type, 0.7)
    img.blob2 = dataURItoBlob(img.url2)
    img.size2 = img.blob2.size
    img.canvas2 = canvas2
    img
  previewPic = (input) ->
    if input.files and input.files[0]
      reader = new FileReader()
      reader.onload = (e) ->
        document.querySelector("#previewImg").src =  e.target.result
      reader.readAsDataURL input.files[0]

  # if changed and not null update formdata[fldTobeUpdated
  $scope.$watch 'date', (newValue, oldValue) ->
    if (newValue isnt undefined) and (newValue isnt null) and (newValue isnt oldValue) and (newValue isnt $scope.minDate)
      if (not $scope.rangeMode)
        $scope.formData[$scope.fldTobeUpdated+'f'] = undefined
        $scope.formData[$scope.fldTobeUpdated+'f'] = undefined
      else
        if $scope.range is 'from'
          $scope.dateFrom = new Date( newValue )
        else
          $scope.dateTo   = new Date( newValue )
        if $scope.dateFrom and $scope.dateTo
          $scope.formData[$scope.fldTobeUpdated] = undefined
    return

  $scope.$watch 'userData', ((newValue, oldValue) ->
    if newValue isnt oldValue
      $scope.dataHasChanged = not angular.equals($scope.userData, $scope.original)
      #old value is empty, set to false, ignore if haschanged
      if not (oldValue['eml'] or $scope.dataHasChanged)
        $scope.dataHasChanged = false
    return
  ), true

  $scope.$watch 'inputZipRet', (newValue, oldValue)->
    return unless newValue
    $scope.inputZipRet = newValue.toUpperCase()
    # if newValue.length > 6
    #   newValue = newValue.substr(0,6)
    if isValidZipCode newValue
      $scope.tmpData['zip'] = newValue.toUpperCase()
      $scope.fullAddr = true
      # $scope.propDataHasChanged = true
      # $scope.propForm.$dirty = true
      #user input zip, write incomplete addr to formData,
      #if return has full zip, will confuse
      # if $scope.tmpData?.city and not $scope.gotFullAddrFromSearch
      #   for i in locfld
      #     $scope.formData[i] = $scope.tmpData[i]
      # if ($scope.formData.city and not $scope.gotFullAddrFromSearch) or $scope.gotFullAddrFromSearch
      #   $scope.inputAddrRet = getFormattedAddr($scope.formData)
      if not $scope.gotFullAddrFromSearch
        $scope.searchAddrRet = getFormattedAddr($scope.tmpData)
    else
      $scope.fullAddr = false
    return

  $scope.$watch 'formData', ((newValue, oldValue) ->
    return unless newValue
    return unless $scope.formData
    if angular.equals(newValue, oldValue)
      # console.log 'only addresschange'
      return
    $scope.propDataHasChanged = true
    prop = newValue or {}
    $scope.changeSaved = false
    # $scope.prop = $scope.prop or {}
    if $scope.editingTitle#title input focused
      #only tl change, dont over write
      # console.log 'here'
      return
    unless $scope.formData?.tllck#if $scope.propForm?.tl?.$pristine
      titleString = vars.RealMaster + ' ' + (vars.ltpMap[$scope.formData.ltp] or '') + ' '#"RealMaster - "
      if $scope.formData.daddr is 'N'
        flds = ['lp', 'city', 'prov', 'zip']
      else
        flds = ['lp', 'addr', 'unt', 'city', 'prov']
      for i in flds
        if i is 'lp' and prop[i]
          titleString += $filter('currency')(prop.lp, '$', 0)
          # titleString += ", "
        else
          titleString += prop[i] or ""
        titleString += " "
      $scope.formData['tl'] = titleString
    return
  ), true

  #TODO: show loading
  unless $scope.mylistings
    $scope.loadListings vars.type, 0
  if vars.id
    $scope.editMyListing vars.id

  # create listing from ml
  # NOTE: deprecarted, user shouldnt be able create mylisting from TREB props
  if $scope.ml_num and $scope.type is 'mylisting'
    $http.post('/1.5/props/detail', {nt:1, slp:1, _id:'TRB'+$scope.ml_num}, {timeout: 5000}
    ).success( (data)->
      if data.ok
        prop = data.prop or data.detail
        $scope.picUrls = getTrebPicUrls prop.pho, prop.sid
        prop.ptp =  [prop.ptp , prop.pstyl, prop.ptp + ' ' + prop.pstyl]
        unless prop.ts or prop.ts is ""
          prop.ts = new Date()
        # update occ/psn
        psn = prop.psn
        if psn
          if /imme?d/i.test psn
            prop.psn = 'immed'
          else if /tba|tbd/i.test psn
            prop.psn = 'tba'
          else
            prop.psn = psn
        pets = prop.pets
        if pets # null/""
          if pets is 'Restrict'
            prop.pets = 1
          else if pets is 'N'
            prop.pets = 0
        else
          delete prop.pets
        # str -> int
        for i in ['tbdrms','bthrms','kch','gr','lvl','mintr', 'tax', 'mfee', 'lp']
          prop[i] = parseInt(prop[i]) or ""
        prop.rid = prop.id
        delete prop.id
        prop.m = prop.m_zh + prop.m if prop.m_zh
        $scope.formData = prop
        propStp = prop.stp?.toLowerCase() or 'lease'
        if propStp in ["rent", "lease",'sub-lease']
          $scope.formData.ltp = "mlsrent"
        else
          $scope.formData.ltp = "mlslisting"
        getTemplate($scope.formData.ltp)

        $scope.formData.status = "Active"
        $scope.formData.gr = parseInt(prop.gr) or 0
        # $scope.inputAddrRet = prop.addr + ' ' + prop.city + ' ' + prop.prov
        # $scope.inputAddr = prop.addr + ' ' + prop.city + ' ' + prop.prov
        $scope.inputSearchAddr = $scope.inputAddrRet = getFormattedAddr(prop)
        $scope.inputZipRet = if prop.zip then prop.zip.replace(' ','') else ""
        $scope.propStatus = "Active"
        # $timeout ->
        $scope.hideBackdrop = true
        $scope.showError = false
        $scope.fullAddr = true

        $scope.toggleModal 'editListingModal','open'
        $timeout (-> $scope.$apply()),10
        if vars.to #maynot have to
          #check if already exists
          $http.post('/1.5/rmprop/detail', {rmid:$scope.ml_num, ml:1}, {timeout: 5000}
          ).success( (data)->
            # already saved promote this
            if data.ok
              $scope.promote(vars.to, data.prop)
            # save as a new one and promote
            else
              # set title and expire date
              titleString = vars.RealMaster + '- ' #"RealMaster - "
              for i in ['lp', 'addr', 'unt', 'city', 'prov']
                if i is 'lp' and $scope.formData[i]
                  titleString += $filter('currency')($scope.formData[i], '$', 0)
                  # titleString += ", "
                else
                  titleString += $scope.formData[i] or ""
                titleString += " "
              $scope.formData['tl'] = titleString
              $scope.formData.exp = new Date( new Date().valueOf() + 3600000 * 24 * 90) #3 month
              $scope.submitForm true#$scope.propForm.$valid
              vars.promoteAfterSave = 1
              #load listing?
            toggleModal('editListingModal', 'close')
            $scope.hideBackdrop = true
          ).error(()->
            flashMessage "server-error"
          )
      else
        $scope.message = data.message
        if data.errorType is 'realtor'
          $scope.prop = data.prop
          $scope.toggleModal 'needRealtor','open'
        else
          $scope.toggleModal 'needLogin','open'
      return
    ).error((err)->
      RMSrv.dialogAlert 'post to detail: ' + err.toString()
      return
    )
  # document.querySelector("#imgInputFiles").onchange =  ->
  #   previewPic this

  null
app.controller 'listingCtrl', ['$scope', '$http', '$timeout', '$interval', '$filter', listingCtrl ]

app.directive 'scroll',[ () ->
  (scope, element, attrs) ->
    scrollables = document.querySelector("#realtorUtilPage > div.content")
    angular.element(scrollables).bind 'scroll', ()->
      # used height
      frameHeight = scope.height( document.querySelector("#realtorUtilPage > div.content") ) - (44 + 50)
      listHeight = scope.height(document.querySelector("#realtorUtilPage > div.content .content-list"))
      # console.log  'list: ' + listHeight
      # console.log  "scrollTop: " + @scrollTop
      scope.nextLoadPosition = listHeight - frameHeight - 10
      # console.log  'next:' + scope.nextLoadPosition
      if @scrollTop > scope.nextLoadPosition
        # console.log  'load more'
        scope.loadMore()
        # debug @pageYOffset
      else
        # scope.boolChangeClass = false
        # debug 'current: ' + @pageYOffset
        # debug scope.nextLoadPosition
      scope.$apply()
    return
  ]
RMSrv?.clearCache()

#hack googleapi js (not work, seems no api for that in china server)
# document.createElement = ((create) ->
#   ->
#     ret = create.apply(this, arguments)
#     ret.__defineSetter__ 'src', (val) ->
#       if val.indexOf('googleapis') > 0
#         delete @['src']
#         console.log val
#         valA = val.split('/')
#         valA[0] = 'http'
#         valA[2] = 'ditu.google.cn' #ditu.google.com/map.google.cn/
#         val = valA.join('/')
#         console.log '-----'
#         console.log val
#         @src = val
#       return
#     if ret.tagName.toLowerCase() == 'script'
#       #ret.setAttribute("foo", "bar");
#       console.log arguments[0]
#     ret
# )(document.createElement)
autocomplete = ""
initAutocomplete = ->

  autocomplete = new (google.maps.places.Autocomplete)(
    document.getElementById('inputAddr'),
    types: [ 'geocode' ]
    components: [country:'ca']
    componentRestrictions: {country: 'ca'}
    language:'en'
    )
  autocomplete.addListener 'place_changed', fillInAddress
  return
fillInAddress = ->
  place = autocomplete.getPlace()
  # i = 0
  # while i < place.address_components.length
  #   addressType = place.address_components[i].types[0]
  #   console.log addressType
  #   console.log place.address_components[i].short_name
  #   i++
  # console.log place
  return unless place
  pos =
    lat:place.geometry?.location?.lat() or 0
    lng:place.geometry?.location?.lng() or 0
  scope = angular.element(document.getElementById('realtorUtilPage')).scope()
  scope.inputZipRet = "" #if scope.tmpData.zip?.length < 6
  scope.fullAddr = false
  scope.gotFullAddrFromSearch = false
  setMarker(pos)
  scope._processRetAddr(place)
  scope.$apply()
  return

setMarker =  (loc1)->
  Upos = new google.maps.LatLng loc1[0],loc1[1]
  window.map?.setCenter loc1
  window.marker?.setPosition loc1


#?????????
# { "k":
#    { "label": 1,
#     "page": 0,
#     "src": "mls",
#     "saletp": "sale",
#     "city": "Toronto",
#     "prov": "Ontario",
#     "ptype": "Residential",
#     "ptype2": "[ Detached ]",
#     "no_mfee": "false",
#     "sort": "auto-ts",
#     "oh": "false",
#     "ts": "1522614785518",
#     "DDF_DISPLAY_POLICY": "loginOnly",
#     "locale": "zh-cn "},
#   "r":
#    [ { "k": "src", "vv": "MLS Listings"},
#      { "k": "saletp", "vv": "For Sale"},
#      { "k": "prov", "vv": "Ontario"},
#      { "k": "city", "vv": "Toronto"},
#      { "k": "ptype", "vv": "Residential"},
#      { "k": "ptype2", "vv": ["Array"] },
#      { "k": "sort", "vv": "Auto"} ],
#   "ts": "2018.4.2",
#   "nm": "2465",
#   "sbscb": true,
#   "rs":
#    [ { "k": "prov", "vv": "Ontario"},
#      { "k": "ptype", "vv": "Residential"},
#      { "k": "ptype2", "vv": ["Array"] },
#      { "k": "sort", "vv": "Auto"} ],
#   "d": { "city": "Toronto", "saletp": "For Sale", "src": "MLS Listings"} }
#
#