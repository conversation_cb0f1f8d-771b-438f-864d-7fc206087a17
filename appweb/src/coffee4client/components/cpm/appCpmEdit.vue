<template lang="pug">
  div#cpm-modal
    cpm-advance-setting
    //- img-select-modal
    flash-message
    img-preview-modal
    city-select-modal(:need-loc='false', :loading.sync="loading", :cur-city="{}", hide-nocity=true)
    div.bar.bar-standard.bar-footer#sasDisplay(v-show="citiesFooter")
      div.citiesWrapper
        span.city(v-for="sa in sas")
          | {{sa.n || sa.o}}
          span.icon.icon-close(@click="removeCity(sa)")
    //- header.bar.bar-nav
    //-   a.icon.fa.fa-back.pull-left(href='javascript:;', @click='back();')
    //-   span.title(v-if="cpm._id") {{_('Edit','cpm')}}
    //-   span.title(v-else) {{_('Add New','cpm')}}
    div.content
      div.name.card
        div.card-header
          span {{_('Name','cpm')}}
            span.red *
          input.pull-right(type="text", v-model="cpm.nm", :placeholder="_('Name your campaign','cpm')")
      div.location.card
        div.card-header
          span {{_('Location','cpm')}}
            span.red *
        div.card-content
          div.location-list
            div(v-for="loc in loclist")
              div.box
                div.top(v-if="loc.position=='top'")
                div.text {{loc.page}}
                div.bottom(v-if="loc.position=='bottom'")
              div.nm {{loc.nm}}
              div.desc {{loc.desc}}
              div.chk
                input(type="checkbox", :value="loc.nm", v-model="cpm.loc")
      div.card.card-content
        div.row(@click="selectSa()")
          div {{_('City/Area','cpm')}}
          div.city {{computedSas}}
          span.pull-right
            span.icon.icon-right-nav
        div.row
          div
            span {{_('Language','cpm')}}
            span.red *
          div.lang
            input#zh-cn.chk-btn(type="checkbox", value="zh-cn",  v-model="cpm.lang")
            label(for="zh-cn") {{_('ZH-CN')}}
            input#zh.chk-btn(type="checkbox", value="zh",  v-model="cpm.lang")
            label(for="zh")  {{_('ZH')}}
            input#en.chk-btn(type="checkbox", value="en",  v-model="cpm.lang")
            label(for="en")  {{_('EN')}}
            input#kr.chk-btn(type="checkbox", value="kr",  v-model="cpm.lang")
            label(for="kr")  {{_('KR')}}
        div.row
          div
            span {{_('User Location')}}
            span.red *
          div.lang
            input#other.chk-btn(type="checkbox", value="Other",  v-model="cpm.cstm_loc")
            label(for="other") {{_('Other')}}
            input#china.chk-btn(type="checkbox", value="China",  v-model="cpm.cstm_loc")
            label(for="china")  {{_('China')}}
        div.row
          div
            span {{_('User Type')}}
            span.red *
          div.lang
            input#realtor.chk-btn(type="checkbox", value="realtor",  v-model="cpm.cstm_type")
            label(for="realtor") {{_('Realtor')}}
            input#otherUser.chk-btn(type="checkbox", value="otherUser",  v-model="cpm.cstm_type")
            label(for="otherUser")  {{_('No followed')}}
            input#spUser.chk-btn(type="checkbox", value="spUser",  v-model="cpm.cstm_type")
            label(for="spUser")  {{_('Has followed')}}

        div.row
          div
            span {{_('Property Type')}}
            span.red *
          div.lang
            input#residential.chk-btn(type="checkbox", value="Residential",  v-model="cpm.ptype")
            label(for="residential") {{_('Residential')}}
            input#commercial.chk-btn(type="checkbox", value="Commercial",  v-model="cpm.ptype")
            label(for="commercial")  {{_('Commercial')}}
        div.row
          div {{_('Start Date')}}
            span.red *
          input(type="date", v-model="cpm.start_dt")
        div.row
          div {{_('End Date')}}
          input(type="date", v-model="cpm.end_dt")
          span.pull-right.icon.fa.fa-rmclose(v-if="cpm.end_dt", @click="noEndts()")
      div.card.card-content
        div.row(@click="openAdvance()")
          span {{_('Advance Setting','cpm')}}
          //- span {{cpm.time_weight}}
          span.pull-right
            span.icon.icon-right-nav
      div.card.card-content
        div.row
          div {{_('CPM (Cost Per Thousand Impression)','cpm')}}
          div.pull-right.disabled ${{dispVar.cpm}}
        div.row
          div {{_('Discount','cpm')}}
          select(v-model="cpm.discount")
            option(value="1") {{_('No Discount','cpm')}}
            option(v-for="discount in discounts", :value="discount") {{Math.round(100-discount*100)}}% OFF
        div.row(v-if="dispVar.isCpmAdmin")
          div {{_('per vc cost','cpm')}}
          div.pull-right.disabled  {{dispVar.cpm*cpm.discount/1000}}
        div.row
          div {{_('Current Ad Balance','cpm')}}
          div.pull-right.disabled  {{computedLeftViews}}k imp: ${{cpm.balance.toFixed(4)}}
        div.row(v-if="dispVar.isCpmAdmin && cpm.todayVc")
          div {{_('today vc','cpm')}}
          div.pull-right.disabled(v-if="cpm.todayVc")  {{cpm.todayVc}}
        div.row(v-if="dispVar.isCpmAdmin && cpm.daily_bdgt_left")
          div {{_('Current Daily Budget left','cpm')}}
          div.pull-right.disabled {{cpm.daily_bdgt_left.toFixed(4)}}
        div.row(v-if="dispVar.isCpmAdmin && cpm.hourly_bdgt_left")
          div {{_('Current Hourly Budget left','cpm')}}
          div.pull-right.disabled {{cpm.hourly_bdgt_left.toFixed(4)}}
        div.row
          div {{_('Payment Amount','cpm')}}
          input(type="number", v-model="payAmount")
          div.btn.btn-positive(@click="addBalance()") Add
        div.row
          div {{_("Account Balance:", 'cpm')}}
          div.disabled {{totalBalance}}
        div.row
          div {{_('Max Daily Budget','cpm')}}($)
            span.red *
          input.pull-right(type="Number", v-model="cpm.daily_bdgt")
        div.row
          span {{_('Estimate End Date')}}
          span.pull-right.disabled {{computedExpdata}}
      div.card.ad-style
        div.card-header
          | {{_('AD Style','cpm')}}
        div.card-content
          div.row
            div
              div {{_('Style-Big Banner','cpm')}}
              div 480*300
            div.cpm-photo
              img.image(v-if="cpm.big", :src="cpm.big" , @click="previewPic(cpm.big,'photo')")
              span.image.new-img(v-else,@click="showImgSelectModal('big');")
                div.icon.icon-plus
          div.row
            div
              div {{_('Style-Std Banner','cpm')}}
              div 600*180
            div.cpm-photo
              img.image(v-if="cpm.std", :src="cpm.std" , @click="previewPic(cpm.std,'photo')")
              span.image.new-img(v-else,@click="showImgSelectModal('std');")
                div.icon.icon-plus
          div.row
            div
              div {{_('Map-Std Banner','cpm')}}
              div 600*90
            div.cpm-photo
              img.image(v-if="cpm.mapStd", :src="cpm.mapStd" , @click="previewPic(cpm.mapStd,'photo')")
              span.image.new-img(v-else,@click="showImgSelectModal('mapStd');")
                div.icon.icon-plus
          div.row
            div
              div {{_('Style-Card','cpm')}}
              div 64*64
            div.cpm-photo
              img.image(v-if="cpm.card", :src="cpm.card" , @click="previewPic(cpm.card,'photo')")
              span.image.new-img(v-else,@click="showImgSelectModal('card');")
                div.icon.icon-plus
          div.row
            div {{_('Title - CN')}}
            input.pull-right(type="text", v-model="cpm.tl_zh")
          div.row
            div {{_('Title - EN')}}
            input.pull-right(type="text", v-model="cpm.tl_en")
          div.row
            div {{_('Slogan - CN')}}
            input.pull-right(type="text", v-model="cpm.slogan_zh")
          div.row
            div {{_('Slogan - EN')}}
            input.pull-right(type="text", v-model="cpm.slogan_en")
          div.row
            div {{_('Button Label - CN')}}
            input.pull-right(type="text", v-model="cpm.label_zh")
          div.row
            div {{_('Button Label - EN')}}
            input.pull-right(type="text", v-model="cpm.label_en")
          div.row
            div {{_('Click Note')}}
            input.pull-right(type="text", v-model="cpm.note")
          div.row
            div
              span {{_('Click Action')}}
              span.red *
            div
              input#url.chk-btn(type="radio", value="url",  v-model="cpm.action")
              label(for="url") {{_('URL')}}
              input#eml.chk-btn(type="radio", value="eml",  v-model="cpm.action")
              label(for="eml")  {{_('Email')}}
              input#tel.chk-btn(type="radio", value="tel",  v-model="cpm.action")
              label(for="tel")  {{_('Tel')}}
          div.row(v-if="cpm.action=='tel'")
            div {{_('Tel')}}
            input.pull-right(type="text", v-model="cpm.mbl")
          div.row(v-if="cpm.action=='eml'")
            div {{_('Email')}}
            input.pull-right(type="text", v-model="cpm.ad_eml")
          div.row.url(v-if="cpm.action=='url'")
            div {{_('Url')}}
            input.pull-right(type="text", v-model="cpm.url", placeholder="eg:https://www.realmaster.ca")
          div.row.padding
    div.bar.bar-standard.bar-footer.row.save
      span.btn.btn-positive.pull-left(@click="save()", :class="[cpm._id ? '' : 'btn-long full-width']")
        span(v-if="!cpm._id") {{_('Save')}}
        span(v-else) {{_('Update')}}
      span.btn.btn-positive(v-if="cpm._id && cpm.status=='D'", @click="publish()") {{_('Publish')}}
      span.btn.btn-positive(v-else-if="cpm._id && cpm.status=='U'", @click="cancelOrPause('resume')") {{_('Resume')}}
      span.btn.btn-positive(v-else-if="cpm._id && cpm.status=='A'", @click="cancelOrPause('pause')") {{_('Pause')}}
      span.btn.btn-positive(v-if="cpm._id" @click="cancelOrPause('terminate')") {{_('Terminate')}}
      div.icon.icon-trash(v-if="cpm._id && dispVar.isCpmAdmin", @click="cancelOrPause('delete')")
    div(style="display:none")
        span(v-for="(v,k) of strings") {{_(v.key, v.ctx)}}
</template>

<script>
import PageSpinner from '../frac/PageSpinner.vue'
// import ImgSelectModal from '../frac/ImgSelectModal.vue'
import ImgPreviewModal from '../frac/ImgPreviewModal.vue'
import CitySelectModal from '../frac/CitySelectModal.vue'
import FlashMessage from '../frac/FlashMessage.vue'
import pagedata_mixins from '../pagedata_mixins'
import cpmAdvanceSetting from './cpmAdvanceSetting.vue'

export default {
  mixins:[pagedata_mixins],
  components:{
    PageSpinner,
    ImgPreviewModal,
    FlashMessage,
    CitySelectModal,
    cpmAdvanceSetting
  },

  data () {
    return {
      datas:[
        'isCpmAdmin',
        'cpm',
        'languageList'
      ],
      dispVar :{
        isCpmAdmin: false,
        cpm:10,
        languageList:[]
      },
      cpm:{
        nm:'',
        loc:[],
        sas:[],
        lang:[],
        cstm_loc:['China','Other'],
        ptype:['Residential','Commercial'],
        cstm_type: ['realtor','spUser','otherUser'],
        start_dt: '',
        end_dt: '',
        discount:1,
        balance:0,
        daily_bdgt:10,
        big:'',
        std:'',
        mapStd:'',
        card:'',
        slogan: '',
        label_en:'',
        label_zh:'',
        note:'',
        action:'eml',
        mbl:'',
        url:'',
        status:'D',
        time_weight:''
      },
      curCity:{o:'Toronto',p:'Ontario'},
      payType: 'Cash',
      loclist:[{nm:'MP1',desc:'property map preview',position:'top', page:'map'},
      {nm:'NB1',desc:'forum detail top',position:'top', page:'forum'},
      {nm:'NB2',desc:'forum detail bottom',position:'bottom', page:'forum'}],
      currentPic:null,
      strings:{
        ERR_ALREADY_SELECTED:{key:'Already added',ctx:''},
        ERR_TOO_MANY_SAS:{key:'Too Many Cities',ctx:''},
        deleteConfirm:{key:"Are you sure to delete current ad?", ctx:'cpm'},
        terminateConfirm:{key:"Are you sure to terminate current ad?", ctx:'cpm'},
        pauseConfirm:{key:"Are you sure to pause current ad?", ctx:'cpm'},
        resumeConfirm:{key:"Are you sure to resume current ad?", ctx:'cpm'},
        cancel:{key:"Cancel", ctx:''},
        confirm:{key:"Confirm", ctx:''},
        message:{key:"Confirm", ctx:''}

      },
      citiesFooter: false,
      loading:false,
      sas:[],
      discounts:[0.95,0.9,0.85,0.8,0.75,0.7,0.65,0.6,0.55,0.5,0.45,0.4,0.35,0.3,0.25,0.2,0.15,0.1,0.05],
      payAmount:0,
      totalBalance:vars.bal,
      oldCpm:{}
    }
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var self = this;
    self.getPageData(self.datas, {}, true);
    window.bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      self.cpm.lang = d.languageList
    });
    if (vars.id) {
      this.getCpm();
    } else {
      this.cpm.ad_eml = this.cpm.eml;
      this.cpm.uid = vars.uid;
      this.cpm.eml = vars.eml||'';
      this.cpm.mbl = vars.mbl||'';
    }
    if (vars.len) {
      this.cpm.nm = 'AD '+vars.len
    } else {
      this.cpm.nm = 'AD 1'
    }
    bus.$on('set-city', function (d) {
      var city = d.city;
      // console.log(d.city);
      if (self.canAddCityToSa(city)) {
        self.sas.push(city);
        self.citiesFooter = true;
      } else {
        if (self.sas.length >= 50) {
          return window.bus.$emit('flash-message', self.$parent._(self.strings.ERR_TOO_MANY_SAS.key));
        }
        window.bus.$emit('flash-message', self.$parent._(self.strings.ERR_ALREADY_SELECTED.key));
      }
    });
    bus.$on('city-select-close', function (d) {
      self.citiesFooter = false;
    });
    // bus.$on('select-img', function (data) {
    //   var picUrls = data.picUrls;
    //   toggleModal("imgSelectModal");
    //   self.cpm[self.selectPhotoType] = picUrls[0];
    // });
    bus.$on("set-time-weight", function(data) {
      // console.log(data);
      self.cpm.time_weight = data.join('');
    });
  },
  computed:{
    computedLeftViews:function() {
      return Math.round(this.cpm.balance*100/(this.dispVar.cpm*this.cpm.discount))/100
    },
    computedExpdata:function() {
      if (this.cpm.daily_bdgt>0 && this.cpm.balance>0) {
        let days = this.cpm.balance/this.cpm.daily_bdgt
        let startts = new Date().getTime()
        if (this.cpm.start_dt) {
          startts = new Date(this.cpm.start_dt).getTime()
        }
        let ms = startts + 3600 * 24 * days * 1000;
        let date =  new Date(ms);
        return date.getFullYear() + '-' + (date.getMonth()+1) + '-' + date.getDate();
      } else {
        return '';
      }
    },
    computedSas:function () {
      var ret = [];
      for (var sa of this.sas) {
        ret.push(sa.n || sa.o);
      }
      return ret.join(',');
    }
  },
  methods: {
    openAdvance() {
      toggleModal('cpmAdvanceSetting');
      window.bus.$emit('init-time-weight', this.cpm.time_weight);
    },
    noEndts() {
      this.cpm.end_dt = '';
    },
    // todo, deleted confirm
    cancelOrPause(action) {
      let self = this;
      self.loading = true;
      if (action =='resume') {
        if(!this.validCheck()) {
          return;
        }
      }
      let _do = function(idx) {
        // alert(idx)
        if (idx+'' != '2') {
          return;
        }
        let data = {
          _id: self.cpm._id,
          action: action
        };

        self.$http.post('/1.5/cpm/changeStatus', data).then(
          function (ret) {
            ret = ret.body;
            self.loading = false;
            if (ret.ok) {
              if (action =='resume') {
                self.cpm.status = 'A';
              } else if (/pause/.test(action)) {
                self.cpm.status = 'U';
                if (action == 'cancel') {
                  self.cpm.balance = 0;
                }
              } else if (/delete|terminate/.test(action)) {
                self.cpm.status = 'U';
                self.cpm.balance = 0;
              }
            } else {
              // console.log(ret.e);
              self.processPostError(ret)
              // RMSrv.dialogAlert(ret.err || ret.e);
            }
          },
          function (ret) {
            ajaxError(ret);
          }
        );
      };

      let msg = self._(self.strings[action+'Confirm'].key,'cpm');
      return RMSrv.dialogConfirm(msg,
        _do,
        self._(self.strings.message.key,self.strings.message.ctx),
        [self._(self.strings.cancel.key,self.strings.cancel.ctx), self._(self.strings.confirm.key,self.strings.confirm.ctx)]
      );
    },
    validCheck() {
      var self = this;
      if (!self.cpm.nm) {
        RMSrv.dialogAlert('Please specify a name for your ad');
        return false;
      }
      if (!self.cpm.eml) {
        RMSrv.dialogAlert('No user email entered');
        return false;
      }
      if (!self.cpm.loc.length) {
        RMSrv.dialogAlert(this._('Please select at least one ads location','cpm'));
        return false;
      }
      if (self.cpm.loc.indexOf('MP1')>=0 && !self.cpm.ptype.length) {
        RMSrv.dialogAlert(this._('Please select at least one property type','cpm'));
        return false;
      }
      if (!self.cpm.lang.length) {
        RMSrv.dialogAlert(this._('Please select at least one Language','cpm'));
        return false;
      }
      if (!self.cpm.cstm_loc.length) {
        RMSrv.dialogAlert(this._('Please select at least one User location','cpm'));
        return false;
      }
      if (!self.cpm.cstm_type.length) {
        RMSrv.dialogAlert(this._('Please select at least one User Type','cpm'));
        return false;
      }
      if (!(self.cpm.start_dt)) {
        return RMSrv.dialogAlert(this._('Please select start date'));
      }

      if (!self.cpm.daily_bdgt || self.cpm.daily_bdgt<=0) {
        RMSrv.dialogAlert(this._('Please fill in max daily Budget','cpm'));
        return false;
      }
      if (!self.cpm.big && !self.cpm.std && !self.cpm.card && !self.cpm.mapStd ) {
        RMSrv.dialogAlert(this._('Please upload at least one image','cpm'));
        return false;
      }
      if (self.cpm.loc.indexOf('MP1')>=0 && (!self.cpm.card && !self.cpm.mapStd)) {
        RMSrv.dialogAlert(this._('Please upload card or map standard image for map','cpm'));
        return false;
      }
      if (self.cpm.card && (!self.cpm.label_zh ||!self.cpm.label_en )) {
        RMSrv.dialogAlert(this._('Please fill in button label','cpm'));
        return false;
      }
      if (!self.cpm.action) {
        RMSrv.dialogAlert(this._('Please fill in click action','cpm'));
        return false;
      }
      if (self.cpm.url && !/^(http|https)/.test(self.cpm.url)) {
        RMSrv.dialogAlert(this._('Please enter valid url, eg:https://www.realmaster.ca','cpm'));
        return false;
      }
      if (!self.cpm.mbl && !self.cpm.url && !self.cpm.ad_eml ) {
        RMSrv.dialogAlert(this._('Please fill in click action','cpm'));
        return false;
      }
      return true;
    },

    addBalance() {
      var self = this;
      if (this.payAmount<=0) {
        RMSrv.dialogAlert("Purchase amount has to > 0, eg. 600");
        return;
      }
      if (!this.cpm._id) {
        this.save(true, function(err) {
          if (!err) {
            self.payAndAddBalance();
          }
        });
      } else {
        self.payAndAddBalance();
      }
    },
    payAndAddBalance(){
      var self = this;
      self.loading = true;
      var d = {
        eml:self.cpm.eml,
        uid: self.cpm.uid,
        amount:-(this.payAmount),
        act:'purchase',
        start_dt:self.cpm.start_dt,
        cpmid:this.cpm._id,
        cpmnm:this.cpm.nm
      };
      if (self.cpm.end_dt) {
        d.end_dt = self.cpm.end_dt
      }
      self.$http.post('/1.5/cpm/addCpmBalance', d).then(
        function (ret) {
          ret = ret.body;
          self.loading = false;
          // console.log(ret);
          if (ret.ok) {
            // self.searchUser();
            self.cpm.balance = self.cpm.balance + parseInt(self.payAmount);
            self.totalBalance = self.totalBalance - parseInt(self.payAmount);
            var msg = ret.msg || 'Added';
            self.payAmount = 0;
            RMSrv.dialogAlert(msg);
          } else {
            // console.log(ret.e);
            self.processPostError(ret);
            // RMSrv.dialogAlert(ret.err || ret.e);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    setLang(lang) {
      this.cpm.lang
    },
    formatSas(){
      var ret = [];
      for (let sa of this.sas) {
        ret.push({city:sa.o, prov:sa.p});
      }
      return ret;
    },
    canAddCityToSa(c){
      if (this.sas.length >= 50) {
        return false;
      }
      for (var sa of this.sas) {
        if (sa.o == c.o) {
          return false;
        }
      }
      return true;
    },
    selectSa(){
      this.citiesFooter = true;
      window.bus.$emit('select-city', {noloading:true});
    },
    removeCity(sa){
      var idx = -1;
      for (var i = 0; i < this.sas.length; i++) {
        if (this.sas[i].o == sa.o) {
          idx = i;
          break;
        }
      }
      this.sas.splice(idx,1);
    },
    deletePhoto(photo) {
      if(this.cpm.big == photo) {
        this.cpm.big = ''
      } else if (this.cpm.std == photo) {
        this.cpm.std = ''
      } else if (this.cpm.card == photo) {
        this.cpm.card = ''
      } else if (this.cpm.mapStd == photo) {
        this.cpm.mapStd = ''
      }
    },
    previewPic(src,type) {
      this.selectPhotoType = type;
      window.bus.$emit('img-preview', src);
    },
    showImgSelectModal(type) {
      var self = this;
      self.selectPhotoType = type;
      // toggleModal('imgSelectModal');
      var opt = {
      url :'/1.5/img/insert'
      }
      insertImage(opt,(val)=>{
        // console.log(val)
          //   val = '{"picUrls":["http://file.test:8091/img/G/BWMB/BHA.png","http://file.test:8091/img/G/BWMB/BHB.png"]}
        if (val == ':cancel') {
            console.log('canceled');
            return;
        }
        try {
          // alert(ret)
          var ret = JSON.parse(val);
          var picUrls = ret.picUrls;
          self.cpm[self.selectPhotoType] = picUrls[0];
        } catch (e) {
          console.error(e);
        }
      })
    },
    setAction(action) {
      this.cpm.action = action;
    },
    getUserBalance(eml) {
      var self = this;
      var d = {
        eml:eml,
        act:'check'
      };
      self.$http.post('/sys/user', d).then(
        function (ret) {
          ret = ret.body;
          if (ret.id) {
            self.totalBalance = ret.balance || 0;
          } else {
            self.processPostError(ret);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    getCpm() {
      var self = this;
      self.loading = true;
      self.$http.post('/1.5/cpm/one', {id:vars.id}).then(
        function (ret) {
          ret = ret.body;
          self.loading = false;
          if (ret.ok) {
            self.cpm = Object.assign(self.cpm, ret.cpm);
            self.oldCpm = Object.assign({}, ret.cpm);
            let clientNm = vars.clientNm||self.cpm.clientNm
            if (clientNm) {
              self.cpm.clientNm = clientNm;
            }
            for(let i of self.cpm.sas) {
              i.n = self._(i.city)
              i.o = i.city
              i.p = i.prov
              self.sas.push(i);
            }
            self.cpm.ad_eml = self.cpm.ad_eml || vars.eml||'';
            self.cpm.mbl = self.cpm.mbl||vars.mbl || '';
            if(!vars.bal) {
              self.getUserBalance(self.cpm.eml);
            }
          } else {
            self.processPostError(ret);
            // console.log(ret.err);
            // RMSrv.dialogAlert(ret.err || ret.e);
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    },
    back() {
      window.history.back();
    },
    publish() {
      let self = this;
      if(!self.cpm._id) {
        return;
      }
      if(!this.validCheck()) {
        return;
      }
      let data = {
        eml: self.cpm.eml,
        _id: self.cpm._id,
        action: 'publish'
      };
      self.loading = true;
      this.save(true, function(err) {
        if (err) {
          return;
        }
        self.$http.post('/1.5/cpm/changeStatus', data).then(
          function(ret) {
            self.loading = false;
            ret = ret.body
            if (ret.ok) {
              self.cpm.status = 'A';
            } else if (ret.err || ret.e){
              self.processPostError(ret);
              // RMSrv.dialogAlert(ret.err || ret.e);
            }
          },
          function (err) {
            self.loading = false;
            ajaxError(err);
          }
        )
      });
    },
    save(auto,cb) {
      if(this.cpm.status!='D' && !this.validCheck()) {
        if (cb) {
          return cb('valid check failed');
        }
        return;
      }
      var self = this;
      self.loading = true;
      self.cpm.sas = self.formatSas(self.sas)
      let data = Object.assign({}, self.cpm)
      if (self.oldCpm.discount !== self.cpm.discount) {
        data.oldDiscount =  self.oldCpm.discount;
      }
      if (self.oldCpm.daily_bdgt !== self.cpm.daily_bdgt) {
        data.oldDailyBdgt =  self.oldCpm.daily_bdgt;
      }
      self.$http.post('/1.5/cpm/edit', data).then(
        function (ret) {
          ret = ret.body;
          self.loading = false;
          if (ret.ok) {
            self.cpm._id = ret._id;
            self.oldCpm.discount = self.cpm.discount;
            self.oldCpm.daily_bdgt = self.cpm.daily_bdgt
            if(!auto) {
              RMSrv.dialogAlert('saved');
            }
            if (cb) {
              return cb(null, self.cpm._id);
            }
          } else {
            // console.log(ret.err);
            self.processPostError(ret);
            if (cb) {
              return cb(ret.e|| ret.err);
            }
            // RMSrv.dialogAlert(ret.e || ret.err);
          }
        },
        function (ret) {
          self.loading = false;
          if (cb) {
            return cb(ret);
          }
          ajaxError(ret);
        }
      );
    },
  }
}
</script>
<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.row.padding{
  height: 400px;
}
.red {
  color: #fa5255;
}
 .icon-trash, .fa-rmclose{
  color: #fa5255;
  font-size: 20px;
}
.noendts {
  width: 100px!important;
  float: right;
  margin-left: auto;
}
.row input {
  text-align: right;
}
.full-width {
  width: 100%!important;
}
.ad-style .image {
  background-size: 100% 100%;
  display: inline-block;
  width: 50%;
  margin: 1%;
  padding: 5px;
  height: 70px;
  vertical-align: top;
}
.ad-style .new-img div {
  height: 100%;
  border: 3px dotted #ddd;
  width:100%
}
.cpm-photo {
  width: 50%;
}
.ad-style .new-img .icon{
  font-size: 20px;
  font-style: normal;
  text-align: center;
  margin-left: -9px;
  padding-top: 18px;
}
.bar-footer.save{
  display: flex;
  justify-content: space-between;
  color: white;
  align-items: center;
  background: #f1f1f1;
}
.bar-footer.save span {
  top: 0px;
  width: 30%;
  font-size: 15px;
}
.location-list .top, .location-list .bottom {
  background-color: #fa5255;
  height: 15px;
  width: 100%;
  position: absolute;
}
.location-list .top {
  top: 0px;
}
.location-list .bottom {
  bottom: 0px;
}
.content{
  background: #f1f1f1;
}
.content{
  background: #f1f1f1;
}
.content .card{
  border: none;
  margin: 0;
}
.card .card-header{
  padding: 15px 10px;
  font-weight: bold;
  font-size: 16px;
  border-bottom: 1px solid #f1f1f1;
  align-items: center;
  display: flex;
  justify-content: space-between;
}
.card .card-header .pull-right{
  font-size: 17px;
  font-weight: normal;
}
.card .card-header .pull-right .icon{
  font-size: 13px;
}
.content > div{
  background: white;
}
.content > div.card:not(:first-of-type) {
  margin-top: 15px;
}
.card-content{
  color: #666;
}
.card-content .row{
  display: flex;
  border-bottom: 1px solid #f1f1f1;
  padding: 10px 10px;
  justify-content: space-between;
  align-items: center;
}

.card-content .row.flex-end{
  justify-content: flex-end;
}
.card-content .row select,
.card-content .row input{
  width: 60%;
  margin-bottom: 0;
  border: none;
  height: 29px;
  padding-right: 0px;
}

.card-content .row.url input {
  width: calc(100% - 50px);
}
.card-content .row ::placeholder {
  color: #ddd;
}
.content .btn{
  border-radius: 0;
  height: 30px;
  font-size: 14px;
  width: 50px;
  padding-top: 6px;
  margin-left: 10px;
}
.card-header input{
  margin-bottom: 0;
  display: inline-block;
  height: 30px;
  width: 60%;
  border: none;
}
input[type="checkbox"]{
  width: auto;
}
 .location-list {
   display: flex;
   width: 100%;
   overflow-x: scroll;
 }
.location-list > div {
  width: 100%;
  display: inline-block;
  vertical-align: top;
  overflow: hidden;
  align-items: stretch;
  position: relative;
  padding: 15px;
  /* box-shadow: 1px 1px 8px 1px #f1f1f1; */
  position: relative;
  background: rgba(255,255,255,0.7);
  text-align: center;
}
.location-list .nm {
  font-size: 14px;
  padding-top: 5px;
}
.location-list .desc  {
  font-size: 12px;
  height: 44px;
  display: table-cell;
}
.location-list .chk {
  padding:5px;
}
.location-list .box {
  border: 1px solid #ddd;
  height: 150px;
  position: relative;
  background: #F2F2F2
}
.location-list .box .text {
  height: 100%;
  text-align: center;
  line-height: 150px;
}
#sasDisplay{
  z-index: 21;
  padding-right: 0;
  background-color: white;
  height: 44px;
}
#sasDisplay .citiesWrapper{
  width: auto;
  white-space: nowrap;
  overflow-x: auto;
  padding: 11px 0 0 0;
  height: 100%;
}
#sasDisplay .city{
  background: #428bca;/*#5cb85c;*/
  border-radius: 19px;
  color: white;
  padding: 3px 8px 3px 7px;
  font-size: 15px;
  vertical-align: top;
}
#sasDisplay .city .icon.icon-close{
  vertical-align: top;
  font-size: 19px;
  padding: 1px 0 0 3px;
}
#sasDisplay .city:not(:first-child){
  margin-left: 13px;
}
.lang span, .action span {
  background: #F2F2F2;
  margin: 10px;
  border-radius: 19px;
  color: white;
  padding: 3px 8px 3px 7px;
  font-size: 15px;
  vertical-align: top;
}
.lang span .active, .action span .active {
  background: #fa5255;
}
input.chk-btn {
  display: none;
}
input.chk-btn + label {
  background: #b3b3b3;
  margin: 0px 10px;
  border-radius: 17px;
  color: white;
  padding: 2px 10px 2px 9px;
  font-size: 14px;
  vertical-align: top;
  font-weight: 400;
}
input.chk-btn:not(:checked) + label:hover {
  box-shadow: 0px 1px 3px;
}
input.chk-btn + label:active,
input.chk-btn:checked + label {
  box-shadow: 0px 0px 3px inset;
  background: #fa5255;
}
.disabled {
  color: #b3b3b3;
}
</style>
