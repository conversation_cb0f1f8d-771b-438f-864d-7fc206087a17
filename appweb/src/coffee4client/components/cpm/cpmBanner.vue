<template lang="pug">
div
  div.clear-mask.mask(v-show="showMask", @click="closeAll()")
  div(style="position:relative", v-if="isValidCpm")
    a.big(v-if="cpm.big",  :href="computedHref", @click="openAd()")
      img(:src="cpm.big")
    a.std(v-if="cpm.std||cpm.mapStd", :href="computedHref", @click="openAd()")
      img(:src="computedStdSrc", :style="{'height':computedStdHeight}")
    div.cpm-card(v-if="cpm.card", :class="{bigcard: loc!='MP1'}")
      div.img(:style="{'background-image':'url('+cpm.card+')', 'height':computedImageHeight,'width':computedImageHeight}")
      div.card-right(:style="{'width':computedCardRightWidth}")
        div.slogan
          div.tl.trim {{cpm.tl}}
          div.trim
            span.ad {{_('AD')}}
            span.desc.trim {{cpm.slogan}}
        div(style="padding-right: 20px; float:right;")
          a.btn.btn-positive(v-if="cpm.action=='tel'", :href="computedHref", @click="openAd()") {{cpm.label}}
          a.btn.btn-positive(v-if="cpm.action=='eml'", :href="computedHref", @click="openAd()") {{cpm.label}}
          a.btn.btn-positive(v-if="cpm.action=='url'", @click="openAd()") {{cpm.label}}
    div.more(@click.stop.prevent="clickMore()",:class="{top: loc!='MP1' && !cpm.card}")
      div.icon-wrapper
        i.fa.fa-ellipsis-h(v-if="loc!='MP1' && !cpm.card")
        i.fa.fa-ellipsis-v(v-else)
    div.show-help(v-if="showHelp", :class="{top: loc!='MP1' || isWebView}", @click.stop.prevent="openAdService()")
      img(src='/img/cpm-check.png')
      span {{_('Your AD Here','cpm')}}
</template>
<script>
import cpm_mixin from './cpm_mixin'

export default {
  mixins:[cpm_mixin],
  props: {
    currentLoc:{
      type: String,
      default: function () {
        return ''
      }
    }
  },
  data () {
    return {
      showHelp:false,
      showMask:false,
      cpm: {
        type: Object,
        default: function() {
          return {
            std:'',
            card:'',
            big:'',
            note:'',
            label_en:'Email',
            label_zh:'邮件',
            action:'',
            mbl:'',
            url:'',
            tl:'',
            slogan:'',
          }
        }
      },
      loc: '',
      city: '',
      prov: '',
      ptype: '',
      isWebView: false,
    }
  },
  mounted() {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    let self = this;
    // self.getCurrentCpm();
    if (vars.dispVar && vars.dispVar.userCity) {
      self.city =  vars.dispVar.userCity.o;
    }
    if (vars.dispVar && vars.dispVar.userCity) {
      self.prov= vars.dispVar.userCity.p;
    }
    self.loc = self.currentLoc || vars.loc;

    window.bus.$on('get-current-ad',function (params) {
      if (params.isWebView) {
        self.isWebView = true;
      }
      if (!self.loc) {
        self.loc = params.loc;
      }
      if (params) {
        self.prov = params.prov;
        self.city = params.city;
        self.ptype = params.ptype;
      }
      if (self.loc == params.loc) {
        self.getCurrentCpm();
      }
    });

    window.bus.$emit('close-mask',function() {
      self.closeAll();
    });
  },
  computed:{
    computedStdSrc() {
      if (this.loc == 'MP1') {
        return this.cpm.mapStd || this.cpm.std;
      } else {
        return this.cpm.std;
      }
    },
    computedCardRightWidth() {
      return 'calc(100% - '+this.computedImageHeight +')';
    },
    computedStdHeight() {
      if (this.loc=='MP1') {
        return '65px';
      } else {
        return '100%';
      }
    },
    computedImageHeight() {
      return '45px'
      // if (this.loc=='MP1') {
      //   return '45px';
      // } else {
      //   return '55px';
      // }
    },
    computedHref() {
      if (this.cpm.action=='eml') {
        return 'mailto:'+this.cpm.ad_eml ;
      } else if (this.cpm.action=='tel') {
        return 'tel:'+this.cpm.mbl ;
      } else {
        return "javascript:void(0);";
      }
    },
    isValidCpm() {
      return (this.cpm.big || this.cpm.std || this.cpm.card || this.cpm.mapStd)
    },

  },
  methods: {
    closeAll() {
      this.showMask = false;
      this.showHelp = false;
    },
    clickMore() {
      this.showHelp= !this.showHelp;
      this.showMask = !this.showMask;
      if (this.showMask) {
        window.bus.$emit('show-mask');
      }
    },
    openAdService() {
      this.closeAll();
      this.openService();
    },
    eventHandler(event) {
      if (!event.target.matches('.more') && !event.target.matches('.fa-ellipsis-v')) {
        this.showHelp = false;
      }
    },
    openAd(opt={}) {
      this.showHelp = false;
      this.$http.post('/1.5/cpm/click',{id: opt._id||this.cpm._id, loc:opt.loc||this.loc, city: opt.city||this.city, prov: opt.prov||this.prov, ptype: opt.ptype||this.ptype}).then(
        function(ret) {
          if (ret.data.ok) {
            if (opt.action=='url' || this.cpm.action=='url') {
              RMSrv.showInBrowser(opt.url || this.cpm.url);
            }
          }
        },
        function(err) {
          ajaxError(err);
        });
    },

    getCurrentCpm () {
      this.showHelp = false;
      let self = this;
      // let city = this.computedCity;
      // let prov = this.computedProv;
      // let loc = this.loc || vars.loc;
      let d = {
        loc: this.loc,
        city: this.city,
        prov: this.prov,
        ptype: this.ptype
      }
      if (!d.loc) {
        return;
      }
      self.$http.post('/1.5/cpm/currentAD', d).then(
        function (ret) {
          ret = ret.body;
          if (ret.cpm) {
            self.cpm = Object.assign({},ret.cpm) ;
            if(!self.cpm.label && self.cpm.ad_eml) {
              self.cpm.lable = self._('Email');
            }
            if (this.loc=='MP1' && !this.isWebView) {
              if (self.isValidCpm) {
                document.getElementById('propHalfDetail').style.top='calc(100% - 235px)'
              } else {
                document.getElementById('propHalfDetail').style.top='calc(100% - 170px)'
              }
            }
          }
        },
        function (ret) {
          ajaxError(ret);
        }
      );
    }

  }
}
</script>
<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>


</style>
