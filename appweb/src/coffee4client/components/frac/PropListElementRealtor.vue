<template lang="pug">
div.realtor(@click.stop.prevent="realtorClicked(adrltr)")
  div
    img.avt(src="/img/logo.png", :src="avtSrc", onerror="this.src='/img/logo.png';return true;")
    img.vip(src='/img/vip.png', v-show="adrltr.vip")
  span.nm(v-show="dispVar.lang == 'en'") {{adrltr.nm_en || adrltr.nm}}
  span.nm(v-show="dispVar.lang !== 'en'") {{adrltr.nm_zh || adrltr.nm}}
  //- span.nm(v-if="adrltr.fn || adrltr.ln") {{adrltr.fn}} {{adrltr.ln}}
  //- span.nm(v-show="adrltr.nm") {{adrltr.nm}}
</template>

<script>
export default {
  props: {
    dispVar:{
      type:Object,
      default:function () {
        return {fav:false};
      }
    },
    prop: {
      type: Object,
      default: function () {
        return {fav:false}
      }
    },
    adrltr: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  computed:{
    avtSrc:function () {
      if (this.adrltr.avt) {
        return this.adrltr.avt;
      }
      return '/img/logo.png';
    },
  },
  data () {
    return {
    };
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
  },
  methods: {
    realtorClicked () {
      window.bus.$emit('realtor-clicked', this.adrltr);
    },
  }
}
</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.realtor > div{
  text-align: center;
}
.realtor{
  /*text-align: right;*/
  margin-top: -37px;
  float: right;
  /* margin-right: 15px; */
}
.realtor img{
  border-radius: 50%;
}
.realtor .avt{
  width: 70px;
  height: 70px;
  /*position: relative;*/
  border: 1px solid #fff;
}
.realtor .vip{
  width: 18px;
  height: 18px;
  margin-left: -16px;
}
.realtor .nm{
  white-space: nowrap;
  overflow: hidden;
  width: 90px;
  display: inline-block;
  text-align: center;
  font-size: 13px;
  color: #428bca;
  text-overflow: ellipsis;
}
</style>
