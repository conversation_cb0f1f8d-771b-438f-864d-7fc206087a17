<template lang="pug">
div
  div.prop.rm(@click="propClicked()", data-sub="detail", data-act="open", :data-id='prop._id')
    div.img(:style="{ 'height':rcmdHeight+'px'}",
    :class="{blur:prop.login}")
      img(:src="computedBgImg"
        style="background-image: url('/img/noPic.png');background-size: 100% 100%;width:100%;"
        :style="{'height':rcmdHeight+'px'}"
        @error="e => { e.target.src = '/img/noPic.png'}"
        referrerpolicy="same-origin")
      div.on-img-top
        span.top.pull-left(v-if="isTop") {{_('TOP')}}
        span.tp(v-show='prop.type')
          | {{prop.type}}
        span.pull-right.fav.fa(:class="{'fa-heart-o':!prop.fav, 'fa-heart':prop.fav}",
          @click.stop.prevent="toggleFav()", v-show="!prop.login && dispVar.isApp && showFav && !prop.isProj")
    div.price(:class="{blur:prop.login}")
      | {{prop.priceValStrRed || prop.askingPriceStr}}
      span.stp(v-show="prop.saletp && prop.status == 'A'")
        | {{computedSaletp}}
      span.stp(v-show="['exlisting','assignment','rent'].indexOf(prop.ltp) > -1")
        span(v-show="prop.ltp == 'exlisting'") {{_('Exclusive')}}
        span(v-show="prop.ltp == 'assignment'") {{_('Assignment')}}
        span(v-show="prop.ltp == 'rent' && !prop.cmstn") {{_('Landlord Rental')}}
        span(v-show="prop.ltp == 'rent' && prop.cmstn") {{_('Exclusive Rental')}}
      span.stp(v-show="prop.saletp && prop.status !== 'A'", :class="{'sold':soldOrLeased, 'inactive':!soldOrLeased}")
        span(v-if="soldOrLeased")
          span(v-show="saletpIsSale") {{_('Sold')}}
          span(v-show="!saletpIsSale") {{_('Leased')}}
        span(v-else)
          span {{_('Inactive')}}
      span.stp.viewTrusted(v-if="prop.marketRmProp")
        span.fa.fa-check-circle.trustedCir
        | {{_(prop.marketRmProp)}}
    div.realtor(@click.stop.prevent="realtorClicked(adrltr)", v-show="showRealtorInfo()")
      div
        img.avt(src="/img/logo.png", :src="avtSrc", onerror="this.src='/img/logo.png';return true;", referrerpolicy="same-origin")
        img.vip(src='/img/vip.png', v-show="adrltr.vip")
      span.nm(v-show="dispVar.lang == 'en'") {{adrltr.nm_en || adrltr.nm}}
      span.nm(v-show="dispVar.lang !== 'en'") {{adrltr.nm_zh || adrltr.nm}}
      //- span.nm(v-if="adrltr.fn || adrltr.ln") {{adrltr.fn}} {{adrltr.ln}}
      //- span.nm(v-show="adrltr.nm") {{adrltr.nm}}
    div.addr.one-line(v-if="!prop.login")
      span(v-if="prop.addr")
        span {{prop.unt}} {{prop.addr}},
        |  {{prop.city}}, {{prop.prov}}
      span(v-else)
        span(v-show="prop.cmty") {{prop.cmty}},
        | {{prop.city}}, {{prop.prov}}
    div.addr.one-line(v-if="prop.login")
      span(v-if="prop.addr") {{prop.addr}}, {{prop.city}}
      span(v-else) {{prop.city}}, {{prop.prov}}

    div.bdrms(v-if='!prop.login')
      span.rmbed(v-show="prop.bdrms != null || prop.tbdrms != null")
        span.fa.fa-rmbed
        b.num {{prop.bdrms || prop.tbdrms}} {{prop.br_plus?'+ '+prop.br_plus:''}}
      span(v-show="prop.rmbthrm != null || prop.tbthrms != null || prop.bthrms != null")
        span.fa.fa-rmbath
        b.num {{prop.rmbthrm || prop.tbthrms || prop.bthrms}}
      span(v-show="prop.rmgr != null || prop.gr != null || prop.tgr != null")
        span.fa.fa-rmcar
        b.num {{prop.rmgr || prop.tgr || prop.gr}}
      span(v-show="prop.isAgreementVerified")
        span.fa.fa-check-circle
        b.num {{_('Verified')}}
      span(v-if="prop.private")
        span.num(style="color:#e03131") {{_('Hide to the public')}}
      //- span
      //- span.sid(v-if="!isTop",:class="{'promo':prop.topup_pts}")
      //-   //- span.ad(v-if="(dispVar.isPaytop || dispVar.isVipRealtor) && prop.topup_pts", @click.stop.prevent="showPays()")
      //-   //-   | {{_('Ad','advertisement')}}
      //-   //-   i.fa.fa-caret-down
      //-   | {{propSid}}
    div.bdrms(v-if='prop.login')
      | {{_('Please login to see this listing!')}}
    div.verify(v-if="prop.ver")
      i.fa.fa-verify
      span {{_('Verified Listing')}}
</template>

<script>
import filters from '../filters'
export default {
  filters:{
    currency:filters.currency,
  },
  props: {
    dispVar:{
      type:Object,
      default:function () {
        return {};
      }
    },
    showFav:{
      type:Boolean,
      default:true,
    },
    rcmdHeight:{
      type:Number,
      default:170
    },
    prop: {
      type: Object,
      default: function () {
        return {}
      }
    },
    adrltr: {
      type: Object,
      default: function () {
        return {}
      }
    }
  },
  computed:{
    computedSaletp:function(){
      let saletp = this.prop.saletp;
      if (saletp) {
        if (Array.isArray(saletp)) {
          return saletp.join(' ')
        } else {
          return saletp;
        }
      }
      return this.prop.lpunt;
    },
    computedShowRealtor:function(){
      if (this.dispVar.hasFollowedVipRealtor) {
        return false;
      }
      if (!this.dispVar.isLoggedIn) {
        return false;
      }
      if (this.prop.ltp == 'rent') {
        return true;
      }
      return (!this.dispVar.isVisitor);
    },
    avtSrc:function () {
      if (this.adrltr.avt) {
        return this.adrltr.avt;
      }
      return '/img/logo.png';
    },
    soldOrLeased: function () {
      return this.prop.saleTpTag_en == 'Sold'|| ['Sld','Lsd','Pnd','Cld'].includes(this.prop.lst);
    },
    saletpIsSale: function () {
      if (!this.prop.saletp_en) {
        return true;
      }
      if (/sale/.test(this.prop.saletp_en.toString().toLowerCase())) {
        return true;
      }
      return false;
    },
    computedBgImg: function () {
      return this.prop.thumbUrl || "/img/noPic.png";
    },
    propSid:function () {
      if (this.prop.id) {
        return this.prop.id;
      } else if (this.prop.sid) {
        return this.prop.sid;
      } else {
        return this.prop._id?this.prop._id.substr(3):'';
      }
    },
    isTop:function() {
      return (this.prop.status == 'A') && (new Date(this.prop.topTs) >= new Date());
    },
  },
  data () {
    return {
    };
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
  },
  methods: {
    showPays(){
      window.bus.$emit('show-topup', this.prop);
    },
    propClicked () {
      window.bus.$emit('prop-changed', this.prop);
      // window.bus.$emit('set-loading', true);
    },
    realtorClicked () {
      checkAndSendLogger(null,{sub:'open realtor',id:this.prop._id,query:{realtor:this.adrltr._id}});
      window.bus.$emit('realtor-clicked', this.adrltr);
    },
    toggleFav(){
      checkAndSendLogger(null,{sub:'toggle fav',id:this.prop._id});
      window.bus.$emit('prop-fav-add', this.prop);
    },
    showRealtorInfo(){
      if ((this.prop.ltp == 'assignment' && this.dispVar.isRealtor) || this.prop.ltp != 'assignment'){
        return this.adrltr._id && this.computedShowRealtor && !this.prop.hideInfo && !this.prop.marketRmProp
      }
      return false
    }
  }
}
</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.on-img-top{
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
}
.prop {
  background: white;
}
.prop .img{
  height: 170px;
  background: #ddd;
  /*background-image:url('/img/noPic.png');*/
  /*background-size: cover;*/
  /*background-position: center center;*/
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.img .top {
  background-color: #e03131;
  padding: 3px 10px;
  color: #fff;
  font-size: 14px;
}
.prop .price{
  color: #fff;
  height: 31px;
  margin-top: -31px;
  padding: 6px 10px 0;
  /*background: rgba(39,39,39,.5);*/
  background: linear-gradient(transparent, #4a4a4a);
  display: flex;
  align-items: center;
  position: relative;
}
.prop .price .stp{
  background: #6fce1b;
  border-radius: 10px;
  font-size: 12px;
  padding: 0 7px;
  margin-left: 10px;
  vertical-align: bottom;
  color:white;
}
.prop .price .stp.sold{
  background: #e03131;
}
.prop .price .stp.inactive{
  background: #07aff9;
}
.prop .price .fa-check-circle{
  color: #6fce1b;
  font-size: 24px;
  margin-left: 10px;
  vertical-align: bottom;
  /* background: #fff;
  border-radius: 50%; */
}
.prop .bdrms .fa {
  font-size: 14px;
  margin-right: 5px;
  vertical-align: text-bottom;;
}
.prop .bdrms .num {
  font-size: 14px;
  margin-right: 10px;
}
.prop .bdrms span.sid{
  float: right;
  padding-right: 0;
  width: auto;
}
/*.prop .bdrms span.sid.promo{
  color: #e03131;
}*/
.prop .bdrms span.sid .ad{
  color: #e03131;
  font-size: 12px;
  border: 1px solid #e03131;
  margin-right: 7px;
  padding: 1px 5px;
  border-radius: 4px;
}
.prop .bdrms span.sid .ad .fa{
  padding-left: 3px;
  vertical-align: top;
}
.prop .addr, .prop .bdrms{
  padding: 1px 10px;
  /*padding: 1px 10px 0px 10px;*/
}
.prop .bdrms{
  font-size: 12px;
  color: #777;
  padding-bottom: 10px;
  min-height: 33px;
}
.bdrms .rmbed{
  width: auto;
  margin-right: 2px;
}
.prop .addr{
  font-size: 15px;
  padding-top: 10px;
  width: calc(100% - 100px);
}
.img .tp, .img .fav{
  color: white;
  display: inline-block;
}
.img .tp{
  background: #e03131;
  padding: 0px 5px;
  border-radius: 7px;
  margin: 4px 0 0 3px;
  font-size: 12px;
}
.img .fav{
  margin: 5px;
  margin-top: 15px;
  padding: 7px 7px 5px 5px;
  font-size: 23px;
  /*color:rgb(255, 235, 59);*/
  color: #e03131;
}
.img .fav.fa-heart{
  /* color: rgb(255, 233, 41); */
}
.img.blur{
  filter: blur(3px);
  -webkit-filter: blur(3px);
}
.price.blur{
  filter: blur(2px);
  -webkit-filter: blur(2px);
}
/*.img .fav.fa-heart{
  color:rgb(255, 235, 59);
}*/
.realtor > div{
  text-align: center;
}
.realtor{
  /*text-align: right;*/
  margin-top: -37px;
  float: right;
  /* margin-right: 15px; */
}
.realtor img{
  border-radius: 50%;
}
.realtor .avt{
  width: 70px;
  height: 70px;
  /*position: relative;*/
  border: 1px solid #fff;
}
.realtor .vip{
  width: 18px;
  height: 18px;
  margin-left: -16px;
}
.realtor .nm{
  white-space: nowrap;
  overflow: hidden;
  width: 90px;
  display: inline-block;
  text-align: center;
  font-size: 13px;
  color: #428bca;
  text-overflow: ellipsis;
}
.verify .fa{
  font-size: 16px;
  padding-right: 5px;
  display: inline-block;
  vertical-align: top;
  padding-top: 2px;
}
.verify{
  color: #e03131;
  font-size: 15px;
  padding: 0 0 10px 9px;
  margin-top: -3px;
}
.verify span{
  display: inline-block;
  padding-top: 0px;
  vertical-align: top;
}
.prop .price .viewTrusted{
  padding: 0 7px 0 0;
  background-color: #e03131;
  border-radius: 17px;
  font-weight: normal;
}
.prop .price .trustedCir{
  margin-left: 2px;
  padding-right: 2px;
  vertical-align: middle;
  color: #fff;
  font-size: 17px;
}
</style>
