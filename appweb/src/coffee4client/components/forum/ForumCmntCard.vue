<template lang="pug">
div.post-comments(v-if="canEdit || !cmnt.del")
  div.post-img(@click="showAuthorInfo(cmnt.uid)",:style="{ 'background-image': getImageUrl(cmnt.avt)}" )
  div.comments-right
    div
      div.name-wrapper
        div.name {{cmnt.fornm}}
        div.pull-right(:class='{blue:!cmnt.del && cmnt.tup,lightBlue:cmnt.tupcnt}')
          span.pointer.icon.fa.fa-rmliked(v-if="cmnt.tupcnt", @click="toggleThumb()")
          span.pointer.icon.fa.fa-rmlike(v-if="!cmnt.tupcnt", @click="toggleThumb()")
          span.pull-right2.tupcnt(v-if="cmnt.tupcnt") {{cmnt.tupcnt}}
      div.ts
        div.pull-left.my-rating(v-if="cmnt.r",:class="'my-rating-'+cmnt.r")
        span.red-button.pull-left(v-if="cmnt.sticky")
          | {{_('TOP')}}
        span {{formatTs2(cmnt.ts)}}
        //- span(v-if='canEdit'){{_('sticky time','forum')}} {{formatTs2(cmnt.mt)}}
        span.split
          |·
        span.pointer(style="color:#7ebae5;line-height: inherit;", @click="showComments(cmnt.idx)") {{_('Reply','forum')}}
        span.split
          |·
        span.pointer(style="color:#7ebae5;line-height: inherit;", @click="toggleBlockModal(cmnt)") {{_('Report & Block','forum')}}
        //- span.split
        //-   |·
        //- span.icon.icon-close.reportForumIcon(@click="toggleBlockModal(cmnt)")
    div.comment-content(v-if="!cmnt.del || showdel")
      div.ref-div(v-show="cmnt.ref>=0")
        div {{cmnt.refUser}} : {{showOriginalComment ? cmnt.refOrigContent : cmnt.refContent}}
      span(:id="'cmntContent_' + cmnt.idx") {{showOriginalComment ? cmnt.origM : cmnt.m}}
      div(v-if="cmnt.origM", style="margin-top: 5px;")
        a(href="javascript:void(0)", @click="toggleCommentOriginal", style="color: #7ebae5; text-decoration: none; font-size: 12px;")
          span(v-show="showOriginalComment") {{ _('Close original')}}
          span(v-show="!showOriginalComment") {{ _('View original')}}
    div.comment-control
      span.pointer.icon.fa.fa-undo(v-if="canEdit  && cmnt.del",@click="deleteCmntConfirm('recover', cmnt.idx, cmnt.wpHosts)", style="color:#E03131;")
      span.pointer.icon.fa.fa-angle-down(v-if="canEdit  && cmnt.del",@click="showdel=!showdel", style="float:right; padding-right:10px")
      span.pointer.icon.fa-trash(v-if="canEdit  && cmnt.del==false",@click="deleteCmntConfirm('delete', cmnt.idx, cmnt.wpHosts)")
      span.pointer.icon.fa-thumb-tack(v-if="canEdit && cmnt.sticky==false && !cmnt.del" @click="stickyComment(cmnt.idx, true)")
      span.pointer.icon.fa-dot-circle-o(v-if="canEdit && cmnt.sticky" @click="stickyComment(cmnt.idx, false)")
</template>

<script>
import pagedata_mixins from '../pagedata_mixins'
import forum_common_mixins from './forum_common_mixins'

export default {
  mixins: [pagedata_mixins,forum_common_mixins],
  props: {
    cmnt: {
      type: Object,
      default: function () {
        return {
          sticky:null,
          del:null,
          avt:null
        }
      }
    },
    canEdit: {
      type:Boolean,
      default:function() {
        return false;
      }
    }
  },
  components: {
  },
  computed:{
  },
  data () {
    return {
      showdel:false,
      share:vars.share,
      isClickThumb:true,
      showOriginalComment: false
    };
  },
  methods: {
    toggleBlockModal(cmnt) {
      this.$parent.$parent.toggleBlockModal(cmnt);
    },
    toggleThumb() {
      if(vars.share) {
        return window.location.href = '/app-download';
      }
      if(!this.isClickThumb) {
        return;
      }
      this.isClickThumb = false;
      var self = this;
      let index = this.cmnt.idx;
      let tup = !this.cmnt.tup
      var params = {action:'tup', index: index, tup: tup};
      self.$parent.$parent.manageComment(params, function(ret) {
        self.cmnt.tup = tup;
        self.isClickThumb = true;
        if (tup) {
          self.cmnt.tupcnt ++;
        } else {
          self.cmnt.tupcnt --;
        }

      });
    },
    showAuthorInfo(uid) {
      this.$parent.$parent.showAuthorInfo(uid);
    },

    deleteCmntConfirm(action, index, wpHosts) {
      event.stopPropagation();
      this.$parent.$parent.deleteCmntConfirm(action, index, wpHosts)
    },
    stickyComment(index, sticky) {
      event.stopPropagation();
      this.$parent.$parent.stickyComment(index, sticky)
    },
    /**
     * 切换评论原文显示
     */
    toggleCommentOriginal() {
      // 检查是否存在原文内容
      if (!this.cmnt.origM) {
        return;
      }

      // 切换显示状态
      this.showOriginalComment = !this.showOriginalComment;
    },
    // reportComment(cmnt) {
    //   window.bus.$emit('report_comment', cmnt);
    // }
  }
}
</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
@import '../../../style/sass/base.scss';
@import '../../../style/sass/components/forum/cmntCard.scss';
</style>
