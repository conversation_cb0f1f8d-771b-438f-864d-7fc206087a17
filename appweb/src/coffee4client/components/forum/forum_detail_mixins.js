import LZString from 'lz-string'

var mixins = {
  created :  function () {
  },
  data () {
    return {
      post:{
        _id:null,
        del:null,
        tl:'',
        m:'',
        sticky:false,
        cmnts:[],
        favs:[],
        city:null,
        prov:null,
        avt:null,
        photos:[],
        wpHosts:[],
        discmnt:false,
        mt:null,
        // adInBtop:'',
        // adIntop:'',
        rank:0,
        adTop:'',
        adTopPhoto:'',
        noCpm:null,
        myRating:0,
        avgRoundRating:0,
        avgRating:0,
      },
      dispVar: {
        isLoggedIn:  false,
        lang:        'zh',
        isApp:       true,
        isCip:       false,
        forumAdmin: false,
        sessionUser: {
          fas:[]
        },
        shareHost:false,
        canWebComment: false,
        globalTags:[],
        ownerData:{},
        isVipRealtor: false,
        isAdmin:false,
        isRealtor: false,
        edmAdmin: false,
        userGroups:[]
      },
    }
  },
  computed: {
    shareUrl: function () {
      if (this.wId) {
        return document.location.href;
      }
      var url= null
      if (this.col=='psch') {
        url = this.dispVar.shareHost + "/1.5/school/private/detail/"+this.post._id+"?share=1&lang=" + this.dispVar.lang;
      } else if (this.col=='sch') {
        url = this.dispVar.shareHost + "/1.5/school/public/detail?id="+this.post._id+"&share=1&lang=" + this.dispVar.lang;
      } else {
        var url = this.dispVar.shareHost + '/1.5/topics/details?share=1&id=' + this.post._id + '&lang=' + this.dispVar.lang;
      }
      if (this.post.gid) {
        url += '&gid='+this.post.gid ;
      }
      if (this.wComment) {
        url += '&wct=1';
      } else {
        url += '&wct=0';
      }
      if (this.dispVar.isRealtor && this.wSign) {
        url += '&aid=' + this.dispVar.sessionUser._id;
      }
      return url;
    },
  },
  methods : {
    computedCmntNum: function() {
      if (!this.post.cmnts)
        return 0;
      var self = this;
      var cmnts = this.post.cmnts.filter(function(cmnt){
        return (!cmnt.del && self.checkBlockCmnt(cmnt));
      });
      return cmnts.length > 99 ? '99+' :  cmnts.length;
    },
    setDefaultPhoto(el) {
      console.log(el);
      //defined in forum.coffee,
      window.setDefaultPhoto(el);
    },
    refreshWpCategory() {
      if (this.loading == true)
        return
      this.loading = true;
      this.$http.post('/1.5/forum/refreshWpCategory',{}).then(
        function(ret) {
          if (ret.data.ok) {
            window.bus.$emit('flash-message', "refreshed");
          } else {
            window.bus.$emit('flash-message', ret.data.e);
          }
          this.loading = false;
        },
        function(err) {
          this.loading = false;
          console.error(err.status+':'+err.statusText);
          RMSrv.dialogAlert('Error happens when  refreshWpCategory. stauts:'+err.status+': statusText:'+err.statusText);
        }
      )
    },
    manageRealtorOnly() {
      var self = this;
      var params = {type:'realtorOnly',realtorOnly: self.post.realtorOnly, gid: self.post.gid}
      self.$http.post('/1.5/forum/sticky/'+self.post._id, params).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            return;
          } else {
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (ret) {
          RMSrv.dialogAlert( "server-error" );
        }
      );
    },
    manageEdm(type) {
      var self = this;
      var url = '/1.5/forum/edm';
      var params = {_id: self.post._id}
      params.del = this.post[type] ? 1 : 0;
      params.type = type;
      if(this.post.gid) {
        params.gid = this.post.gid
      }
      self.$http.post(url, params).then(
        function (ret) {
          ret = ret.data;
          if (ret.ok) {
            if (ret[type]) {
              self.post[type] = ret[type];
              self[type] =  self.formatTs(ret[type]);
            } else {
              self.post[type] = null
              self[type] = null;
            }
          } else {
            RMSrv.dialogAlert(ret.err);
          }
        },
        function (ret) {
          RMSrv.dialogAlert( "server-error" );
        }
      );
    },

    disableCmnt(disable) {
      var self = this;
      var _doDisableCmnt = function(idx) {
        if (idx+'' != '2') {
          return;
        }
        var params = {discmnt:disable, city: self.post.city, prov: self.post.prov}
        if(self.post.gid) {
          params.gid = self.post.gid
        }
        if(self.col) {
          params.col = self.col;
        }
        self.$http.post('/1.5/comment/disableCmnt/' + self.post._id, params).then(
            function(ret) {
              if (ret.data.ok) {
                self.post.discmnt = disable;
              } else {
                return window.bus.$emit('flash-message', ret.data.e);
              }
            },
            function(err) {
              console.error(err.status+':'+err.statusText);
            }
          );
      };
      var text;
      disable? text = 'disable' : text = 'enable'
      var msg = 'Are you sure to '+ text +' the comments?'
      RMSrv.dialogConfirm(msg,
        _doDisableCmnt,
        "Message",
        ['Cancel', 'Confirm']
      );
    },
    updateMt() {
      var self = this;
      var params = {id: self.post._id};
      if(this.post.gid) {
        params.gid = this.post.gid
      }
      self.$http.post('/1.5/forum/updateMt/', params).then(
        function(ret) {
          if (ret.data.ok) {
            self.post.mt = ret.data.mt;
            return window.bus.$emit('flash-message', "Updated");
          } else {
            return window.bus.$emit('flash-message', ret.data.e);
          }
        },
        function(err) {
          console.error(err.status+':'+err.statusText);
          RMSrv.dialogAlert('Error happens when updateMt. stauts:'+err.status+': statusText:'+err.statusText);
        }
      );
    },
    pushToWp() {
      var self = this;
      if(self.loading == true) {
        return;
      }
      self.loading = true;
      self.$http.post('/1.5/forum/pushToWp', {_id: this.post._id}).then(
        function(ret) {
          if (ret.data.ok) {
            self.post.wpHosts = ret.data.wpHosts;
            self.post.wpHosts = self.post.wpHosts.slice(0);
            self.wpmt = self.post.wpHosts[0].mt;
            window.bus.$emit('flash-message', "Pushed");
          } else {
            window.bus.$emit('flash-message', ret.data.e);
          }
          self.loading = false;
        },
        function(err) {
          self.loading = false;
          console.error(err.status+':'+err.statusText);
          RMSrv.dialogAlert('Error happens when pushToWord Press. stauts:'+err.status+': statusText:'+err.statusText);
        }
      );
    },


    sticky(sticky) {
      var self = this;
      var params = {type:'sticky', sticky:sticky, city: self.post.city, prov: self.post.prov};
      if(this.post.gid) {
        params.gid = this.post.gid
      }
      self.$http.post('/1.5/forum/sticky/' + self.post._id,params ).then(
        function(ret) {
          if (ret.data.ok) {
            self.post.sticky = sticky;
            self.post_sticky = sticky;
          } else {
            return window.bus.$emit('flash-message', ret.data.e);
          }
        },
        function(err) {
          console.error(err.status+':'+err.statusText);
        }
      );
    },
    showAgentInfo(uid){
      return RMSrv.openTBrowser(this.appendDomain('/1.5/wesite/'+uid+'?inFrame=1'));
    },
    showAuthorInfo(uid) {
      if (this.post.category && vars.from!='wesite' && this.post.isMerchant) {
        return this.openTBrowser(this.appendDomain('/1.5/wesite/'+uid+'?inFrame=1'));
      }
      if(!this.dispVar.isAdmin) return;
      var self = this;
      self.$http.get('/1.5/forum/authorInfo?uid='+uid).then(
        function(ret) {
          if (ret.data.ok) {
            return window.bus.$emit('showAuthorDialog', ret.data.user);
          } else {
            return window.bus.$emit('flash-message', ret.data.e);
          }
        },
        function(err) {
          console.error(err.status+':'+err.statusText);
        }
      );
    },
    closeAuthorInfo() {
      this.showMask = false;
      this.showAuthorDialog = false;
    },

    pushPost(opt) {
      var self = this;
      self.pushNotified = true;
      window.bus.$emit('flash-message', "Push Notified");
      var params = {id: self.post._id, tl: self.post.tl||self.post.nm, realtorOnly:self.post.realtorOnly};
      if (opt == 'unread') {
        params.groupUnread = opt;
      }
      if (opt == 'dev') {
        params.dev = true;
      }
      if(this.post.gid) {
        params.gid = this.post.gid
      }
      self.$http.post('/1.5/forum/pushNotify/', params).then(
        function(ret) {
          if (ret.data.ok) {
            self.post_pn = 1;
            return window.bus.$emit('flash-message', ret.data.msg);
          } else {
            return window.bus.$emit('flash-message', ret.data.e);
          }
        },
        function(err) {
          console.error(err.status+':'+err.statusText);
          RMSrv.dialogAlert('Error happens when push notify. stauts:'+err.status+': statusText:'+err.statusText);
        }
      );
    },
    closeForumView(newPostId, tag, loadPosts) {
      if(vars.src=='property') {
        window.location.href="/1.5/forum?city="+this.post.city;
      }
      var obj = {readStatus:this.post.readStatus, gid: this.post.gid, realtorOnly: this.post.realtorOnly, cmntl: this.post.cmntl, _id: this.post._id, vc: this.post.vc, cc: this.post.cc, newPostId: newPostId, tag:tag,loadPosts};
      var ret = JSON.stringify(obj);
      window.rmCall(':ctx:' + ret);
    },
    goToProperty(id='') {
      // NOTE:
      if (vars.id && vars.src === 'property'){
        return;
      }
      var propId = id||this.post._id;
      // console.log('++++++',id,propId)
      // return;
      if(this.post.src==='property'||this.post.src==='video') {
        var url = '/1.5/prop/detail?id='+propId+'&inframe=1&nobar=1';
        url = this.appendDomain(url);
        if (vars.dispVar.isApp) {
          let url = '/1.5/prop/detail/inapp?id='+propId+'&mode=map&inframe=1&showShareIcon=1';
          url = this.appendDomain(url);
          var cfg = {hide:false,title:this._('RealMaster')};
          RMSrv.getPageContent(url, '#callBackString', cfg, function(val){
            if (val == ':cancel') {
              console.log('canceled');
              return;
            }
            // if (/^redirect/.test(val)) {
            //   return window.location = val.split('redirect:')[1]
            // }
            try {
              if (/^cmd-redirect:/.test(val)) {
                var url = val.split('cmd-redirect:')[1];
                var callBackStr = ":ctx:cmd-redirect:"+url;
                window.rmCall(callBackStr);
              }
            } catch (e) {
              console.error(e);
            }
          });
        } else {
          var url = '/1.5/prop/detail?id='+propId+'&inframe=1';
          url = this.appendDomain(url);
          window.location.href = url;
        }
      }
    },
    openAd(url, index) {
      this.$http.post('/1.5/forum/adClick',{id: this.post._id, index:index, gid:this.gid}).then(
        function(ret) {
          if (ret.data.ok) {
            if (!url) {
              return null;
            } else {
              RMSrv.showInBrowser(url);
            }
          }
        },
        function(err) {
          console.error(err.status+':'+err.statusText);
        });
    },
    confirmVerifyEmail:function (optTip,optTl) {
      var optTip = optTip?optTip:"To be presented here, please complete your personal profile.";
      var fn = this._?this._:this.$parent._;
      // var tip = fn(optTip);
      var later = fn('Later');
      var seemore = fn('Do it Now');
      var optTl = optTl?optTl:"";
      function _doShowEditProfile(idx) {
        if (idx+'' == '2') {
          RMSrv.closeAndRedirectRoot('/1.5/settings/notification');
        }
      }
      return RMSrv.dialogConfirm(optTip, _doShowEditProfile, optTl, [later, seemore]);
    },
    manageComment(params, cb) {
      var self = this;
      if(this.post.gid) {
        params.gid = this.post.gid;
      }
      if(self.col) {
        params.col = self.col;
      }
      self.$http.post('/1.5/comment/' + self.post._id, params).then(
        function(ret) {
          if (ret.data.ok){
            return cb(ret.data);
          }
          else{
            self.publishing = false;
            if (ret.data.forBlk){
              self.closeCommentsDialog({clear:true});
              return;
            } else if (ret.data.verifyEmail) {
              // TODO: alert window
              // todo 重复翻译
              self.confirmVerifyEmail(ret.data.e);
              // RMSrv.dialogAlert(ret.data.e)
            } else {
              window.bus.$emit('flash-message', ret.data.e);
            }
          }
        },
        function(err) {
          console.error(err.status+':'+err.statusText);
        }
      );
    },
    initCmnts() {
      var self = this;
      if (!self.post.cmnts) return;
      self.sticky_cmnts = [];
      self.recent_cmnts = [];
      self.display_cmnts = [];
      for (var i = self.post.cmnts.length; i>0; i--) {
        var element = self.post.cmnts[i-1];
        element.idx = i-1;
      }
      for (var i = self.post.cmnts.length; i>0; i--) {
        var element = self.post.cmnts[i-1];

        if (element.ref>=0) {
          var refcmnt = self.post.cmnts.find(function(cmnt) {
            return cmnt.idx == element.ref;
          });
          if (refcmnt && refcmnt.m) {
            var m = refcmnt.m
            element.refContent  = m.length > 70 ? m.substr(0,100)+'...' : m;
            element.refUser  = refcmnt.fornm;
            element.refTs  = this.formatTs(refcmnt.ts);
            if (refcmnt.origM) {
              element.refOrigContent  = refcmnt.origM.length > 70 ? refcmnt.origM.substr(0,100)+'...' : refcmnt.origM;
            }
          }
        }
        // only add deleted comments for admin.
        if(self.computedAdmin|| !element.del)
          self.recent_cmnts.push(element);
      }
      self.recent_cmnts.sort(self.sortCmnt);
      self.display_cmnts = self.recent_cmnts.slice(0,19);
      self.updateCmntsAfterBlock();
    },
    checkBlockCmnt(cmnt) {
      var blkUids,blkCmnts;
      try {
        blkUids = JSON.parse(localStorage.getItem('blkUids'));
        blkCmnts = JSON.parse(localStorage.getItem('blkCmnts'));
      } catch (error) {
        console.error(error);
      }
      if (blkUids && blkUids[cmnt.uid]) {
        return false;
      }
      var forumId = vars.post._id;
      if (blkCmnts && blkCmnts[forumId] && blkCmnts[forumId][cmnt.idx]) {
        return false;
      }
      return true;
    },
    updateCmntsAfterBlock() {
      var cmnts = [];
      for (let i = 0; i < this.display_cmnts.length; i++) {
        const cmnt = this.display_cmnts[i];
        if (this.checkBlockCmnt(cmnt)) {
          cmnts.push(cmnt);
        }
      }
      this.display_cmnts = cmnts;
    },
    computeFav: function () {
      if (!this.post.favs) return false;
      return this.post.favs.indexOf(this.dispVar.sessionUser._id) >= 0
    },

    initPostDetail() {
      var self = this;
      self.post_sticky = self.post.sticky || false;
      self.post_fav = self.computeFav()|| false;
      self.post_pn = self.post.pn;
      self.post.favs = self.post.favs || [];
      if (self.post.edm)
        self.edm = self.formatTs(self.post.edm);
      if (self.post.edmw)
        self.edmw = self.formatTs(self.post.edmw);
      if (self.post.edmd)
        self.edmd = self.formatTs(self.post.edmd);
      self.post.wpHosts = self.post.wpHosts || []
      if (self.post.wpHosts && self.post.wpHosts.length) {
        self.wpmt = self.post.wpHosts[0].mt;
      }
    },
    previewPic (event) {
      var el = event.target;
      var self = this;
      self.picUrls = [];
      self.sizes = [];
      var compressed, index, text, url;
      if (!el || /^data:image/.test(el.src)) {
        return;
      }
      var imgs = document.querySelectorAll('.post-content img, .post-photo img')||[]
      for (var img of imgs) {
        var datasize = img.getAttribute('data-size');
        let isBase64 = /^data:image/.test(img.src)
        if (datasize && !isBase64 && img.src) {
          self.sizes.push(datasize);
          self.picUrls.push(img.src);
        }
      }
      if(!self.picUrls.length)
        return

      index = self.picUrls.findIndex(function(pic) {
        return pic == el.src
      });
      if (!index) {
        index = Math.abs(window.slideNumber) || 0;
      }

      text = JSON.stringify({
        urls: self.picUrls,
        index: index,
        sizes: self.sizes
      });
      if (!LZString) {
        return console.error('LZString is required!');
      }
      compressed = LZString.compressToEncodedURIComponent(text);
      url =  '/1.5/SV/photoSwipe?data=' + compressed;
      // console.log(url)
      url = this.appendDomain(url);

      if (!window.RMSrv && !this.inFrame) {
        return;
      }
      self.openTBrowser(url);
    },
  },
}
export default mixins;
