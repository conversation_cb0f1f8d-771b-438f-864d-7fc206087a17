RMSrv =
  getCookie: (cname)->
    name = cname + "=";
    ca = document.cookie.split(';')
    for c in ca
      while (c.charAt(0) is ' ')
        c = c.substring(1)
      if c.indexOf(name) is 0
        return c.substring(name.length,c.length)
    ""
  cError: (err,url = window.location.href)->
    # send error to server
    console.error err
    try
      strErr = JSON.stringify err
    catch e
    window.onerror err.toString(),url,(strErr or err.toString())
  _actCallbacks: {next: Date.now()}
  _actListeners: {} # key: [{fn:once:}]
  _emitEvent: (json)=>
    if (fns = RMSrv._actListeners[json?.tp]) and fns.length > 0
      for fnObj,i in fns by -1
        # most the case, it's only one listener, so save the trouble for cloning object
        # p = if json.p then JSON.parse(JSON.stringify(json.p)) else null
        setTimeout((-> fnObj.fn(json.p)),0)
        fns.pop() if fnObj.once # once function always at the end
  action: (act,callback)=> # send event to native
    if 'string' is typeof act
      act = tp:act
    if 'object' isnt typeof act
      throw new Error "RM: wrong action type"
    if callback
      act.cb = RMSrv._actCallbacks.next
      RMSrv._actCallbacks[RMSrv._actCallbacks.next++] = callback
    # alert act.tp+act.cb
    window.rmCall JSON.stringify act
  reaction: (data)=> # send live
    #setTimeout (-> alert(data)),0
    return if data.charAt(0) isnt '{'
    json = JSON.parse data
    # alert ''+json.tp+':'+json.cb+typeof(RMSrv._actCallbacks[json.cb])+':'+json.p
    if json.cb and (fn = RMSrv._actCallbacks[json.cb])
      delete RMSrv._actCallbacks[json.cb]
      # alert(json.cb+'+'+json.p+'###')
      setTimeout((-> fn json.p),0)
    else if json.tp
      if json.tp is 'ready'
        RMSrv._actCallbacks.next = json.next if json.next?
      else
        RMSrv._emitEvent json
    else
      console.log {warn:"Undealt reaction",data:data}
  listen: (actType,once,fn)=>
    unless fn
      fn = once
      once = false
    fns = RMSrv._actListeners[actType] ?= []
    for existFn in fns
      if existFn.fn is fn
        throw new Error("Conflict listen once flag for #{actType}") if existFn.once isnt once
        return
    if once
      fns.unshift {fn:fn}
    else # keep once at last
      fns.push {fn:fn,once:once}
  setFileChooser: (id, chooser)->
    return
  removeListner: (actType,fn)=>
    unless fn # remove all
      delete RMSrv._actListeners[actType]
      return
    fns = RMSrv._actListeners[actType] ?= []
    for existFn,i in fns
      if existFn.fn is fn
        fns.splice(i,1)
        return
  init: =>
    #handle postMessage from native postMessage
    document.addEventListener "message", (event)->
      #return if event.isTrusted?
      setTimeout( (()->
        # alert('window.onMessage:'+JSON.stringify(event.data))
        RMSrv.reaction(event.data)
        ),0);
    #if androidVer = RMSrv.androidVersion()
    # ERROR:post message not enough arguments, IOS need push stack too
    window.rmCall = (data)-> (RMSrv._callStack ?= []).push data
      # window.rmCall = (data)-> #window.postMessage data
      #   __REACT_WEB_VIEW_BRIDGE.postMessage(String(data))
    # else
    #   window.rmCall = (data)->
    #      window.location = 'react-js-navigation://rmCall?' + encodeURIComponent(String(data))
    setTimeout(()->
      # if not RMSrv.ready
      # alert('timeouted')
      RMSrv.goReady()
      # RMSrv.ready = true
    ,500)
    #TODO: consider drop support old android webviews?
    if (androidVer = RMSrv.androidVersion()) and (parseFloat(androidVer) < 4.4)
      window.oldVerBrowser = true
    RMSrv.listen 'pushToken',RMSrv.pushToken
    RMSrv.listen 'pushNotice',RMSrv.pushNotice
    RMSrv.listen 'openURL',handleOpenURL
    RMSrv.listen 'native.keyboardshow',RMSrv.handleKeyboardshow

    #RMSrv.action 'pushToken',RMSrv.pushToken
    # setup file choosers for android 4.4.2
    # RMSrv._setFileChooser()
  goReady: ->
    # if RMSrv.androidVersion()
    # alert('goReady: '+RMSrv.ready)
    return if RMSrv.ready
    setTimeout (->
      if RMSrv.androidVersion()
        #NOTE: length 2->1; undefined -> [native code]
        # alert window.originalPostMessage#postMessage.length
        if not window.originalPostMessage#postMessage.length
          return setTimeout(()->
            RMSrv.goReady()
          , 500)
        RMSrv.ready = true
        window.rmCall = (data)-> window.postMessage data
      else
        RMSrv.ready = true
        window.rmCall = (data)->
          window.location = 'react-js-navigation://rmCall?' + encodeURIComponent(String(data))
          # try
          #   window.postMessage data
          # catch err
          #   console.error err
          #   setTimeout (()->
          #     window.postMessage data
          #   ), 300
      doWhenReady = ()->
        doOneReady = ()->
          if job = RMSrv._whenReady?.shift()
            job() if 'function' is typeof job
            setTimeout doOneReady,1
          else
            delete RMSrv._whenReady
        doOneReady()
      doOne = ->
        if job = RMSrv._callStack?.shift()
          window.rmCall job
          setTimeout doOne,1
        else
          delete RMSrv._callStack
          doWhenReady()
      doOne()
        # for d in RMSrv._callStack
        #   setTimeout(()->
        #     tsgap += 100
        #     window.rmCall d
        #   ,tsgap)
        # delete RMSrv._callStack
      ),1
  onReady: (cb)->
    if RMSrv.ready # iOS alwasy ready
      # console.log '++++ready'
      # RMSrv.action({tp:'log',p:'++++ready'})
      return setTimeout((-> cb()),0)
    (RMSrv._whenReady ?= []).push cb
  enableBackButton: (enabled = true)-> RMSrv._disableBackKey = not enabled
  setupBackBtn: ->
    RMSrv.listen "backbutton",((e)->
      return if RMSrv._disableBackKey
      if document.getElementById('top-page')
        e.preventDefault()
        navigator.notification.confirm "Quit?",((btn)->
          if btn is 1
            navigator.app.exitApp()
          )
      else if document.getElementById('news-page')
        e.preventDefault()
        window.location = '/1.5/index'
      else if document.getElementById('wecard-list-page')
        e.preventDefault()
        window.location = '/1.5/settings'
      else if document.getElementById('dl-share-content')
        window.location.href = document.referrer
      else if document.getElementById('srvEle')
        e.preventDefault()
        toggleModal('propDetailModal', 'close')
      else
        navigator.app.backHistory()
    ), false
  confirmSettings: (opt={}) ->
    # alert JSON.stringify opt
    optTip = opt.tip or "To find nearby properties and schools you need to enable location."
    # var fn = this._?this._:this.$parent._;
    # var tip = fn(optTip);
    later = opt.later or 'Later'
    gotosettings = opt.go or 'Go to settings'
    # var optTl = optTl?optTl:"";
    _doShowGotoSettings = (idx) ->
      if (idx+'' is '2')
        RMSrv.openSettings()
    return RMSrv.dialogConfirm(optTip, _doShowGotoSettings, '', [later, gotosettings])
  getGeoPosition: (opt,cb)->
    if not cb
      cb = opt
      opt = null
    # alert JSON.stringify opt
    RMSrv.permissions 'location','check',(result)->
      if result is 'authorized'
        RMSrv.action 'geoPosition',cb
      else if result is 'denied'
        if RMSrv.isIOS()
          RMSrv.confirmSettings(opt)
        else
          # msg = opt?.msg or 'Location permission Denied, Go to settings to enable.'
          # RMSrv.dialogAlert(msg)
          # NOTE: trigger permission denied in native,
          RMSrv.action 'geoPosition',cb
      else
        RMSrv.permissions 'location','request',(rresult)->
          # if rresult is 'authorized'
          RMSrv.action 'geoPosition',cb
          # else
          #   RMSrv.dialogAlert('No location permission')
  scanQR: (returnUrl)->
    RMSrv.action 'qrcode',(txt)=>
      if typeof returnUrl is 'function'
        return returnUrl(txt)
      else if typeof returnUrl is 'string'
        window.location = (returnUrl or "/1.5/iframe?u=") + encodeURIComponent(txt)
      else
        RMSrv.cError "Unknown scanQR parameter type"
  isIOS: -> /iPhone|iPad|iPod/i.test(navigator.userAgent)
  isAndroid: -> /Android/i.test(navigator.userAgent)
  isWeChat: -> /MicroMessenger/i.test(navigator.userAgent)
  isBlackBerry: -> /BlackBerry/i.test(navigator.userAgent)
  androidVersion: -> if (match = navigator.userAgent.toLowerCase().match(/android\s([0-9\.]*)/)) then match[1] else false
  appendDomain:(url)->
    location = window.location.href
    arr = location.split("/")
    domain = arr[0] + "//" + arr[2]
    domain + url
  showInBrowser: (url)->
    unless /^(http|https)/.test url
      url = this.appendDomain(url)
    RMSrv.action {tp:"openInBrowser",url:url}
  setAppLang:(lang)->
    RMSrv.action {tp:"setAppLang",lang:lang}
  showInMap: (opt)->
    action = {tp:"map",lat:opt.lat,lng:opt.lng, title:opt.title}
    action.marker = opt.marker if opt.marker? #bool
    action.zoom = opt.zoom if opt.zoom
    action.which = opt.which if opt.which
    # ['hybrid', 'terrain', 'satellite', 'standard']
    # "standard",
    # "satellite",
	  # "hybrid",
	  # "terrain"//androud only
    action.mapTypeId = opt.mapTypeId or 'standard'
    RMSrv.action action
  closeAndRedirect: (url)->
    RMSrv.action {tp:"closeAndRedirect",url:url}
  closeAndRedirectRoot: (url)->
    RMSrv.action {tp:"closeAndRedirectRoot",url:url}
  fetch: (url, opt, cb)->
    RMSrv.action {tp:'fetch',opt:opt, url:url},cb
  coreVer: (cb)->
    return cb RMSrv.coreVersionStr if RMSrv.coreVersionStr
    # when no return from core, callback with null
    timeoutHandler = setTimeout (->
      if cb
        cbTmp = cb
        cb = null
        cbTmp(null)
      ), 500
    RMSrv.action {tp:'coreVer'},(coreVer)->
      clearTimeout timeoutHandler
      RMSrv.coreVersionStr = coreVer
      if cb
        cbTmp = cb
        cb = null
        cbTmp coreVer
  downloadImage:(url,opt,cb)->
    RMSrv.action {tp:'downloadImage',opt:opt, url:url},cb
  permissions:(which='location',cmd='check',cb)->
    # Response is one of: 'authorized', 'denied', 'restricted', or 'undetermined'
    if cmd not in ['request','check']
      RMSrv.dialogAlert('Not Supported action: '+cmd)
      return
    if which  not in ['location', 'notification']
      RMSrv.dialogAlert('Not Supported permission type: '+which)
      return
    computedTp = 'permissions.'+cmd+'.'+which
    if cmd is 'check'
      permissionTimeout = setTimeout ()->
        cb2 'denied'
      , 700
    cb2 = (ret)->
      return unless cb
      clearTimeout permissionTimeout
      oldCb = cb
      cb = null
      if (cmd is 'check')
        RMSrv.getItemObj which+'Permission',true,(whichStatus)->
          # 安卓的location在blocked后，check的结果为denied，所以需要对比之前请求过的记录。如果之前有记录，check结果为denied，则返回blocked
          if (whichStatus.indexOf('true') > -1) and (ret is 'denied')
            ret = 'blocked'
          oldCb ret
      else
        oldCb ret
    RMSrv.action {tp:computedTp},cb2
  openSettings:()->
    RMSrv.action {tp:'permissions.openSettings'}
  getItemObj:(key, readCache, cb)->
    if not cb
      cb = readCache
      readCache = false
    RMSrv.action {tp:'storage.getItemObj', key:key},cb
  setItemObj:({key, value}, cb)->
    RMSrv.action {tp:'storage.setItemObj', key:key, value:value},cb
  removeItemObj:(key, cb)->
    RMSrv.action {tp:'storage.removeItemObj', key:key},cb
  refreshSystemValue:(opt,cb)->
    RMSrv.action {tp:'refreshSystemValue', opt:opt},cb
  openTBrowser: (url, cfg, cb)->
    # unless cb
    unless /^(http|https)/.test url
      url = this.appendDomain(url)
    RMSrv.action {tp:"popup",url:url,cfg:cfg}
    # else
    #   RMSrv.action {tp:"popup",url:url,cfg:cfg}, cb
    null
  openInAppBrowser: (url,options='location=false')-> #',toolbar=false')->
    RMSrv.action {tp:"popup",url:url,cfg:options}
    #ref = window.open(encodeURI(url), '_blank', options)
  dialogAlert: (msg)->
    RMSrv.action {tp:"alert",msg:(if 'string' is typeof msg then msg else msg.message or msg.toString())}
  dialogConfirm: (msg,cb,title,btns)->
    if not btns
      if title and Array.isArray title
        btns = title
        title = 'Confirm'
    RMSrv.action {tp:"confirm",title:title,msg:msg,btns:btns},cb
  isDBG: false # /i\.realmaster/i.test window.location.hostname or /app\.test/i.test window.location.hostname
  fDoc: ->
    doc = null
    if iframe = document.getElementById 'iframe'
      try
        doc = iframe.contentDocument
        doc ?= document
        doc = doc.document if doc.document
      catch e
        console.log e
    #console.log doc
    doc
  getMeta: (doc)->
    meta = doc.querySelectorAll 'meta'
    ret = title:doc.title
    for m in meta
      ret[m.getAttribute('name')] = m.getAttribute('content')
    ret
  getShareImage: (doc)->
    if div = (doc.getElementById('content_div') or doc.body)
      for img in div.getElementsByTagName 'img'
        if img?.src
          return img.src
    'https://realmaster.com/img/logo.png'
  logoImg: "data:image/png;base64,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"
  hasWechat: (cb)-> RMSrv.action 'wechat.has',cb
  wechatAuth: ->
    RMSrv.action 'wechat.auth',(resp)->
      #alert JSON.stringify resp
      if resp?.code?.length > 10
        window.location.href = "/scheme?code=" + resp.code
    #   Wechat?.auth "snsapi_userinfo",((resp)->
    #       # got code here, will call /schema?code=xxx automatically
    #       # android don't go to /schema automatically, need jump
    #       if RMSrv.isAndroid() and resp?.code?.length > 10
    #         window.location.href = "/scheme?code=" + resp.code
    #     ),(reason)->
    #       location.reload() # when bind/or auth
    #       console?.log reason
  wechatShareError: (err)->
    errStr = err?.toString()
      # when 'ERR_USER_CANCEL'
      #   return
    if errStr is 'ERR_WECHAT_NOT_INSTALLED'
      RMSrv.dialogAlert('WeChat Not Installed')
      # when 'ERR_UNKNOWN' # user can't do anything with unknown error. Don't show it.
      #   window.onerror "WeChat Sharing ERR_UNKNOWN",window.location.href,""
      #   return
    else if 'object' is typeof err and err.code is -2
      RMSrv.dialogAlertCancel()
    else
      RMSrv.dialogAlert(JSON.stringify(err))
  dialogAlertCancel:()->
    msg = 'Cancelled'
    if RMSrv._shareLang in ['zh','zh-cn']
      msg = '已取消'
    else if RMSrv._shareLang is 'kr'
      msg = '취소되었습니다'
    RMSrv.dialogAlert(msg)
  wechatShare: (doc,share,tp,msgTp)->
    opts =
      title: share.title or "RealMaster Sharing"
      description: share.description or "RealMaster App Sharing"
      thumb: share.image
      url: share.url
      tp: tp or 0
      type: msgTp or 'news'
    if not /d\d\.realmaster/.test share.url
      opts.url = share.url.replace('realmaster.com','realmaster.cn')
    if (not RMSrv.isIOS()) and /\.gif/.test opts.thumb
      opts.thumb = RMSrv.logoImg
    # console.log share
    # return;
    RMSrv.shared share, true
    RMSrv.action {tp:'wechat.share',p:opts},(ret)->
      # alert JSON.stringify ret
      if ret.err
        RMSrv.wechatShareError(ret.err)
      else if ret.ok
        RMSrv.shared share
  facebookShare: (doc,share,opt={})->
    RMSrv.shared share, true
    RMSrv.action(
      {tp:'facebook.share',
      content:{
        contentType: opt.type or 'link',
        contentUrl:share.url,
        contentDescription: (share.title or '') + (share.description or "RealMaster App Sharing")
        }
      },(ret)->
        if ret.err
          RMSrv.dialogAlert err.toString()
        else if ret.cancelled
          RMSrv.dialogAlert 'Cancelled'
        else
          RMSrv.shared share
    )
  socialShare: (doc,share)->
    # TODO: if is IOS append url and append desc
    opts =
      title: share.title or share.description or 'Shared with RealMaster App'
      message: share.title or share.description or 'RealMaster App Sharing'
      # url: share.url
      #share.image,
    if share.title
      opts.message += ' \n'+share.description
    if share.url
      # only gmail supports a tag
      opts.message += ' URL: '+share.url
    RMSrv.shared share, true
    RMSrv.action {tp:'share',p:opts},(ret)->
      if ret.cancelled
        RMSrv.dialogAlert 'Cancelled'
      else if ret.ok
        RMSrv.shared share
  qrcodeShare: (cmd,url,id)->
    id ?= 'id_share_qrcode'
    if cmd is 'show'
      if dialog = document.getElementById(id)
        dialog.style.display = 'block'
        genQrCode = ->
          # generate qrcode
          holder = document.getElementById id+'_holder'
          holder.innerHTML = ''
          new QRCode(holder, url)
        if QRCode?
          genQrCode()
        else
          # load js if not yet
          po = document.createElement 'script'
          po.type = 'text/javascript'
          po.src = '/js/qrcode/qrcode.min.js'
          document.getElementsByTagName('head')[0].appendChild po
          po.onload = genQrCode
    else
      if dialog = document.getElementById(id)
        dialog.style.display = 'none'
  showSMB: (cmd,prefix='share-')-> # backdrop must be after the main detail content div, with class 'backdrop', style is 'display:none'
    if not RMSrv._shareMask
      RMSrv._shareMask = document.getElementById("backdrop")
      RMSrv._shareMask?.addEventListener 'click', -> RMSrv.showSMB 'hide'
    if cmd is 'show'
      RMSrv._sharePrefix = prefix
      if newParent = document.getElementById prefix + 'placeholder'
        newParent.appendChild document.getElementById 'shareDialog'
      document.body.classList.add 'smb-open'
      RMSrv._shareMask?.style.display = 'block'
      RMSrv.shareLang()
    else if cmd is 'hide'
      document.body.classList.remove 'smb-open'
      RMSrv._shareMask?.style.display = 'none'
  _shareMap: {title:'title',desc:'description',url:'url',image:'image', data:'data',dnurl:'dnurl'} # set default. Some case has no language selection
  _shareLang: null
  shareLang: (lang,doc = window.document)-> # en or not
    lang_en = document.getElementById('id_share_lang_en')
    lang_zh = document.getElementById('id_share_lang_zh')
    lang_kr = document.getElementById('id_share_lang_kr')
    lang_cur = document.getElementById('id_share_lang_cur')

    lang_en?.classList.remove('active')
    lang_cur?.classList.remove('active')
    lang_zh?.classList.remove('active')
    lang_kr?.classList.remove('active')

    langCur = lang_cur?.dataset?.lang
    if (not lang) or (lang is 'cur')
      lang = langCur
      lang_cur?.classList.add('active')
    else if (lang is 'en')
      lang_en?.classList.add('active')
    else if lang in ['zh-cn','zh']
      lang_zh?.classList.add('active')
    else if lang is 'kr'
      lang_kr?.classList.add('active')

    if (lang is 'en')
      RMSrv._shareMap = {"title-en":'title',"desc-en":'description',url:'url',image:'image', data:'data',dnurl:'dnurl'}
    else
      RMSrv._shareMap = {title:'title',desc:'description',url:'url',image:'image', data:'data',dnurl:'dnurl'}

    _shareInfo = RMSrv._getShareInfo doc,RMSrv.getMeta(doc)
    document.getElementById('id_share_title')?.value = _shareInfo.title
    document.getElementById('id_share_desc')?.value = _shareInfo.description
    if lang? and lang isnt 'cur' # only need to specify
      RMSrv._shareLang = lang
    else
      RMSrv._shareLang = null # TODO
  _getShareInfo: (doc,share)->
    _getInfo = (key,name)->
      try
        if tmp = (doc.getElementById(RMSrv._sharePrefix + key) or doc.getElementById('share-' + key) or doc.getElementById('alt-' + key))
          share[name or key] = tmp.value or tmp.textContent
      catch e
        console?.log e
    for k,n of RMSrv._shareMap # {title:'title',desc:'description',url:'url',image:'image'}
      _getInfo k,n
    if not share.image? then share.image = RMSrv.getShareImage doc
    if not share.url? then share.url = doc.URL or window.location.href
    share
  share: (type,doc = window.document,opt)->
    _getShareInfo = (data, cb)->
      done = (d)->
        done = null
        cb d
      _shareInfo = RMSrv._getShareInfo doc,RMSrv.getMeta(doc)
      if title = doc.getElementById('id_share_title')?.value
        _shareInfo.title = title
      if desc = doc.getElementById('id_share_desc')?.value
        _shareInfo.description = desc
      # update url for language
      if RMSrv._shareLang
        if m = _shareInfo.url.match /\?.*(lang\=[a-zA-Z\-]+)/
          _shareInfo.url = _shareInfo.url.replace m[0],(m[0].replace m[1],"lang=#{RMSrv._shareLang}")
        else if /\?[a-z0-9]+\=/i.test _shareInfo.url
          _shareInfo.url += '&lang=' + RMSrv._shareLang
        else
          _shareInfo.url += '?lang=' + RMSrv._shareLang
        if m = _shareInfo.data?.match /.*(lang\=[a-zA-Z\-]+)/
          _shareInfo.data = _shareInfo.data.replace m[0],(m[0].replace m[1],"lang=#{RMSrv._shareLang}")
        # _shareInfo.url
      #POST to mp get share info
      try
        if not _shareInfo.data
          #RMSrv.dialogAlert "Cannot Share! No Data"
          unless _shareInfo.data = document.querySelector('#share-data')?.innerHTML
            return done _shareInfo
        if to
          _shareInfo.data += "&to=#{to}"
        if channel
          _shareInfo.data += "&channel=#{channel}"
        xmlhttp = new XMLHttpRequest()
        xmlhttp.open "POST", '/1.5/api/rm/shareInfo',true #RMSrv.origin() +
        xmlhttp.setRequestHeader "Content-type","application/x-www-form-urlencoded"
        xmlhttp.timeout = 8000
        xmlhttp.ontimeout = ()->
          RMSrv.dialogAlert 'Timeout! Try again Later'
        xmlhttp.onreadystatechange = ()->
          if xmlhttp.readyState is 4
            if xmlhttp.status is 200
              ret = JSON.parse(xmlhttp.responseText)
              console?.log ret.url
              #open link in browser
              unless ret.ok
                if ret.err
                  return RMSrv.dialogAlert ret.err
              else
                _shareInfo.url2 = _shareInfo.url
                _shareInfo.url = ret.url
            return done _shareInfo if done and (channel isnt 'share')
        # alert _shareInfo.data
        xmlhttp.send _shareInfo.data
        return
      catch e
        console?.log e
        # alert e.toString()
        done _shareInfo if done
        return
    # get meta; get title/desc/url/image
    switch type
      when 'show','hide'
        RMSrv.showSMB type
      when 'lang-en'
        RMSrv.shareLang 'en',doc
      when 'lang-cur'
        RMSrv.shareLang 'cur',doc
      when 'lang-zh-cn'
        RMSrv.shareLang 'zh-cn',doc
      when 'lang-kr'
        RMSrv.shareLang 'kr',doc
      when 'qr-code'
        RMSrv.showSMB 'hide'
        _getShareInfo {channel:type}, (sInfo)->
          RMSrv.qrcodeShare 'show',sInfo.url
      when 'qr-code-close'
        RMSrv.qrcodeShare 'hide'
      when 'wechat-friend'
        RMSrv.showSMB 'hide'
        _getShareInfo {channel:type}, (sInfo)->
          #alert sInfo.image if RMSrv.isDBG()
          # alert JSON.stringify sInfo #(Wechat?.Scene.SESSION or WeChat.Scene.session or 0)
          # console.log sInfo
          RMSrv.wechatShare doc,sInfo,0 #(if RMSrv.ver >= '3.1.0' then 0 else WeChat.Scene.session or 0)
      when 'wechat-moment'
        RMSrv.showSMB 'hide'
        _getShareInfo {channel:type}, (sInfo)->
          RMSrv.wechatShare doc,sInfo,1 #(if RMSrv.ver >= '3.1.0' then Wechat?.Scene.TIMELINE else WeChat.Scene.timeline)
      when 'wechat-cust'
        RMSrv.showSMB 'hide'
        _getShareInfo {channel:type}, (sInfo)->
          RMSrv.wechatShare doc,sInfo,opt.tp,opt.type
      when 'facebook-feed'
        RMSrv.showSMB 'hide'
        _getShareInfo {channel:type}, (sInfo)->
          RMSrv.facebookShare doc,sInfo,opt
      else
        RMSrv.showSMB 'hide'
        _getShareInfo {channel:type}, (sInfo)->
          RMSrv.socialShare doc,sInfo
  origin: ->
    window.location.origin or (window.location.protocol + "//" + window.location.hostname + if window.location.port then ':' + window.location.port else '')
  shared: (share, preShare)->
    getECodeFromUrl = (url)->
      if /propDetailPage/.test url
        return url.split('ec=')[1].split('&')[0]
      if /projDetailPage/.test url
        return 'proj:'+url.split('id=')[1].split('&')[0]
      else
        return url.split('?')[0].split('/').pop()
    url = RMSrv.origin() + '/1.5/user/update?act=share'
    if share.dnurl?
      url = share.dnurl
      if /^\//.test share.dnurl
        url = RMSrv.origin() + share.dnurl
    if share.url
      sharedEData = getECodeFromUrl(share.url)
      split = if url.indexOf('?') > 0 then '&' else '?'
      url += split+'data='+sharedEData
      url += '&pre=1' if preShare
    xmlhttp = new XMLHttpRequest()
    xmlhttp.onreadystatechange = ->
      if (xmlhttp.readyState is 4) and (xmlhttp.status is 200)
        try
          #x= close, j = jump, r = rmcall
          ret = JSON.parse(xmlhttp.responseText)
          if ret?.j
            window.location.href = ret.j
          if ret?.r
            window.rmCall(ret.r)
        catch e
    xmlhttp.open "POST",url,true
    #xmlhttp.setRequestHeader "Content-type","application/x-www-form-urlencoded"
    xmlhttp.send()
  sendSMS: (opt)->
    if 'string' is typeof opt
      opt = body:opt,recipients:[opt]
    RMSrv.action {tp:'sendSMS',p:opt}
    # opt = {body:string,recipients:[string],successTypes:['sent','queued']}
  clearCache: -> # nothing to do now
  keyboard: {
    close: -> RMSrv.action "keyboard.dismiss"
    disableScroll: (p) -> RMSrv.action {tp:'disableScroll',p:p}
    isVisible:false
  }
  getKeyboard: (cb)->
    setTimeout (-> cb RMSrv.keyboard),0
  handleKeyboardshow: (p)->
    # alert JSON.stringify p
    RMSrv.keyboard.isVisible = p.isShow
    if p.isShow && window.CustomEvent
      event = new CustomEvent('native.keyboardshow',{detail:{keyboardHeight:p.keyboardHeight}})
      window.dispatchEvent(event)
  # TODO: sync lang from website to local(native)
  pushToken: (pushToken)->
    # alert typeof(pushToken)+':'+JSON.stringify(pushToken)
    return unless pushToken?.token
    pnTp = if pushToken.tp then (pushToken.tp+':') else ''
    if pushToken.os is 'ios'
      pn = pushToken.os+':'+pushToken.token
    else
      pn = pushToken.os+':'+pnTp+pushToken.token
    localStorage.pn = pn
    # send to server
    xmlhttp = new XMLHttpRequest()
    xmlhttp.onreadystatechange = ->
      if xmlhttp.readyState is 4 and xmlhttp.status is 200 then console?.log xmlhttp.responseText
    xmlhttp.open "POST", '/1.5/user/updPn'
    xmlhttp.setRequestHeader "Content-type","application/x-www-form-urlencoded"
    xmlhttp.send "pn="+pn
    RMSrv._RegFinished = true
    if fns = RMSrv._fnWhenReg
      for fn in fns
        try
          fn pn
        catch e
  pushNotice: (notice)-> # TODO: test
    RMSrv.action 'vibrate'
    # alert JSON.stringify notice
    if url = (notice.url or notice.data?.url) #and notice.data?.message
      if /^http/.test url
        parser = document.createElement('a')
        url = parser.pathname+parser.search
      if notice.foreground
        confirmJump (notice.data?.message || notice.message),url
      else
        jumpURL url
    else if msg = (notice.data?.message or notice.message)
      RMSrv.dialogAlert msg
  whenReg: (cb)->
    return cb() if RMSrv._RegFinished
    (RMSrv._fnWhenReg ?= []).push cb
  getTranslate: (m, cb) ->
    url = "https://translate.google.com/#auto/zh-CN/" + encodeURIComponent(m)
    selector = ".text-wrap .translation"
    RMSrv.getPageContent url, selector, {cancelable:true,wait:1000,hide:true,text:"Translating..."}, (ret)->
      return cb ret
  # hide:true means not showing page content.
  getPageContent: (url, selector, opt, cb) -> # TODO
    unless /^(http|https)/.test url
      url = this.appendDomain(url)
    if not cb?
      cb = opt
      opt = {wait:0,hide:true}
    # alert selector+':'+url
    # setTimeout (()->
    RMSrv.onReady(()->
      RMSrv.action {title:opt.title, tp:'pageContent',cancelable:opt.cancelable,url:url,sel:selector,wait:opt.wait,hide:opt.hide,text:opt.text},(ret)->
        # alert ret
        cb ret
    )

jumpURL = (url)->
  url = RMSrv.origin() + "/scheme/jump?u=" + encodeURIComponent url
  RMSrv.coreVer (ver)->
    if ver? and ver > '5.6'
      RMSrv.closeAndRedirectRoot(url)
    else
      window.location = url

confirmJump = (msg,url)->
  fnCfmJump = (idx)->
    if idx is 2
      jumpURL url
  RMSrv.dialogConfirm msg,fnCfmJump,"Message",['Cancel','Open']

handleOpenURL = (url)->
  return unless 'string' is typeof url
  jump = ->
    if (p = url.indexOf('?')) >= 0
      params = url.substr(p)
      window.location = RMSrv.origin() + "/scheme" + params
    else if /^realmaster\:\/\/qrcode\/act/.test(url)
      window.location = RMSrv.origin() + url.substr(12)
    else
      params = ''
      return # it may be a return from WeChat or other app; this case we don't change url
  setTimeout jump,0

window._errorSent = {}
window.onerror = (msg, url, l)->
  m = msg + "\n" + url + "\n" + l
  #alert m
  if /d[0-9]\.realmaster/.test window.location.href
    alert m
  if (window._errorSent[m])
    return
  window._errorSent[m] = 1
  try
    xmlhttp = new XMLHttpRequest()
    xmlhttp.onreadystatechange = ->
      if xmlhttp.readyState is 4 and xmlhttp.status is 200 then console?.log xmlhttp.responseText
    xmlhttp.open "POST", '/cError'
    xmlhttp.setRequestHeader "Content-type","application/x-www-form-urlencoded"
    xmlhttp.send "m="+encodeURIComponent(m)
  catch e
    console?.log e

RMSrv.init()
