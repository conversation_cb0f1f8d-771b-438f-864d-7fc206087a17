photosGallery = null
$(document).ready ->
  $('.detail-photo').on 'click', ->
    if not photosGallery
      photosGallery = new Swiper('#photos-gallery', {
        preloadImages: false, #Disable preloading of all images
        lazy: true, #Enable lazy loading
        slidesPerView: 1,
        centeredSlides: true,
        freeMode: true,
        grabCursor: true,
        observer: true,
        observeParents: true,
        navigation: {
          nextEl: '#photo-gallery-next',
          prevEl: '#photo-gallery-prev',
        },
        keyboard: {
          enabled: true,
          onlyInViewport: false,
        },
      })
    index = $(this).data('index')
    photosGallery.slideTo(index, false,false)
    $('#photos-gallery').show()
  swiper = new Swiper('#detail-photos', {
    slidesPerView: 'auto',
    # preloadImages: false, #Disable preloading of all images
    # lazy: true, #Enable lazy loading
    # centeredSlides: true,
    spaceBetween: 10,
    freeMode: true,
    grabCursor: true,
    navigation: {
      nextEl: '.swiper-button-next',
      prevEl: '.swiper-button-prev',
    },
    # breakpoints: {
    #   1024: {
    #     slidesPerView: 4
    #   },
    #   768: {
    #     slidesPerView: 3
    #   },
    #   640: {
    #     slidesPerView: 1
    #   },
    #   320: {
    #     slidesPerView: 1
    #   }
    # }
  })
