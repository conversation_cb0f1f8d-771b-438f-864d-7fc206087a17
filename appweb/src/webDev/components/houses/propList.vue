<!-- This file is no longer used -->
<template>
  <div id="el"  :class="[rendered ? 'noopcity' : 'opcity-0'  ]">
    <rednavbar :pop-up-login='true' :curmenu="curmenu"></rednavbar>
    <!-- <login></login> -->
    <div class="container">
      <div class="col-sm-4 col-xs-12">
        <form onsubmit="return false;" class="margin-top-10">
          <div class="searchWrapper">
            <input v-model="searchStr" :placeholder="_('ID or Address')" class="full-width">
             <span class="btn icon fa fa-search searchbutton" @click="searchById()" ></span>
          </div>
        </form>
        <searchbox :ltp="vars.ltp" :init-search-place-holder="searchPlaceholder" :ptp-types='dispVar.ptpTypes'></searchbox>
        <div id="downloadArea" class="hidden-xs">
          <div class="textArea text-center">
            <img src="/web/imgs/download.png" @click="goToDownload" width="100%" height="100%">
          </div>
        </div>
      </div>
      <div class="col-sm-8 col-xs-12">
        <h1 style="font-size: 30px;" v-cloak>
          {{_(currentCity)}} {{_(title, 'web')}}
        </h1>
        <div v-for="house in houseList">
          <housecard :ltp="vars.ltp" :prop="house" :disp-var='dispVar' :adrltr.sync="house.adrltr" ></housecard>
        </div>
        <div class="pagenav">
          <!-- <listing-page-nav :page-info="pageInfo" :show-download=true></listing-page-nav> -->
          <listing-page-nav :urlPrefix="urlPrefix" :totalPage="totalPage" :currentPage="currentPage" :disp-var="dispVar"></listing-page-nav>
        </div>
      </div>
      <div id="downloadArea" class="col-xs-12 visible-xs" >
        <div class="textArea text-center">
          <img src="/web/imgs/download.png" @click="goToDownload" width="100%" height="100%">
        </div>
      </div>
    </div>
    <footer-multiline :disp-var="dispVar"></footer-multiline>
  </div>
</template>

<script>
import RedNavbar from '../common/redNavbar.vue';
// import HouseCard from '../houses/houseCard.vue';
import FooterMultiline from '../common/footerMultiline.vue';
// import SearchBox from './searchBox.vue'
// import Login from '../login.vue'
import rmsrv_mixins from '../../../coffee4client/components/rmsrv_mixins'
import ListingPageNav from '../common/pageNav.vue';
import prop_mixins from '../../../coffee4client/components/prop_mixins'

export default{
  mixins: [prop_mixins],
  components: {
    rednavbar: RedNavbar,
    housecard: HouseCard,
    searchbox: SearchBox,
    footerMultiline: FooterMultiline,
    // login:Login,
    listingPageNav: ListingPageNav
  },
  data: {
    rendered: false,
    propTmpFilter:{
      src:'mls',
      saletp:'sale',
      city:'',
      prov:'',
      cmty:'',
      ptype:'Residential',
      ptype2:[],
      bdrms:'',
      bthrms:'',
      gr:'',
      min_lp:null,
      max_lp:null,
      no_mfee:false,
      max_mfee:null,
      sort:'',
      dom:'',
      bsmt:'',
      oh:false
    },
    searchPlaceholder: {
      saletp: 'Sale',
      ptype: "",
      ptype2: [],
      ptpStyle: [],
      bdrms: "",
      bthrms: "",
      gr: "",
      min_lp:"",
      max_lp:"",
      max_mfee:"",
      order:{},
      no_mfee:false,
      prov: 'ON',
      city: 'Toronto'
    },
    ltp:'',
    totalnum: 0,
    pgNum: 0,
    currentPage: 1,
    totalPage: 1,
    lastPagePressed: 1,
    houseList: [],
    ul:[],
    dispVar: {
      isLoggedIn:  false,
      lang: 'en',
      isRealtor: false,
      isVipUser: false,
      isVipRealtor: false,
      isCip: false,
      userCity: {o: "Toronto", n: "Toronto", p: "ON"},
    },
    searchStr:'',
    userDict:{},
    title:'Resale',
    urlPrefix: '',
    curmenu:'resale',
    currentCity: 'Toronto',
    vars:{}
  },
  beforeMount:function() {
    this.parseUserList(this.ul);
    this.initPropListImg();
  },
  mounted: function() {
    var bus = window.bus, self = this;
    this.$getTranslate(this);
    self.totalPage = self.page;
    bus.$on('confirmSearchBox', function(searchParams) {
      for (var key in searchParams){
        self.searchPlaceholder[key] = searchParams[key];
      }
      self.setFiltersFromSearchBox(searchParams);
      self.doSearch({});
    });
    var fields = ['prov','city','min_lp','max_lp','no_mfee','saletp','ptype','max_mfee','ptp','ltp', 'bdrms','bthrms','gr']
    for (let key of fields) {
      var val = this.vars[key]
      if (key =='min_lp' || key == "max_lp" || key =="max_mfee") {
        val = Number(val);
      }
      if (val == "false")
        val = false
      if (val == "true")
        val = true
      if (val)
        self.searchPlaceholder[key] = val
    }
    if (self.vars.sort) {
      self.searchPlaceholder.order = {k:self.vars.sort};
    }
    if (self.vars.ptype2)
      self.searchPlaceholder.ptype2 = self.vars.ptype2;
    self.userDict = {};
    if (self.vars.ltp=='assignment') {
      self.title="Assignment";
      self.curmenu = 'assignment'
      self.propTmpFilter.src = 'rm';
      self.propTmpFilter.ltp = 'assignment';
    }
    if (self.vars.ltp=='exlisting') {
      self.title="Exclusive listings";
      self.curmenu = 'exlisting'
      self.propTmpFilter.src = 'rm';
      self.propTmpFilter.ltp = 'exlisting';
    }
    if (self.vars.page)
      self.currentPage = Number(self.vars.page);
    if (self.vars.saletp)
      self.searchPlaceholder.saletp = self.propTmpFilter.saletp = self.vars.saletp;

    this.urlPrefix = document.location.href;
    window.bus.$emit('set-param', {params:self.searchPlaceholder});
    this.rendered = true;
  },
  methods: {
    clearItems(){
      var self = this;
      self.pgNum = 0;
      self.houseList = [];
    },
    setFiltersFromSearchBox: function(searchParams) {
      for (var key in searchParams){
        if (key!=='order')
          this.propTmpFilter[key] = searchParams[key];
      }

      if (!this.propTmpFilter.saletp)
        this.propTmpFilter.saletp = 'sale';

      if (!this.propTmpFilter.no_mfee)
        this.propTmpFilter.no_mfee = false;

      if (searchParams.order && searchParams.order.k !== '')
          this.propTmpFilter.sort = searchParams.order.k;
      else
        this.propTmpFilter.sort = null;
    },

    picUrl (r) {
      var ret;
      if (this.isRMprop(r.id)) {
        r.pic.ml_num = r.sid || r.ml_num;
        ret = this.convert_rm_imgs(this, r.pic, 'reset');
        return ret[0] || (window.location.origin + "/img/noPic.png");
      } else {
        return listingPicUrls(r, {isCip:this.dispVar.isCip})[0] || '/img/noPic.png';
      }
    },
    isRMprop (id) {
      if (!id) {
        return false;
      }
      return id.substr(0, 3) === 'RM1';
    },
    initPropListImg () {
      for (let prop of this.houseList) {
        if (!prop.thumbUrl) {
          prop.thumbUrl = this.picUrl(prop);
        }
        // insert adrltr for rmprop
        if (prop.id) {
          var user = {};
          if (prop.flwng) {//use followed for this prop
            user = this.userDict[data.uid];
            prop.uid = data.uid;
          } else {
            user = this.userDict[prop.uid];
          }
          prop.adrltr = user;
        }
      }
    },
    parseUserList(ul){
      this.userDict = {};
      if (!ul) {
        ul = [];
      }
      for (let user of ul) {
        this.userDict[user._id] = user;
      }
    },
    searchById() {
      if (!this.searchStr) return;
      var self = this,
          data = {cache:true, id:this.searchStr, p:this.pgNum};
      self.loading = true;
      self.$http.post('/1.5/search/prop/list', data).then(
        function(ret) {
          ret = ret.data;
          self.loading = false;
          if (ret.e) {
            console.error(ret.e);
            self.err = ret.e;
            return;
          }
          self.houseList = ret.resultList;
          self.totalnum = ret.cnt || self.resultList.length;
          self.totalPage = ret.page
          this.searchStr = '';
          self.parseUserList(ret.ul);
          self.initPropListImg();
      });
    },
    doSearch(opt={}){
      var self = this, filter = self.propTmpFilter;
      var str = 'label=1'
      function isValidData(d) {
        if((d === '') || (d == null)) {
          return false;
        }
        if (Array.isArray(d) && d.length == 0) {
          return false;
        }
        return true;
      }

      for (let key in filter) {
        if (filter.hasOwnProperty(key) && isValidData(filter[key])) {
          str += "."+key +'='+ encodeURIComponent(filter[key]);
        }
      }
      if (!self.$http) {
        throw new Error('Vue-resource is required.');
      }
      this.urlPrefix = 'http://' + this.dispVar.reqHost + "/prop/list/" + str;
      if (self.vars.mlsonly) {
        this.urlPrefix = this.urlPrefix + '.mlsonly=1';
      }
      window.location.href= this.urlPrefix;
    },
    goToDownload: function() {
      window.open('/app-download?lang=' + this.dispVar.lang, '_blank');
    }
  }
}

</script>

<style>
.realtor > div{
  text-align: center;
}
.realtor{
  position: absolute;
  right: 0px;
  bottom: 0px;
}
.realtor img{
  width: 70px;
  border-radius: 50%;
}
.realtor .avt{
  width: 70px;
  height: 70px;
  /*position: relative;*/
  border: 1px solid #fff;
}
.realtor .vip{
  width: 18px;
  height: 18px;
  margin-left: -16px;
}
.realtor .nm{
  white-space: nowrap;
  overflow: hidden;
  width: 90px;
  display: inline-block;
  text-align: center;
  font-size: 13px;
  color: #428bca;
  text-overflow: ellipsis;
}
.padding-top-20 {
  padding-top: 20px;
}
#houseList {
  /*position: fixed;*/
  /*border-style: solid;
  border-color: #F0F0F0;*/
  padding: 0;
  font-size: 0.875rem;
  overflow-y:hidden;
  margin:1rem 0 1rem 0;
  /*min-height: 6rem;*/
  background-color: white;
  /*bottom: 0;*/
}

/* #houseList .img {
  width:45%;
  max-width: 20rem;
  max-height: 170px;
  left:0;
  float: left;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  cursor: pointer
} */

@media screen and (max-width: 767px){
  #houseList .img {
    width:100%;
    max-width:100rem;
  }
}


#houseList .card_label {
  position: absolute;
  top:5%;
  font-size: 1.4em;
}


#houseList p{
  color: #a6a6a6;
}

#houseList .card_label2{
  position: relative;
}
#houseList .card-block, #houseList .card-title {
  cursor: pointer
}
#houseList .card_label2 .price{
  color: #FF4242;
  cursor: pointer
}

#houseList .badge {
  position: absolute;
  /*top:0.2rem;*/
  /*left:110%;*/
  background-color: #3C0;
  font-weight: 200;
  margin-left: 0.5rem;

}

#houseList .cardLabels{
  top:0;
  /*margin-left: 10rem;*/
}

#houseList .card_label{
  position: absolute;
  left:0;
  /*display: none;*/
}
</style>
