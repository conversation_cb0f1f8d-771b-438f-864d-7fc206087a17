#!/bin/bash
cd coffee4client
which coffee
coffee -v

rm -rf /tmp/coffee

coffee -o /tmp/coffee -b -c -t *.coffee
coffee -o /tmp/coffee -b -c -t ./coffeeJs/*/*/*.coffee

coffee -o ./ -b -c -t ../libapp/common.coffee
sed 's/exports.//' ./common.js > ./common1.js
mv ./common1.js ./common.js

for file in /tmp/coffee/*.js
do
  echo $file
  minFile=`echo $file | sed "s/.js/.min.js/"`
  terser -c -m < $file > $minFile
done

terser -c -m < common.js >> /tmp/coffee/rmapp.min.js

terser frameManager.js --keep-fnames -c drop_console=true --mangle-props `regex=_` -o /tmp/coffee/frameManager.min.js

cp /tmp/coffee/*.min.js ../webroot/public/js/
rm -rf /tmp/coffee
echo "Copied Minified js"